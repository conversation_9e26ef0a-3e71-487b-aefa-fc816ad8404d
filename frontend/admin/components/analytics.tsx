"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Bar,
  BarChart,
  Pie,
  <PERSON>hart,
  Cell,
} from "recharts"
import { Download, TrendingUp, Users, Trophy, Gift } from "lucide-react"

const engagementData = [
  { month: "Jan", users: 1200, quests: 2400, redemptions: 800 },
  { month: "Feb", users: 1400, quests: 2800, redemptions: 950 },
  { month: "Mar", users: 1600, quests: 3200, redemptions: 1100 },
  { month: "Apr", users: 1800, quests: 3600, redemptions: 1250 },
  { month: "May", users: 2000, quests: 4000, redemptions: 1400 },
  { month: "Jun", users: 2200, quests: 4400, redemptions: 1550 },
]

const topRewards = [
  { name: "10% Discount", redemptions: 450, color: "#8884d8" },
  { name: "Gift Card", redemptions: 320, color: "#82ca9d" },
  { name: "Premium Access", redemptions: 280, color: "#ffc658" },
  { name: "Free Shipping", redemptions: 180, color: "#ff7300" },
  { name: "Exclusive Content", redemptions: 120, color: "#00ff88" },
]

const topBadges = [
  { badge: "First Quest", earned: 1250 },
  { badge: "Social Butterfly", earned: 890 },
  { badge: "Streak Master", earned: 650 },
  { badge: "Explorer", earned: 420 },
  { badge: "Champion", earned: 280 },
]

export function Analytics() {
  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">Track engagement metrics and campaign performance</p>
        </div>
        <div className="flex items-center gap-2">
          <Select defaultValue="30">
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,847</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12%</span> from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Participation Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">68%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+5%</span> from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Badges Earned</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3,490</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+18%</span> from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rewards Redeemed</CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,350</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+8%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Engagement Over Time</CardTitle>
            <CardDescription>User activity and quest completions</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                users: {
                  label: "Active Users",
                  color: "hsl(var(--chart-1))",
                },
                quests: {
                  label: "Quests Completed",
                  color: "hsl(var(--chart-2))",
                },
                redemptions: {
                  label: "Redemptions",
                  color: "hsl(var(--chart-3))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={engagementData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line type="monotone" dataKey="users" stroke="var(--color-users)" strokeWidth={2} />
                  <Line type="monotone" dataKey="quests" stroke="var(--color-quests)" strokeWidth={2} />
                  <Line type="monotone" dataKey="redemptions" stroke="var(--color-redemptions)" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Most Popular Rewards</CardTitle>
            <CardDescription>Top redeemed rewards this month</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                redemptions: {
                  label: "Redemptions",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={topRewards}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="redemptions"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {topRewards.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Earned Badges</CardTitle>
          <CardDescription>Most frequently earned badges this month</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              earned: {
                label: "Times Earned",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={topBadges}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="badge" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="earned" fill="var(--color-earned)" />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  )
}
