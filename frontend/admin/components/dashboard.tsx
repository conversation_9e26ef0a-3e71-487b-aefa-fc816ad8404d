"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/lib/auth-context";
import {
  Calendar,
  Gift,
  Trophy,
  TrendingUp,
  Users,
  Plus,
  Activity,
  Target,
  Database,
  Loader2,
} from "lucide-react";

const kpiData = [
  {
    title: "Active Campaigns",
    value: "12",
    change: "+2 this month",
    icon: Calendar,
    color: "text-blue-600",
  },
  {
    title: "Quests Completed",
    value: "2,847",
    change: "+18% from last month",
    icon: Target,
    color: "text-green-600",
  },
  {
    title: "Points Earned",
    value: "45,231",
    change: "+12% from last month",
    icon: Trophy,
    color: "text-yellow-600",
  },
  {
    title: "Redemption Rate",
    value: "68%",
    change: "+5% from last month",
    icon: TrendingUp,
    color: "text-purple-600",
  },
];

const recentActivity = [
  {
    user: "Sarah Chen",
    action: "completed quest 'Daily Login Streak'",
    points: "+50 points",
    time: "2 minutes ago",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    user: "Mike Johnson",
    action: "redeemed '10% Discount Coupon'",
    points: "-500 points",
    time: "5 minutes ago",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    user: "Emma Davis",
    action: "earned badge 'Social Butterfly'",
    points: "+100 points",
    time: "12 minutes ago",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    user: "Alex Rodriguez",
    action: "completed quest 'Share on Social'",
    points: "+25 points",
    time: "18 minutes ago",
    avatar: "/placeholder.svg?height=32&width=32",
  },
];

export function Dashboard() {
  const { token } = useAuth();
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedingMessage, setSeedingMessage] = useState<string | null>(null);

  const handleSeedData = async () => {
    if (!token) return;

    setIsSeeding(true);
    setSeedingMessage(null);

    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/v1/seeding/seed-sample-data`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to seed data");
      }

      const result = await response.json();
      setSeedingMessage(
        `✅ Successfully created ${result.summary.campaigns_created} campaigns, ${result.summary.quests_created} quests, ${result.summary.badges_created} badges, and ${result.summary.rewards_created} rewards!`
      );
    } catch (error) {
      console.error("Seeding failed:", error);
      setSeedingMessage("❌ Failed to seed data. Please try again.");
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 pt-4 animate-fade-in">
      <div className="grid auto-rows-min gap-4 md:grid-cols-4">
        {kpiData.map((kpi) => (
          <Card
            key={kpi.title}
            className="group hover:shadow-neon-sm transition-all duration-200 hover:scale-[1.02]"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-foreground">
                {kpi.title}
              </CardTitle>
              <div className="p-2 rounded-md bg-primary/10 group-hover:bg-primary/15 transition-colors">
                <kpi.icon className="h-4 w-4 text-primary" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpi.value}</div>
              <p className="text-xs text-muted-foreground mt-1">{kpi.change}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-primary/10">
                <Activity className="h-5 w-5 text-primary" />
              </div>
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest user interactions and achievements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-4">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={activity.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {activity.user
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm">
                      <span className="font-medium">{activity.user}</span>{" "}
                      {activity.action}
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {activity.points}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {activity.time}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <Button
              variant="outline"
              className="w-full mt-4 hover:border-primary/50 hover:text-primary"
            >
              View All Activity
            </Button>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Get started with common tasks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="neon" className="w-full justify-start" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Create Campaign
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start hover:border-primary/50 hover:text-primary"
              size="sm"
            >
              <Gift className="mr-2 h-4 w-4" />
              Add Reward
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start hover:border-primary/50 hover:text-primary"
              size="sm"
            >
              <Users className="mr-2 h-4 w-4" />
              Invite Admin
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start hover:border-primary/50 hover:text-primary"
              size="sm"
            >
              <Trophy className="mr-2 h-4 w-4" />
              Create Badge
            </Button>

            <div className="border-t pt-3 mt-3">
              <Button
                variant="outline"
                className="w-full justify-start hover:border-primary/50 hover:text-primary"
                size="sm"
                onClick={handleSeedData}
                disabled={isSeeding}
              >
                {isSeeding ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                {isSeeding ? "Seeding..." : "Seed Sample Data"}
              </Button>
              {seedingMessage && (
                <p className="text-xs mt-2 text-muted-foreground">
                  {seedingMessage}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="text-primary">
            Welcome to Your Rewards Platform
          </CardTitle>
          <CardDescription>
            Start engaging your customers with milestone-based rewards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                Ready to launch your first campaign? Create engaging quests and
                rewards to drive customer engagement.
              </p>
            </div>
            <Button variant="neon">Start Your First Campaign</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
