"use client";

import type * as React from "react";
import Link from "next/link";
import {
  BarChart3,
  Calendar,
  Gift,
  Home,
  Puzzle,
  Settings,
  Shield,
  Trophy,
  Users,
  Zap,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";

const navigation = [
  {
    title: "Overview",
    items: [{ title: "Dashboard", url: "/", icon: Home }],
  },
  {
    title: "Campaign Management",
    items: [
      { title: "Campaigns", url: "/campaigns", icon: Calendar },
      { title: "Quests", url: "/quests", icon: Puzzle },
      { title: "Badges", url: "/badges", icon: Trophy },
    ],
  },
  {
    title: "Rewards & Users",
    items: [
      { title: "Rewards", url: "/rewards", icon: Gift },
      { title: "Users", url: "/users", icon: Users },
    ],
  },
  {
    title: "Insights & Tools",
    items: [
      { title: "Analytics", url: "/analytics", icon: BarChart3 },
      { title: "Integrations", url: "/integrations", icon: Zap },
    ],
  },
  {
    title: "Administration",
    items: [
      { title: "Access Control", url: "/access-control", icon: Shield },
      { title: "Settings", url: "/settings", icon: Settings },
    ],
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-3 px-4 py-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
            <Trophy className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold text-primary">
              RewardsPlatform
            </span>
            <span className="truncate text-xs text-muted-foreground">
              Admin Console
            </span>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        {navigation.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <Shield />
              <span>Admin Panel</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
