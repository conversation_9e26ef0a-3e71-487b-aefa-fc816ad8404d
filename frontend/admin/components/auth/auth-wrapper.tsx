"use client";

import { useState } from "react";
import { Login } from "./login";
import { Signup } from "./signup";

interface AuthWrapperProps {
  onAuthenticated?: () => void;
  defaultMode?: "login" | "register";
}

export function AuthWrapper({
  onAuthenticated,
  defaultMode = "login",
}: AuthWrapperProps) {
  const [isLogin, setIsLogin] = useState(defaultMode === "login");

  const handleAuthenticated = () => {
    onAuthenticated?.();
  };

  return (
    <>
      {isLogin ? (
        <Login
          onLogin={handleAuthenticated}
          onSwitchToSignup={() => setIsLogin(false)}
        />
      ) : (
        <Signup
          onSignup={handleAuthenticated}
          onSwitchToLogin={() => setIsLogin(true)}
        />
      )}
    </>
  );
}
