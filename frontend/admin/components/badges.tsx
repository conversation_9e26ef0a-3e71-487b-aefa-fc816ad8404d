"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Copy,
  Archive,
  Trophy,
  Star,
  Crown,
  Shield,
  Target,
  Zap,
  Heart,
  Award,
  Upload,
  Download,
  Eye,
  Trash2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BadgeAPI, Badge as BadgeType, BadgeCreate } from "@/lib/api/badges";
import { CategoryAPI, Category } from "@/lib/api/categories";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth-context";

// Static badges data removed - now using API

const badgeIcons = {
  Trophy: Trophy,
  Star: Star,
  Crown: Crown,
  Shield: Shield,
  Target: Target,
  Zap: Zap,
  Heart: Heart,
  Award: Award,
};

const badgeTiers = ["bronze", "silver", "gold", "platinum", "diamond"];
const badgeStatuses = ["ACTIVE", "DRAFT", "PAUSED", "ARCHIVED"];

const getTierColor = (tier: string) => {
  switch (tier) {
    case "Bronze":
      return "bg-orange-100 text-orange-800";
    case "Silver":
      return "bg-gray-100 text-gray-800";
    case "Gold":
      return "bg-yellow-100 text-yellow-800";
    case "Platinum":
      return "bg-purple-100 text-purple-800";
    case "Diamond":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "ACTIVE":
      return "bg-green-100 text-green-800";
    case "DRAFT":
      return "bg-gray-100 text-gray-800";
    case "PAUSED":
      return "bg-yellow-100 text-yellow-800";
    case "ARCHIVED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function Badges() {
  const { toast } = useToast();
  const { token, user } = useAuth();
  const [badges, setBadges] = useState<BadgeType[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState<BadgeType | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("All Categories");
  const [tierFilter, setTierFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentBadge, setCurrentBadge] = useState<BadgeCreate>({
    name: "",
    description: "",
    icon: "Trophy",
    tier: "bronze",
    criteria: "",
    points_reward: 50,
    status: "DRAFT",
  });

  // Load categories and badges on component mount
  useEffect(() => {
    loadCategories();
    loadBadges();
  }, [token, user]);

  const loadCategories = async () => {
    if (!token || !user) return;

    try {
      const categoryData = await CategoryAPI.getCategories(token);
      setCategories(categoryData);
    } catch (error) {
      console.error("Failed to load categories:", error);
    }
  };

  const loadBadges = async () => {
    if (!token || !user) return;

    try {
      setLoading(true);
      const badgesData = await BadgeAPI.getBadges(token, {
        search: searchTerm || undefined,
        tier_filter: tierFilter !== "all" ? tierFilter : undefined,
        status_filter: statusFilter !== "all" ? statusFilter : undefined,
      });
      setBadges(badgesData);
    } catch (error) {
      console.error("Failed to load badges:", error);
      toast({
        title: "Error",
        description: "Failed to load badges",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Reload badges when filters change
  useEffect(() => {
    if (!token || !user) return;

    const timeoutId = setTimeout(() => {
      loadBadges();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, tierFilter, statusFilter, token, user]);

  const filteredBadges = badges.filter((badge) => {
    const matchesCategory = categoryFilter === "All Categories" || true; // Category filtering handled by API
    return matchesCategory;
  });

  const handleCreateBadge = () => {
    setCurrentBadge({
      name: "",
      description: "",
      icon: "Trophy",
      tier: "bronze",
      criteria: "",
      points_reward: 50,
      status: "DRAFT",
    });
    setIsCreateOpen(true);
  };

  const handleViewBadge = (badge: BadgeType) => {
    setSelectedBadge(badge);
    setIsViewOpen(true);
  };

  const handleSaveBadge = async () => {
    if (!token || !user) return;

    try {
      if (!currentBadge.name.trim() || !currentBadge.criteria.trim()) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        return;
      }

      await BadgeAPI.createBadge(token, currentBadge);
      toast({
        title: "Success",
        description: "Badge created successfully",
      });
      setIsCreateOpen(false);
      loadBadges(); // Reload badges
    } catch (error) {
      console.error("Failed to create badge:", error);
      toast({
        title: "Error",
        description: "Failed to create badge",
        variant: "destructive",
      });
    }
  };

  const handleDeleteBadge = async (badgeId: string) => {
    if (!token || !user) return;

    if (
      !confirm(
        "Are you sure you want to delete this badge? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await BadgeAPI.deleteBadge(token, badgeId);
      toast({
        title: "Success",
        description: "Badge deleted successfully",
      });
      loadBadges(); // Reload badges
    } catch (error) {
      console.error("Failed to delete badge:", error);
      toast({
        title: "Error",
        description: "Failed to delete badge",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Badges</h1>
          <p className="text-muted-foreground">
            Create and manage achievement badges for customer recognition
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={handleCreateBadge}>
            <Plus className="mr-2 h-4 w-4" />
            Create Badge
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search badges..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Categories">All Categories</SelectItem>
                {categories
                  .filter(
                    (category) => category.name && category.name.trim() !== ""
                  )
                  .map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      <div className="flex items-center gap-2">
                        {category.icon && <span>{category.icon}</span>}
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <Select value={tierFilter} onValueChange={setTierFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Filter by tier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tiers</SelectItem>
                {badgeTiers.map((tier) => (
                  <SelectItem key={tier} value={tier.toLowerCase()}>
                    {tier}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {badgeStatuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0) + status.slice(1).toLowerCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Badges Grid */}
      <Card>
        <CardHeader>
          <CardTitle>All Badges</CardTitle>
          <CardDescription>
            Manage achievement badges and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading badges...</p>
            </div>
          ) : filteredBadges.length === 0 ? (
            <div className="text-center py-12">
              <Trophy className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No badges found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ||
                categoryFilter !== "All Categories" ||
                tierFilter !== "all" ||
                statusFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "Get started by creating your first badge"}
              </p>
              <Button onClick={handleCreateBadge}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Badge
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredBadges.map((badge) => {
                const IconComponent = badgeIcons[badge.icon] || Trophy;
                return (
                  <Card
                    key={badge.id}
                    className="overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <IconComponent className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{badge.name}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge
                                className={getTierColor(
                                  badge.tier.charAt(0).toUpperCase() +
                                    badge.tier.slice(1)
                                )}
                                variant="secondary"
                              >
                                {badge.tier.charAt(0).toUpperCase() +
                                  badge.tier.slice(1)}
                              </Badge>
                              <Badge
                                className={getStatusColor(badge.status)}
                                variant="secondary"
                              >
                                {badge.status.charAt(0) +
                                  badge.status.slice(1).toLowerCase()}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleViewBadge(badge)}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Archive className="mr-2 h-4 w-4" />
                              Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteBadge(badge.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <p className="text-sm text-muted-foreground mb-4">
                        {badge.description}
                      </p>

                      <div className="space-y-3">
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">
                            Criteria
                          </div>
                          <div className="text-sm font-medium">
                            {badge.criteria}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-xs text-muted-foreground">
                              Points Reward
                            </div>
                            <div className="text-sm font-medium">
                              {badge.points_reward}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xs text-muted-foreground">
                              Times Earned
                            </div>
                            <div className="text-sm font-medium">
                              {badge.times_earned.toLocaleString()}
                            </div>
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-muted-foreground mb-1">
                            Category
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {badge.category?.name || "No Category"}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Badge Modal */}
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Badge</DialogTitle>
            <DialogDescription>
              Design an achievement badge to recognize and reward customer
              accomplishments
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Basic Information</h4>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="badge-name">Badge Name *</Label>
                  <Input
                    id="badge-name"
                    placeholder="e.g., First Steps"
                    value={currentBadge.name}
                    onChange={(e) =>
                      setCurrentBadge((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="badge-description">Description</Label>
                  <Textarea
                    id="badge-description"
                    placeholder="Describe what this badge represents"
                    rows={3}
                    value={currentBadge.description}
                    onChange={(e) =>
                      setCurrentBadge((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="badge-icon">Icon</Label>
                    <Select
                      value={currentBadge.icon}
                      onValueChange={(value) =>
                        setCurrentBadge((prev) => ({ ...prev, icon: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select icon" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(badgeIcons).map((iconName) => {
                          const IconComponent = badgeIcons[iconName];
                          return (
                            <SelectItem key={iconName} value={iconName}>
                              <div className="flex items-center gap-2">
                                <IconComponent className="h-4 w-4" />
                                {iconName}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="badge-tier">Tier</Label>
                    <Select
                      value={currentBadge.tier}
                      onValueChange={(value) =>
                        setCurrentBadge((prev) => ({ ...prev, tier: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select tier" />
                      </SelectTrigger>
                      <SelectContent>
                        {badgeTiers.map((tier) => (
                          <SelectItem key={tier} value={tier}>
                            {tier.charAt(0).toUpperCase() + tier.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="badge-category">Category</Label>
                  <Select
                    value={currentBadge.category_id || "none"}
                    onValueChange={(value) =>
                      setCurrentBadge((prev) => ({
                        ...prev,
                        category_id: value === "none" ? undefined : value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Category</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            {category.icon && <span>{category.icon}</span>}
                            {category.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            {/* Badge Configuration */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Badge Configuration</h4>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="badge-criteria">Earning Criteria *</Label>
                  <Textarea
                    id="badge-criteria"
                    placeholder="e.g., Complete 5 quests in the Social category"
                    rows={2}
                    value={currentBadge.criteria}
                    onChange={(e) =>
                      setCurrentBadge((prev) => ({
                        ...prev,
                        criteria: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="badge-points">Points Reward</Label>
                    <Input
                      id="badge-points"
                      type="number"
                      min="0"
                      value={currentBadge.points_reward}
                      onChange={(e) =>
                        setCurrentBadge((prev) => ({
                          ...prev,
                          points_reward: Number.parseInt(e.target.value) || 0,
                        }))
                      }
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="badge-status">Status</Label>
                    <Select
                      value={currentBadge.status}
                      onValueChange={(value) =>
                        setCurrentBadge((prev) => ({
                          ...prev,
                          status: value,
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {badgeStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status.charAt(0) + status.slice(1).toLowerCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Preview */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Preview</h4>
              <div className="p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    {(() => {
                      const IconComponent =
                        badgeIcons[currentBadge.icon] || Trophy;
                      return <IconComponent className="h-6 w-6 text-primary" />;
                    })()}
                  </div>
                  <div>
                    <h5 className="font-medium">
                      {currentBadge.name || "Badge Name"}
                    </h5>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge
                        className={getTierColor(
                          currentBadge.tier.charAt(0).toUpperCase() +
                            currentBadge.tier.slice(1)
                        )}
                        variant="secondary"
                      >
                        {currentBadge.tier.charAt(0).toUpperCase() +
                          currentBadge.tier.slice(1)}
                      </Badge>
                      <Badge
                        className={getStatusColor(currentBadge.status)}
                        variant="secondary"
                      >
                        {currentBadge.status.charAt(0) +
                          currentBadge.status.slice(1).toLowerCase()}
                      </Badge>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {currentBadge.description ||
                    "Badge description will appear here"}
                </p>
                <div className="text-xs text-muted-foreground">
                  <div>
                    Criteria:{" "}
                    {currentBadge.criteria || "Badge criteria will appear here"}
                  </div>
                  <div>Points Reward: {currentBadge.points_reward}</div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveBadge}
              disabled={
                !currentBadge.name.trim() || !currentBadge.criteria.trim()
              }
            >
              Create Badge
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Badge Modal */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          {selectedBadge && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    {(() => {
                      const IconComponent =
                        badgeIcons[selectedBadge.icon] || Trophy;
                      return <IconComponent className="h-5 w-5 text-primary" />;
                    })()}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      {selectedBadge.name}
                      <Badge className={getStatusColor(selectedBadge.status)}>
                        {selectedBadge.status.charAt(0) +
                          selectedBadge.status.slice(1).toLowerCase()}
                      </Badge>
                    </div>
                    <Badge
                      className={getTierColor(
                        selectedBadge.tier.charAt(0).toUpperCase() +
                          selectedBadge.tier.slice(1)
                      )}
                      variant="secondary"
                    >
                      {selectedBadge.tier.charAt(0).toUpperCase() +
                        selectedBadge.tier.slice(1)}
                    </Badge>
                  </div>
                </DialogTitle>
                <DialogDescription>
                  {selectedBadge.description}
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Category</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Badge variant="outline">
                          {selectedBadge.category?.name || "No Category"}
                        </Badge>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Points Reward</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {selectedBadge.points_reward}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">
                        Earning Criteria
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">{selectedBadge.criteria}</p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="performance" className="space-y-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Times Earned</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {selectedBadge.times_earned.toLocaleString()}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Badge Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            Created:
                          </span>
                          <span>
                            {new Date(
                              selectedBadge.created_at
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="campaigns" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">
                        Associated Campaigns
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-4">
                        <p className="text-muted-foreground">
                          Campaign associations will be available in a future
                          update.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
