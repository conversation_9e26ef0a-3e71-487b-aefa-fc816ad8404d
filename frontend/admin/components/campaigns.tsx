"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import {
  campaignApi,
  questApi,
  categoryApi,
  audienceApi,
  authApi,
  type Campaign,
  type Quest,
  type Category,
  type CampaignCreate,
  type QuestCreate,
} from "@/lib/api";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Copy,
  Archive,
  Calendar,
  Users,
  Play,
  Pause,
  Eye,
  Upload,
  Target,
  Trophy,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

// Mock data for display purposes - will be replaced with API data
const mockCampaignStats = {
  progress: 65,
  participants: 1247,
  questsCompleted: 3420,
  pointsAwarded: 85600,
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "active":
      return "bg-green-100 text-green-800";
    case "draft":
      return "bg-gray-100 text-gray-800";
    case "ended":
      return "bg-blue-100 text-blue-800";
    case "paused":
      return "bg-yellow-100 text-yellow-800";
    case "archived":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusLabel = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1);
};

export function Campaigns() {
  const { token, user } = useAuth();

  // UI State
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(
    null
  );
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedQuests, setSelectedQuests] = useState<string[]>([]);
  const [selectedAudience, setSelectedAudience] = useState<string[]>([]);

  // API Data State
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [quests, setQuests] = useState<Quest[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [audienceSegments, setAudienceSegments] = useState<string[]>([]);

  // Loading and Error State
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form State for Campaign Creation
  const [campaignForm, setCampaignForm] = useState({
    name: "",
    description: "",
    banner_url: "",
    start_date: "",
    end_date: "",
    target_audience: {} as Record<string, any>,
  });

  const [isQuestBuilderOpen, setIsQuestBuilderOpen] = useState(false);
  const [currentQuest, setCurrentQuest] = useState({
    title: "",
    description: "",
    category: "",
    points: 100,
    frequency: "one-time",
    validation: "automatic",
    requirements: [],
    rewards: [],
    isActive: true,
  });
  // Quest categories will come from the API
  const questCategories = categories.map((cat) => cat.name);
  const [validationTypes] = useState([
    {
      value: "automatic",
      label: "Automatic",
      description: "Validated by system integration",
    },
    {
      value: "manual",
      label: "Manual Review",
      description: "Requires moderator approval",
    },
    {
      value: "code",
      label: "Code Entry",
      description: "User enters a verification code",
    },
    {
      value: "upload",
      label: "File Upload",
      description: "User uploads proof/screenshot",
    },
  ]);
  const [frequencyTypes] = useState([
    {
      value: "one_time",
      label: "One-time",
      description: "Can only be completed once",
    },
    {
      value: "daily",
      label: "Daily",
      description: "Can be completed once per day",
    },
    {
      value: "weekly",
      label: "Weekly",
      description: "Can be completed once per week",
    },
    {
      value: "monthly",
      label: "Monthly",
      description: "Can be completed once per month",
    },
    {
      value: "unlimited",
      label: "Unlimited",
      description: "Can be completed multiple times",
    },
  ]);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      if (!token) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load campaigns, quests, categories, and audience segments in parallel
        const [campaignsData, questsData, categoriesData, audienceData] =
          await Promise.all([
            campaignApi.getCampaigns(token),
            questApi.getQuests(token),
            categoryApi.getCategories(token),
            audienceApi.getAudienceSegments(token),
          ]);

        setCampaigns(campaignsData);
        // For now, start with empty quests since existing quests are tied to campaigns
        // Users will create new quests during campaign creation
        setQuests([]);

        // If no categories exist, seed default ones
        if (categoriesData.length === 0) {
          try {
            const seededCategories = await categoryApi.seedDefaultCategories(
              token
            );
            setCategories(seededCategories);
          } catch (err) {
            console.warn("Failed to seed default categories:", err);
            setCategories(categoriesData);
          }
        } else {
          setCategories(categoriesData);
        }

        setAudienceSegments(audienceData);
      } catch (err: any) {
        console.error("Failed to load data:", err);

        // If it's a permission error, try to seed default role
        if (err.detail && err.detail.includes("Missing required permissions")) {
          try {
            console.log("Attempting to seed default admin role...");
            await authApi.seedDefaultRole(token);
            console.log("Default role seeded, retrying data load...");

            // Retry loading data
            const [campaignsData, questsData, categoriesData, audienceData] =
              await Promise.all([
                campaignApi.getCampaigns(token),
                questApi.getQuests(token),
                categoryApi.getCategories(token),
                audienceApi.getAudienceSegments(token),
              ]);

            setCampaigns(campaignsData);
            // Start with empty quests - users will create new ones during campaign creation
            setQuests([]);

            if (categoriesData.length === 0) {
              try {
                const seededCategories =
                  await categoryApi.seedDefaultCategories(token);
                setCategories(seededCategories);
              } catch (err) {
                console.warn("Failed to seed default categories:", err);
                setCategories(categoriesData);
              }
            } else {
              setCategories(categoriesData);
            }

            setAudienceSegments(audienceData);
            setError(null); // Clear error if retry succeeds
          } catch (retryErr: any) {
            console.error("Failed to seed default role or retry:", retryErr);
            setError(
              "Permission denied. Please contact your administrator to set up proper roles and permissions."
            );
          }
        } else {
          setError(err.detail || "Failed to load data");
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [token]);

  const filteredCampaigns = campaigns.filter((campaign) => {
    const matchesSearch = campaign.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || campaign.status.toLowerCase() === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleCreateCampaign = () => {
    setCurrentStep(1);
    setSelectedQuests([]);
    setSelectedAudience([]);
    setCampaignForm({
      name: "",
      description: "",
      banner_url: "",
      start_date: "",
      end_date: "",
      target_audience: {},
    });
    setIsCreateOpen(true);
  };

  const handleFinalizeCampaign = async () => {
    if (!token || !user) return;

    try {
      setIsCreating(true);
      setError(null);

      // Prepare campaign data
      const campaignData: CampaignCreate = {
        name: campaignForm.name,
        description: campaignForm.description,
        banner_url: campaignForm.banner_url,
        start_date: campaignForm.start_date || undefined,
        end_date: campaignForm.end_date || undefined,
        target_audience: {
          segments: selectedAudience,
        },
        organization_id: user.organization_id || "default-org-id", // TODO: Get from user context or API
      };

      // Create the campaign
      const newCampaign = await campaignApi.createCampaign(token, campaignData);

      // Create associated quests
      const createdQuests = [];
      console.log(
        `Creating ${selectedQuests.length} quests for campaign "${newCampaign.name}"`
      );
      console.log("Selected quest IDs:", selectedQuests);
      console.log(
        "Available quests in state:",
        quests.map((q) => ({ id: q.id, title: q.title }))
      );

      for (const questId of selectedQuests) {
        const quest = quests.find((q) => q.id === questId);
        if (quest) {
          try {
            console.log(
              `Creating quest: "${quest.title}" with frequency: ${quest.frequency}, validation: ${quest.validation_type}`
            );

            // Only create quests that are temporary (newly created in UI)
            // Skip existing quests that are already in the database
            if (quest.id.startsWith("temp-")) {
              // Validate required fields
              if (!quest.title || !quest.title.trim()) {
                console.error(`Skipping quest with empty title`);
                continue;
              }

              // Apply enum mapping to ensure correct backend values
              const frequencyMapping: Record<string, string> = {
                "one-time": "one_time",
                daily: "daily",
                weekly: "weekly",
                monthly: "monthly",
                unlimited: "unlimited",
              };

              const validationMapping: Record<string, string> = {
                automatic: "automatic",
                manual: "manual",
                code: "code",
                upload: "upload",
              };

              const questData: QuestCreate = {
                title: quest.title.trim(),
                description: quest.description?.trim() || "",
                points_reward: quest.points_reward || 100,
                frequency: (frequencyMapping[quest.frequency] ||
                  quest.frequency) as any,
                validation_type: (validationMapping[quest.validation_type] ||
                  quest.validation_type) as any,
                validation_criteria: quest.validation_criteria || undefined,
                campaign_id: newCampaign.id,
                category_id: quest.category_id || undefined,
              };

              console.log("🔄 Enum mapping applied:", {
                originalFrequency: quest.frequency,
                mappedFrequency: questData.frequency,
                originalValidation: quest.validation_type,
                mappedValidation: questData.validation_type,
              });

              console.log(
                "Quest data being sent to API:",
                JSON.stringify(questData, null, 2)
              );

              // Validate that required enums are properly set
              if (!questData.frequency || !questData.validation_type) {
                console.error(
                  `Quest "${quest.title}" has invalid frequency or validation_type:`,
                  {
                    frequency: questData.frequency,
                    validation_type: questData.validation_type,
                  }
                );
                continue;
              }

              const createdQuest = await questApi.createQuest(token, questData);
              createdQuests.push(createdQuest);
              console.log(
                `✅ Successfully created quest: "${createdQuest.title}" (ID: ${createdQuest.id})`
              );
            } else {
              // Existing quests are already tied to other campaigns (campaign_id is required)
              // We can't reuse them for new campaigns
              console.log(
                `Skipping existing quest "${quest.title}" - already tied to another campaign`
              );
            }
          } catch (questErr: any) {
            console.error(
              `❌ Failed to create quest "${quest.title}":`,
              questErr
            );
            // Continue with other quests even if one fails
          }
        } else {
          console.warn(
            `Quest with ID ${questId} not found in local quests array`
          );
        }
      }

      console.log(`Campaign created with ${createdQuests.length} quests`);

      // Show success message
      if (createdQuests.length > 0) {
        console.log(
          `✅ Successfully created campaign "${newCampaign.name}" with ${createdQuests.length} quest(s)`
        );
      } else {
        console.log(
          `✅ Successfully created campaign "${newCampaign.name}" (no quests added)`
        );
      }

      // Refresh campaigns list
      const updatedCampaigns = await campaignApi.getCampaigns(token);
      setCampaigns(updatedCampaigns);

      // Close modal and reset form
      setIsCreateOpen(false);
      setCurrentStep(1);
      setSelectedQuests([]);
      setSelectedAudience([]);
      setCampaignForm({
        name: "",
        description: "",
        banner_url: "",
        start_date: "",
        end_date: "",
        target_audience: {},
      });
    } catch (err: any) {
      console.error("Failed to create campaign:", err);
      setError(err.detail || "Failed to create campaign");
    } finally {
      setIsCreating(false);
    }
  };

  const handleViewCampaign = (campaign) => {
    setSelectedCampaign(campaign);
    setIsViewOpen(true);
  };

  const nextStep = () => setCurrentStep((prev) => Math.min(prev + 1, 4));
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 1));

  const toggleQuestSelection = (questId) => {
    setSelectedQuests((prev) =>
      prev.includes(questId)
        ? prev.filter((id) => id !== questId)
        : [...prev, questId]
    );
  };

  const toggleAudienceSelection = (segment) => {
    setSelectedAudience((prev) =>
      prev.includes(segment)
        ? prev.filter((s) => s !== segment)
        : [...prev, segment]
    );
  };

  const handleCreateNewQuest = () => {
    setCurrentQuest({
      title: "",
      description: "",
      category: "",
      points: 100,
      frequency: "one-time",
      validation: "automatic",
      requirements: [],
      rewards: [],
      isActive: true,
    });
    setIsQuestBuilderOpen(true);
  };

  const handleSaveQuest = async () => {
    if (!token || !currentQuest.title.trim()) return;

    try {
      // Map frequency values to match backend enum (lowercase values)
      const frequencyMapping: Record<string, string> = {
        "one-time": "one_time",
        daily: "daily",
        weekly: "weekly",
        monthly: "monthly",
        unlimited: "unlimited",
      };

      // Map validation types to match backend enum (lowercase values)
      const validationMapping: Record<string, string> = {
        automatic: "automatic",
        manual: "manual",
        code: "code",
        upload: "upload",
      };

      // Create a temporary quest that can be used in campaign creation
      const newQuest: Quest = {
        id: `temp-${Date.now()}`,
        title: currentQuest.title,
        description: currentQuest.description || "",
        points_reward: currentQuest.points,
        frequency: (frequencyMapping[currentQuest.frequency] ||
          "one_time") as any,
        validation_type: (validationMapping[currentQuest.validation] ||
          "automatic") as any,
        validation_criteria:
          currentQuest.requirements.length > 0
            ? { requirements: currentQuest.requirements }
            : undefined,
        status: currentQuest.isActive ? "active" : "draft",
        campaign_id: "", // Will be set when campaign is created
        category_id: currentQuest.category
          ? categories.find(
              (c) =>
                c.name.toLowerCase() === currentQuest.category.toLowerCase()
            )?.id
          : undefined,
        created_by: user?.id || "",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add to local quests state for selection
      setQuests((prev) => [...prev, newQuest]);
      setIsQuestBuilderOpen(false);

      // Auto-select the newly created quest
      setSelectedQuests((prev) => [...prev, newQuest.id]);

      console.log("✅ Created new quest for campaign:", {
        title: newQuest.title,
        frequency: newQuest.frequency,
        validation_type: newQuest.validation_type,
        points_reward: newQuest.points_reward,
        category_id: newQuest.category_id,
      });

      console.log("✅ Enum values being used:", {
        originalFrequency: currentQuest.frequency,
        mappedFrequency: newQuest.frequency,
        originalValidation: currentQuest.validation,
        mappedValidation: newQuest.validation_type,
      });

      // Reset quest form
      setCurrentQuest({
        title: "",
        description: "",
        category: "",
        points: 100,
        frequency: "one-time",
        validation: "automatic",
        requirements: [],
        rewards: [],
        isActive: true,
      });
    } catch (err: any) {
      console.error("Failed to save quest:", err);
      setError(err.detail || "Failed to save quest");
    }
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(selectedQuests);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSelectedQuests(items);
  };

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
            <p className="text-muted-foreground">
              Create and manage your customer engagement campaigns
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading campaigns...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  className="bg-red-100 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  onClick={() => setError(null)}
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
          <p className="text-muted-foreground">
            Create and manage your customer engagement campaigns
          </p>
        </div>
        <Button onClick={handleCreateCampaign}>
          <Plus className="mr-2 h-4 w-4" />
          Create Campaign
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search campaigns..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="ended">Ended</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Campaigns</CardTitle>
          <CardDescription>
            Manage your customer engagement campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredCampaigns.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No campaigns found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "Get started by creating your first campaign"}
              </p>
              <Button onClick={handleCreateCampaign}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Campaign
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Campaign</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Participants</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCampaigns.map((campaign) => (
                  <TableRow
                    key={campaign.id}
                    className="cursor-pointer hover:bg-muted/50"
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-8 bg-muted rounded flex items-center justify-center">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div>
                          <div className="font-medium">{campaign.name}</div>
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {campaign.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(campaign.status)}>
                        {getStatusLabel(campaign.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>
                          {campaign.start_date
                            ? new Date(campaign.start_date).toLocaleDateString()
                            : "Not set"}
                        </div>
                        <div className="text-muted-foreground">
                          {campaign.end_date
                            ? new Date(campaign.end_date).toLocaleDateString()
                            : "Not set"}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Progress
                          value={mockCampaignStats.progress}
                          className="w-16"
                        />
                        <span className="text-xs text-muted-foreground">
                          {mockCampaignStats.progress}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        {mockCampaignStats.participants.toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div className="flex items-center gap-1">
                          <Target className="h-3 w-3 text-muted-foreground" />
                          {mockCampaignStats.questsCompleted} quests
                        </div>
                        <div className="flex items-center gap-1">
                          <Trophy className="h-3 w-3 text-muted-foreground" />
                          {mockCampaignStats.pointsAwarded.toLocaleString()}{" "}
                          points
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewCampaign(campaign)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          {campaign.status === "active" ? (
                            <DropdownMenuItem>
                              <Pause className="mr-2 h-4 w-4" />
                              Pause
                            </DropdownMenuItem>
                          ) : campaign.status === "paused" ? (
                            <DropdownMenuItem>
                              <Play className="mr-2 h-4 w-4" />
                              Resume
                            </DropdownMenuItem>
                          ) : null}
                          <DropdownMenuItem>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Campaign Modal */}
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Campaign</DialogTitle>
            <DialogDescription>
              Step {currentStep} of 4: Set up a new engagement campaign for your
              customers
            </DialogDescription>
          </DialogHeader>

          {/* Progress Indicator */}
          <div className="flex items-center justify-between mb-6">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step <= currentStep
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  }`}
                >
                  {step}
                </div>
                {step < 4 && (
                  <div
                    className={`w-12 h-0.5 mx-2 ${
                      step < currentStep ? "bg-primary" : "bg-muted"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Step Content */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">General Information</h3>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Campaign Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter campaign name"
                    value={campaignForm.name}
                    onChange={(e) =>
                      setCampaignForm((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your campaign goals and activities"
                    rows={3}
                    value={campaignForm.description}
                    onChange={(e) =>
                      setCampaignForm((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="banner">Campaign Banner</Label>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Drag and drop an image, or{" "}
                      <Button variant="link" className="p-0 h-auto">
                        browse
                      </Button>
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      PNG, JPG up to 2MB
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="start-date">Start Date *</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={campaignForm.start_date}
                      onChange={(e) =>
                        setCampaignForm((prev) => ({
                          ...prev,
                          start_date: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="end-date">End Date *</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={campaignForm.end_date}
                      onChange={(e) =>
                        setCampaignForm((prev) => ({
                          ...prev,
                          end_date: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Quests</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCreateNewQuest}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Quest
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Create new quests for this campaign. Each quest will be
                specifically designed for this campaign's goals.
              </p>

              {/* Available Quests */}
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {quests.length === 0 ? (
                  <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <Target className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground mb-3">
                      No quests created yet for this campaign
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Click "Create New Quest" to add your first quest
                    </p>
                  </div>
                ) : (
                  quests.map((quest) => (
                    <div
                      key={quest.id}
                      className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <Checkbox
                        checked={selectedQuests.includes(quest.id)}
                        onCheckedChange={() => toggleQuestSelection(quest.id)}
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{quest.title}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {categories.find(
                                (c) => c.id === quest.category_id
                              )?.name || "Uncategorized"}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {quest.points_reward} pts
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {quest.description}
                        </p>
                        <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                          <span>
                            Frequency: {quest.frequency.replace("_", " ")}
                          </span>
                          <span>Validation: {quest.validation_type}</span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Selected Quests with Drag & Drop */}
              {selectedQuests.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium mb-3">
                    Selected Quests ({selectedQuests.length})
                  </h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Drag to reorder the quest sequence
                  </p>
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="selected-quests">
                      {(provided) => (
                        <div
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                          className="space-y-2"
                        >
                          {selectedQuests.map((questId, index) => {
                            const quest = quests.find((q) => q.id === questId);
                            if (!quest) return null;

                            return (
                              <Draggable
                                key={questId}
                                draggableId={questId.toString()}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <div
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className={`p-3 border rounded-lg bg-background ${
                                      snapshot.isDragging ? "shadow-lg" : ""
                                    }`}
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-medium">
                                        {index + 1}
                                      </div>
                                      <div className="flex-1">
                                        <div className="flex items-center justify-between">
                                          <h5 className="font-medium">
                                            {quest.title}
                                          </h5>
                                          <div className="flex items-center gap-2">
                                            <Badge
                                              variant="secondary"
                                              className="text-xs"
                                            >
                                              {categories.find(
                                                (c) =>
                                                  c.id === quest.category_id
                                              )?.name || "Uncategorized"}
                                            </Badge>
                                            <span className="text-sm text-muted-foreground">
                                              {quest.points_reward} pts
                                            </span>
                                          </div>
                                        </div>
                                        <p className="text-sm text-muted-foreground">
                                          {quest.description}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </Draggable>
                            );
                          })}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </div>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Target Audience</h3>
              <p className="text-sm text-muted-foreground">
                Select the audience segments for this campaign. You can target
                multiple segments.
              </p>
              <div className="grid grid-cols-2 gap-3">
                {audienceSegments.map((segment) => (
                  <div
                    key={segment}
                    className="flex items-center space-x-3 p-3 border rounded-lg"
                  >
                    <Checkbox
                      checked={selectedAudience.includes(segment)}
                      onCheckedChange={() => toggleAudienceSelection(segment)}
                    />
                    <div className="flex-1">
                      <span className="font-medium">{segment}</span>
                    </div>
                  </div>
                ))}
              </div>
              {selectedAudience.length > 0 && (
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <p className="text-sm font-medium">
                    Selected Segments: {selectedAudience.length}
                  </p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedAudience.map((segment) => (
                      <Badge key={segment} variant="secondary">
                        {segment}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Review & Confirm</h3>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Campaign Overview</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Name:</span>
                      <p className="font-medium">
                        {campaignForm.name || "Untitled Campaign"}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">
                        {campaignForm.start_date && campaignForm.end_date
                          ? `${new Date(
                              campaignForm.start_date
                            ).toLocaleDateString()} - ${new Date(
                              campaignForm.end_date
                            ).toLocaleDateString()}`
                          : "Not specified"}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Quests:</span>
                      <p className="font-medium">
                        {selectedQuests.length} selected
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Audience:</span>
                      <p className="font-medium">
                        {selectedAudience.length} segments
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Ready to Launch?
                  </h4>
                  <p className="text-sm text-blue-700">
                    Your campaign will be created in "Draft" status. You can
                    activate it anytime from the campaigns list.
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <Button variant="outline" onClick={prevStep}>
                  Previous
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsCreateOpen(false)}>
                Cancel
              </Button>
              {currentStep < 4 ? (
                <Button onClick={nextStep} disabled={isCreating}>
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleFinalizeCampaign}
                  disabled={isCreating || !campaignForm.name.trim()}
                >
                  {isCreating ? "Creating..." : "Create Campaign"}
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>

        {/* Quest Builder Modal */}
        <Dialog open={isQuestBuilderOpen} onOpenChange={setIsQuestBuilderOpen}>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Quest</DialogTitle>
              <DialogDescription>
                Design a quest to engage your customers and drive specific
                behaviors
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-base">Basic Information</h4>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="quest-title">Quest Title *</Label>
                    <Input
                      id="quest-title"
                      placeholder="e.g., Complete Your Profile"
                      value={currentQuest.title}
                      onChange={(e) =>
                        setCurrentQuest((prev) => ({
                          ...prev,
                          title: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="quest-description">Description</Label>
                    <Textarea
                      id="quest-description"
                      placeholder="Describe what users need to do to complete this quest"
                      rows={3}
                      value={currentQuest.description}
                      onChange={(e) =>
                        setCurrentQuest((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="quest-category">Category</Label>
                      <Select
                        value={currentQuest.category}
                        onValueChange={(value) =>
                          setCurrentQuest((prev) => ({
                            ...prev,
                            category: value,
                          }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories
                            .filter(
                              (category) =>
                                category.name && category.name.trim() !== ""
                            )
                            .map((category) => (
                              <SelectItem
                                key={category.id}
                                value={category.name.toLowerCase()}
                              >
                                {category.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="quest-points">Points Reward</Label>
                      <Input
                        id="quest-points"
                        type="number"
                        min="1"
                        value={currentQuest.points}
                        onChange={(e) =>
                          setCurrentQuest((prev) => ({
                            ...prev,
                            points: Number.parseInt(e.target.value) || 0,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Quest Configuration */}
              <div className="space-y-4">
                <h4 className="font-semibold text-base">Quest Configuration</h4>

                {/* Frequency */}
                <div className="space-y-3">
                  <Label>Completion Frequency</Label>
                  <RadioGroup
                    value={currentQuest.frequency}
                    onValueChange={(value) =>
                      setCurrentQuest((prev) => ({ ...prev, frequency: value }))
                    }
                    className="grid grid-cols-1 gap-3"
                  >
                    {frequencyTypes.map((type) => (
                      <div
                        key={type.value}
                        className="flex items-center space-x-3 p-3 border rounded-lg"
                      >
                        <RadioGroupItem value={type.value} id={type.value} />
                        <div className="flex-1">
                          <Label
                            htmlFor={type.value}
                            className="font-medium cursor-pointer"
                          >
                            {type.label}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {type.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <Separator />

                {/* Validation Method */}
                <div className="space-y-3">
                  <Label>Validation Method</Label>
                  <RadioGroup
                    value={currentQuest.validation}
                    onValueChange={(value) =>
                      setCurrentQuest((prev) => ({
                        ...prev,
                        validation: value,
                      }))
                    }
                    className="grid grid-cols-1 gap-3"
                  >
                    {validationTypes.map((type) => (
                      <div
                        key={type.value}
                        className="flex items-center space-x-3 p-3 border rounded-lg"
                      >
                        <RadioGroupItem
                          value={type.value}
                          id={`validation-${type.value}`}
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor={`validation-${type.value}`}
                            className="font-medium cursor-pointer"
                          >
                            {type.label}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {type.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              </div>

              <Separator />

              {/* Quest Requirements */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-base">
                    Requirements & Rules
                  </h4>
                  <Button variant="outline" size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Requirement
                  </Button>
                </div>
                <div className="p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                  <Target className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No requirements added yet. Click "Add Requirement" to set
                    specific conditions.
                  </p>
                </div>
              </div>

              <Separator />

              {/* Additional Settings */}
              <div className="space-y-4">
                <h4 className="font-semibold text-base">Additional Settings</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="quest-active">Active Quest</Label>
                      <p className="text-sm text-muted-foreground">
                        Quest will be available to users immediately
                      </p>
                    </div>
                    <Switch
                      id="quest-active"
                      checked={currentQuest.isActive}
                      onCheckedChange={(checked) =>
                        setCurrentQuest((prev) => ({
                          ...prev,
                          isActive: checked,
                        }))
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Preview */}
              <div className="space-y-4">
                <h4 className="font-semibold text-base">Preview</h4>
                <div className="p-4 border rounded-lg bg-muted/50">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium">
                      {currentQuest.title || "Quest Title"}
                    </h5>
                    <div className="flex items-center gap-2">
                      {currentQuest.category && (
                        <Badge variant="secondary">
                          {currentQuest.category}
                        </Badge>
                      )}
                      <span className="text-sm text-muted-foreground">
                        {currentQuest.points} pts
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    {currentQuest.description ||
                      "Quest description will appear here"}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>
                      Frequency:{" "}
                      {
                        frequencyTypes.find(
                          (f) => f.value === currentQuest.frequency
                        )?.label
                      }
                    </span>
                    <span>
                      Validation:{" "}
                      {
                        validationTypes.find(
                          (v) => v.value === currentQuest.validation
                        )?.label
                      }
                    </span>
                    <span>
                      Status: {currentQuest.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsQuestBuilderOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveQuest}
                disabled={!currentQuest.title.trim()}
              >
                Save Quest
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Dialog>

      {/* View Campaign Modal */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          {selectedCampaign && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {selectedCampaign.name}
                  <Badge className={getStatusColor(selectedCampaign.status)}>
                    {selectedCampaign.status}
                  </Badge>
                </DialogTitle>
                <DialogDescription>
                  {selectedCampaign.description}
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="quests">Quests</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Duration</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm">
                          <div>{selectedCampaign.startDate}</div>
                          <div className="text-muted-foreground">
                            {selectedCampaign.endDate}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Progress</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <Progress value={selectedCampaign.progress} />
                          <span className="text-sm text-muted-foreground">
                            {selectedCampaign.progress}%
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Participants
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {selectedCampaign.participants.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Quests Completed
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {selectedCampaign.questsCompleted.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Trophy className="h-4 w-4" />
                          Points Awarded
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {selectedCampaign.pointsAwarded.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Target Audience</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {selectedCampaign.audience.map((segment) => (
                          <Badge key={segment} variant="secondary">
                            {segment}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="quests" className="space-y-4">
                  <div className="space-y-3">
                    {sampleQuests.slice(0, 2).map((quest, index) => (
                      <Card key={quest.id}>
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                                {index + 1}
                              </div>
                              <div>
                                <h4 className="font-medium">{quest.title}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {quest.description}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium">
                                {quest.points} points
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                {quest.category}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="analytics" className="space-y-4">
                  <div className="text-center py-8">
                    <Trophy className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                      Analytics Coming Soon
                    </h3>
                    <p className="text-muted-foreground">
                      Detailed campaign analytics and performance metrics will
                      be available here.
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
