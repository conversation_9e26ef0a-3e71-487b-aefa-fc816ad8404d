"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  Target,
  Play,
  Pause,
  Eye,
  Upload,
  Download,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/lib/auth-context";
import {
  questApi,
  campaignApi,
  categoryApi,
  type Quest,
  type Campaign,
  type Category,
} from "@/lib/api";

// Static mock data removed - now using real API data

const validationTypes = [
  {
    value: "automatic",
    label: "Automatic",
    description: "Validated by system integration",
  },
  {
    value: "manual",
    label: "Manual Review",
    description: "Requires moderator approval",
  },
  {
    value: "code",
    label: "Code Entry",
    description: "User enters a verification code",
  },
  {
    value: "upload",
    label: "File Upload",
    description: "User uploads proof/screenshot",
  },
];

const frequencyTypes = [
  {
    value: "one-time",
    label: "One-time",
    description: "Can only be completed once",
  },
  {
    value: "daily",
    label: "Daily",
    description: "Can be completed once per day",
  },
  {
    value: "weekly",
    label: "Weekly",
    description: "Can be completed once per week",
  },
  {
    value: "monthly",
    label: "Monthly",
    description: "Can be completed once per month",
  },
  {
    value: "unlimited",
    label: "Unlimited",
    description: "Can be completed multiple times",
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "Active":
      return "bg-green-100 text-green-800";
    case "Draft":
      return "bg-gray-100 text-gray-800";
    case "Paused":
      return "bg-yellow-100 text-yellow-800";
    case "Archived":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getValidationIcon = (validation: string) => {
  switch (validation) {
    case "Automatic":
      return <Zap className="h-4 w-4 text-green-600" />;
    case "Manual":
      return <Eye className="h-4 w-4 text-blue-600" />;
    case "Code":
      return <CheckCircle className="h-4 w-4 text-purple-600" />;
    case "Upload":
      return <Upload className="h-4 w-4 text-orange-600" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-600" />;
  }
};

export function Quests() {
  // Authentication
  const { user, token } = useAuth();

  // UI State
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedQuest, setSelectedQuest] = useState<Quest | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("All Categories");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentQuest, setCurrentQuest] = useState({
    title: "",
    description: "",
    category: "",
    campaign: "",
    points: 100,
    frequency: "one-time",
    validation: "automatic",
    requirements: [],
    rewards: [],
    isActive: true,
  });

  // API Data State
  const [quests, setQuests] = useState<Quest[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  // Loading and Error State
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load data from API
  useEffect(() => {
    const loadData = async () => {
      if (!token || !user) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load all data in parallel
        const [questsData, campaignsData, categoriesData] = await Promise.all([
          questApi.getQuests(token),
          campaignApi.getCampaigns(token),
          categoryApi.getCategories(token),
        ]);

        setQuests(questsData);
        setCampaigns(campaignsData);
        setCategories(categoriesData);

        console.log(`✅ Loaded ${questsData.length} quests for organization`);
      } catch (err: any) {
        console.error("Failed to load quests data:", err);
        setError(err.detail || "Failed to load quests data");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [token, user]);

  const filteredQuests = quests.filter((quest) => {
    const matchesSearch =
      quest.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (quest.description &&
        quest.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Get category name from category ID
    const questCategory = quest.category_id
      ? categories.find((c) => c.id === quest.category_id)?.name ||
        "Uncategorized"
      : "Uncategorized";

    const matchesCategory =
      categoryFilter === "All Categories" || questCategory === categoryFilter;
    const matchesStatus =
      statusFilter === "all" || quest.status.toLowerCase() === statusFilter;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Create dynamic quest categories from loaded categories
  const questCategories = ["All Categories", ...categories.map((c) => c.name)];

  const handleCreateQuest = () => {
    setCurrentQuest({
      title: "",
      description: "",
      category: "",
      campaign: "",
      points: 100,
      frequency: "one-time",
      validation: "automatic",
      requirements: [],
      rewards: [],
      isActive: true,
    });
    setIsCreateOpen(true);
  };

  const handleViewQuest = (quest: Quest) => {
    setSelectedQuest(quest);
    setIsViewOpen(true);
  };

  const handleEditQuest = (quest: Quest) => {
    // Populate form with quest data
    const questCategory = quest.category_id
      ? categories.find((c) => c.id === quest.category_id)?.name || ""
      : "";

    const questCampaign =
      campaigns.find((c) => c.id === quest.campaign_id)?.name || "";

    setCurrentQuest({
      title: quest.title,
      description: quest.description || "",
      category: questCategory.toLowerCase(),
      campaign: questCampaign.toLowerCase(),
      points: quest.points_reward,
      frequency: quest.frequency.replace("_", "-"), // Convert back to UI format
      validation: quest.validation_type,
      requirements: [], // TODO: Parse from validation_criteria
      rewards: [],
      isActive: quest.status === "active",
    });

    setSelectedQuest(quest);
    setIsEditOpen(true);
  };

  const handleDeleteQuest = async (quest: Quest) => {
    if (!token || !user) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete the quest "${quest.title}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setIsLoading(true);
      setError(null);

      await questApi.deleteQuest(token, quest.id);

      // Remove from local state
      setQuests((prev) => prev.filter((q) => q.id !== quest.id));

      console.log(`✅ Successfully deleted quest: "${quest.title}"`);
    } catch (err: any) {
      console.error("Failed to delete quest:", err);
      setError(err.detail || "Failed to delete quest");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDuplicateQuest = (quest: Quest) => {
    // Populate form with quest data for duplication
    const questCategory = quest.category_id
      ? categories.find((c) => c.id === quest.category_id)?.name || ""
      : "";

    const questCampaign =
      campaigns.find((c) => c.id === quest.campaign_id)?.name || "";

    setCurrentQuest({
      title: `${quest.title} (Copy)`,
      description: quest.description || "",
      category: questCategory.toLowerCase(),
      campaign: questCampaign.toLowerCase(),
      points: quest.points_reward,
      frequency: quest.frequency.replace("_", "-"), // Convert back to UI format
      validation: quest.validation_type,
      requirements: [], // TODO: Parse from validation_criteria
      rewards: [],
      isActive: quest.status === "active",
    });

    setIsCreateOpen(true);
  };

  const handleToggleQuestStatus = async (quest: Quest) => {
    if (!token || !user) return;

    const newStatus = quest.status === "active" ? "paused" : "active";

    try {
      setIsLoading(true);
      setError(null);

      const updatedQuest = await questApi.updateQuest(token, quest.id, {
        status: newStatus as any,
      });

      // Update local state
      setQuests((prev) =>
        prev.map((q) => (q.id === quest.id ? updatedQuest : q))
      );

      console.log(
        `✅ Successfully ${
          newStatus === "active" ? "activated" : "paused"
        } quest: "${quest.title}"`
      );
    } catch (err: any) {
      console.error("Failed to update quest status:", err);
      setError(err.detail || "Failed to update quest status");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveQuest = async () => {
    if (!token || !user || !currentQuest.title.trim()) return;

    const isEditing = isEditOpen && selectedQuest;

    try {
      setIsLoading(true);
      setError(null);

      // Map frequency and validation values to backend enum format
      const frequencyMapping: Record<string, string> = {
        "one-time": "one_time",
        daily: "daily",
        weekly: "weekly",
        monthly: "monthly",
        unlimited: "unlimited",
      };

      const validationMapping: Record<string, string> = {
        automatic: "automatic",
        manual: "manual",
        code: "code",
        upload: "upload",
      };

      // Find category ID from category name
      const categoryId = currentQuest.category
        ? categories.find(
            (c) => c.name.toLowerCase() === currentQuest.category.toLowerCase()
          )?.id
        : undefined;

      // Find campaign ID from campaign name
      let campaignId: string;
      if (currentQuest.campaign) {
        const selectedCampaign = campaigns.find(
          (c) => c.name.toLowerCase() === currentQuest.campaign.toLowerCase()
        );
        if (!selectedCampaign) {
          setError("Selected campaign not found");
          return;
        }
        campaignId = selectedCampaign.id;
      } else {
        // If no campaign selected, check if any campaigns exist
        if (campaigns.length === 0) {
          setError(
            "You must create a campaign first before creating quests. Quests must belong to a campaign."
          );
          return;
        }
        // Use the first available campaign as default
        campaignId = campaigns[0].id;
      }

      const questData = {
        title: currentQuest.title.trim(),
        description: currentQuest.description?.trim() || "",
        points_reward: currentQuest.points || 100,
        frequency: (frequencyMapping[currentQuest.frequency] ||
          "one_time") as any,
        validation_type: (validationMapping[currentQuest.validation] ||
          "automatic") as any,
        validation_criteria:
          currentQuest.requirements.length > 0
            ? { requirements: currentQuest.requirements }
            : undefined,
        campaign_id: campaignId,
        category_id: categoryId,
      };

      if (isEditing) {
        console.log("Updating quest with data:", questData);
        const updatedQuest = await questApi.updateQuest(
          token,
          selectedQuest.id,
          questData
        );

        // Update local state
        setQuests((prev) =>
          prev.map((q) => (q.id === selectedQuest.id ? updatedQuest : q))
        );

        setIsEditOpen(false);
        console.log(`✅ Successfully updated quest: "${updatedQuest.title}"`);
      } else {
        console.log("Creating quest with data:", questData);
        const newQuest = await questApi.createQuest(token, questData);

        // Add to local state
        setQuests((prev) => [...prev, newQuest]);

        setIsCreateOpen(false);
        console.log(`✅ Successfully created quest: "${newQuest.title}"`);
      }

      // Reset form
      setCurrentQuest({
        title: "",
        description: "",
        category: "",
        campaign: "",
        points: 100,
        frequency: "one-time",
        validation: "automatic",
        requirements: [],
        rewards: [],
        isActive: true,
      });
      setSelectedQuest(null);
    } catch (err: any) {
      console.error(`Failed to ${isEditing ? "update" : "create"} quest:`, err);
      setError(
        err.detail || `Failed to ${isEditing ? "update" : "create"} quest`
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to format enum values for display
  const formatEnumValue = (value: string) => {
    return value.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quests</h1>
            <p className="text-muted-foreground">
              Create and manage customer engagement quests
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading quests...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  className="bg-red-100 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  onClick={() => setError(null)}
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quests</h1>
          <p className="text-muted-foreground">
            Create and manage customer engagement quests
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={handleCreateQuest}>
            <Plus className="mr-2 h-4 w-4" />
            Create Quest
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search quests..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                {questCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quests Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Quests</CardTitle>
          <CardDescription>
            Manage your customer engagement quests and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredQuests.length === 0 ? (
            <div className="text-center py-12">
              <Target className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No quests found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ||
                categoryFilter !== "All Categories" ||
                statusFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "Get started by creating your first quest"}
              </p>
              <Button onClick={handleCreateQuest}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Quest
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quest</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Points</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Validation</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Performance & Campaign</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuests.map((quest) => (
                  <TableRow
                    key={quest.id}
                    className="cursor-pointer hover:bg-muted/50"
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                          <Target className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <div>
                          <div className="font-medium">{quest.title}</div>
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {quest.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {quest.category_id
                          ? categories.find((c) => c.id === quest.category_id)
                              ?.name || "Uncategorized"
                          : "Uncategorized"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{quest.points_reward}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {formatEnumValue(quest.frequency)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getValidationIcon(
                          formatEnumValue(quest.validation_type)
                        )}
                        {formatEnumValue(quest.validation_type)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={getStatusColor(
                          formatEnumValue(quest.status)
                        )}
                      >
                        {formatEnumValue(quest.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div>
                          {Math.floor(
                            Math.random() * 5000 + 500
                          ).toLocaleString()}{" "}
                          completions
                        </div>
                        <div className="text-muted-foreground">
                          {Math.floor(Math.random() * 40 + 45)}% success rate
                        </div>
                        <div className="text-xs text-muted-foreground mt-2 pt-1 border-t">
                          Campaign:{" "}
                          {campaigns.find((c) => c.id === quest.campaign_id)
                            ?.name || "Unknown"}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Created:{" "}
                          {new Date(quest.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewQuest(quest)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditQuest(quest)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDuplicateQuest(quest)}
                          >
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          {quest.status === "active" ? (
                            <DropdownMenuItem
                              onClick={() => handleToggleQuestStatus(quest)}
                            >
                              <Pause className="mr-2 h-4 w-4" />
                              Pause
                            </DropdownMenuItem>
                          ) : quest.status === "paused" ? (
                            <DropdownMenuItem
                              onClick={() => handleToggleQuestStatus(quest)}
                            >
                              <Play className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          ) : null}
                          <DropdownMenuItem
                            onClick={() => handleDeleteQuest(quest)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Quest Modal */}
      <Dialog
        open={isCreateOpen || isEditOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateOpen(false);
            setIsEditOpen(false);
            setSelectedQuest(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEditOpen ? "Edit Quest" : "Create New Quest"}
            </DialogTitle>
            <DialogDescription>
              {isEditOpen
                ? "Update the quest details and configuration"
                : "Design a quest to engage your customers and drive specific behaviors"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Basic Information</h4>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="quest-title">Quest Title *</Label>
                  <Input
                    id="quest-title"
                    placeholder="e.g., Complete Your Profile"
                    value={currentQuest.title}
                    onChange={(e) =>
                      setCurrentQuest((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="quest-description">Description</Label>
                  <Textarea
                    id="quest-description"
                    placeholder="Describe what users need to do to complete this quest"
                    rows={3}
                    value={currentQuest.description}
                    onChange={(e) =>
                      setCurrentQuest((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="quest-category">Category</Label>
                    <Select
                      value={currentQuest.category}
                      onValueChange={(value) =>
                        setCurrentQuest((prev) => ({
                          ...prev,
                          category: value,
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories
                          .filter(
                            (category) =>
                              category.name && category.name.trim() !== ""
                          )
                          .map((category) => (
                            <SelectItem
                              key={category.id}
                              value={category.name.toLowerCase()}
                            >
                              {category.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="quest-points">Points Reward</Label>
                    <Input
                      id="quest-points"
                      type="number"
                      min="1"
                      value={currentQuest.points}
                      onChange={(e) =>
                        setCurrentQuest((prev) => ({
                          ...prev,
                          points: Number.parseInt(e.target.value) || 0,
                        }))
                      }
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="quest-campaign">Campaign *</Label>
                  <Select
                    value={currentQuest.campaign}
                    onValueChange={(value) =>
                      setCurrentQuest((prev) => ({
                        ...prev,
                        campaign: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select campaign" />
                    </SelectTrigger>
                    <SelectContent>
                      {campaigns.map((campaign) => (
                        <SelectItem
                          key={campaign.id}
                          value={campaign.name.toLowerCase()}
                        >
                          {campaign.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            {/* Quest Configuration */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Quest Configuration</h4>

              {/* Frequency */}
              <div className="space-y-3">
                <Label>Completion Frequency</Label>
                <RadioGroup
                  value={currentQuest.frequency}
                  onValueChange={(value) =>
                    setCurrentQuest((prev) => ({ ...prev, frequency: value }))
                  }
                  className="grid grid-cols-1 gap-3"
                >
                  {frequencyTypes.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-3 p-3 border rounded-lg"
                    >
                      <RadioGroupItem value={type.value} id={type.value} />
                      <div className="flex-1">
                        <Label
                          htmlFor={type.value}
                          className="font-medium cursor-pointer"
                        >
                          {type.label}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {type.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <Separator />

              {/* Validation Method */}
              <div className="space-y-3">
                <Label>Validation Method</Label>
                <RadioGroup
                  value={currentQuest.validation}
                  onValueChange={(value) =>
                    setCurrentQuest((prev) => ({ ...prev, validation: value }))
                  }
                  className="grid grid-cols-1 gap-3"
                >
                  {validationTypes.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-3 p-3 border rounded-lg"
                    >
                      <RadioGroupItem
                        value={type.value}
                        id={`validation-${type.value}`}
                      />
                      <div className="flex-1">
                        <Label
                          htmlFor={`validation-${type.value}`}
                          className="font-medium cursor-pointer"
                        >
                          {type.label}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {type.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>

            <Separator />

            {/* Additional Settings */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Additional Settings</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="quest-active">Active Quest</Label>
                    <p className="text-sm text-muted-foreground">
                      Quest will be available to users immediately
                    </p>
                  </div>
                  <Switch
                    id="quest-active"
                    checked={currentQuest.isActive}
                    onCheckedChange={(checked) =>
                      setCurrentQuest((prev) => ({
                        ...prev,
                        isActive: checked,
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Preview</h4>
              <div className="p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium">
                    {currentQuest.title || "Quest Title"}
                  </h5>
                  <div className="flex items-center gap-2">
                    {currentQuest.category && (
                      <Badge variant="secondary">{currentQuest.category}</Badge>
                    )}
                    <span className="text-sm text-muted-foreground">
                      {currentQuest.points} pts
                    </span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {currentQuest.description ||
                    "Quest description will appear here"}
                </p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>
                    Frequency:{" "}
                    {
                      frequencyTypes.find(
                        (f) => f.value === currentQuest.frequency
                      )?.label
                    }
                  </span>
                  <span>
                    Validation:{" "}
                    {
                      validationTypes.find(
                        (v) => v.value === currentQuest.validation
                      )?.label
                    }
                  </span>
                  <span>
                    Status: {currentQuest.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateOpen(false);
                setIsEditOpen(false);
                setSelectedQuest(null);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveQuest}
              disabled={
                !currentQuest.title.trim() ||
                !currentQuest.campaign.trim() ||
                isLoading
              }
            >
              {isLoading
                ? isEditOpen
                  ? "Updating..."
                  : "Creating..."
                : isEditOpen
                ? "Update Quest"
                : "Create Quest"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Quest Modal */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          {selectedQuest && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {selectedQuest.title}
                  <Badge className={getStatusColor(selectedQuest.status)}>
                    {selectedQuest.status}
                  </Badge>
                </DialogTitle>
                <DialogDescription>
                  {selectedQuest.description}
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Category</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Badge variant="secondary">
                          {selectedQuest.category_id
                            ? categories.find(
                                (c) => c.id === selectedQuest.category_id
                              )?.name || "Uncategorized"
                            : "Uncategorized"}
                        </Badge>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Points Reward</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {selectedQuest.points_reward}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Frequency</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          {selectedQuest.frequency}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Validation</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-2">
                          {getValidationIcon(
                            formatEnumValue(selectedQuest.validation_type)
                          )}
                          {formatEnumValue(selectedQuest.validation_type)}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="performance" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Total Completions
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {Math.floor(
                            Math.random() * 5000 + 500
                          ).toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Trophy className="h-4 w-4" />
                          Success Rate
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {Math.floor(Math.random() * 40 + 45)}%
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Quest Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            Created:
                          </span>
                          <span>
                            {new Date(
                              selectedQuest.created_at
                            ).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            Last Modified:
                          </span>
                          <span>
                            {new Date(
                              selectedQuest.updated_at
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="campaigns" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">
                        Associated Campaigns
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedQuest.campaign_id ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between p-2 border rounded">
                            <span className="font-medium">
                              {campaigns.find(
                                (c) => c.id === selectedQuest.campaign_id
                              )?.name || "Unknown Campaign"}
                            </span>
                            <Badge variant="outline">
                              {formatEnumValue(
                                campaigns.find(
                                  (c) => c.id === selectedQuest.campaign_id
                                )?.status || "unknown"
                              )}
                            </Badge>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-muted-foreground">
                            This quest is not associated with any campaigns yet.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
