"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  config: Record<string, { label: string; color: string }>
}

export function ChartContainer({ className, config, children }: ChartContainerProps) {
  const { theme } = useTheme()

  React.useEffect(() => {
    if (theme === "dark") {
      document.documentElement.style.setProperty("--recharts-tooltip-bg", "#18181b")
    } else {
      document.documentElement.style.setProperty("--recharts-tooltip-bg", "#fff")
    }

    for (const key in config) {
      document.documentElement.style.setProperty(`--color-${key}`, config[key].color)
    }
  }, [theme, config])

  return (
    <div className={className}>
      {React.Children.map(children, (child) => {
        return React.cloneElement(child as React.ReactElement, {
          config: config,
        })
      })}
    </div>
  )
}

export function ChartTooltip({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent className="w-max rounded-md border border-border bg-[--recharts-tooltip-bg] p-2 shadow-md outline-none">
          {props.children}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export function ChartTooltipContent({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  if (!props.payload || !Array.isArray(props.payload) || props.payload.length === 0) {
    return null
  }

  const payload = props.payload[0].payload as Record<string, string | number>
  const config = props.payload[0].config as Record<string, { label: string; color: string }>

  return (
    <div className="grid gap-1">
      {props.label && <div className="text-sm font-medium">{props.label}</div>}
      <div className="grid gap-0.5 py-1">
        {Object.keys(config).map((key) => {
          if (payload[key] === undefined) {
            return null
          }
          return (
            <div key={key} className="flex items-center justify-between text-xs">
              <span className="capitalize">{config[key].label}</span>
              <span className="font-medium">{payload[key]}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
