"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Gift,
  Check,
  X,
  AlertCircle,
  Edit,
  Archive,
  Loader2,
  Trash2,
  <PERSON>fresh<PERSON><PERSON>,
} from "lucide-react";
import { useAuth } from "@/lib/auth-context";
import {
  RewardAPI,
  type Reward,
  type RewardCreate,
  type RewardUpdate,
} from "@/lib/api/rewards";
import { CategoryAPI, type Category } from "@/lib/api/categories";
import { useToast } from "@/hooks/use-toast";

// Form state interfaces
interface RewardFormData {
  name: string;
  description: string;
  points_cost: string;
  stock_quantity: string;
  image_url: string;
  category_id: string;
}

const initialFormData: RewardFormData = {
  name: "",
  description: "",
  points_cost: "",
  stock_quantity: "",
  image_url: "",
  category_id: "none",
};

const redemptionRequests = [
  {
    id: 1,
    user: "Sarah Chen",
    reward: "10% Discount Coupon",
    points: 500,
    status: "Pending",
    date: "2024-12-10",
    email: "<EMAIL>",
  },
  {
    id: 2,
    user: "Mike Johnson",
    reward: "$25 Gift Card",
    points: 2500,
    status: "Approved",
    date: "2024-12-09",
    email: "<EMAIL>",
  },
  {
    id: 3,
    user: "Emma Davis",
    reward: "Premium Feature Access",
    points: 1000,
    status: "Pending",
    date: "2024-12-09",
    email: "<EMAIL>",
  },
];

const getStatusColor = (status: string) => {
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "bg-green-100 text-green-800";
    case "DRAFT":
      return "bg-gray-100 text-gray-800";
    case "PAUSED":
      return "bg-yellow-100 text-yellow-800";
    case "ARCHIVED":
      return "bg-red-100 text-red-800";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "APPROVED":
      return "bg-green-100 text-green-800";
    case "REJECTED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusDisplay = (status: string) => {
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "Active";
    case "DRAFT":
      return "Draft";
    case "PAUSED":
      return "Paused";
    case "ARCHIVED":
      return "Archived";
    default:
      return status;
  }
};

export function Rewards() {
  const { token } = useAuth();
  const { toast } = useToast();

  // State management
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [editingReward, setEditingReward] = useState<Reward | null>(null);
  const [formData, setFormData] = useState<RewardFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletingRewardId, setDeletingRewardId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [rewardToDelete, setRewardToDelete] = useState<Reward | null>(null);

  // Load data on component mount
  useEffect(() => {
    if (token) {
      loadRewards();
      loadCategories();
    }
  }, [token]);

  const loadRewards = async () => {
    try {
      setIsLoading(true);
      const rewardsData = await RewardAPI.getRewards(token!);
      setRewards(rewardsData);
    } catch (error) {
      console.error("Failed to load rewards:", error);
      toast({
        title: "Error",
        description: "Failed to load rewards. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await CategoryAPI.getCategories(token!);
      console.log("Loaded categories:", categoriesData);
      setCategories(categoriesData);

      // If no categories exist, show a helpful message
      if (categoriesData.length === 0) {
        toast({
          title: "No Categories Found",
          description:
            "Consider seeding default categories first to organize your rewards.",
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Failed to load categories:", error);
      toast({
        title: "Warning",
        description:
          "Failed to load categories. You can still create rewards without categories.",
        variant: "destructive",
      });
    }
  };

  const handleCreateReward = async () => {
    if (!token) return;

    try {
      setIsSubmitting(true);

      const rewardData: RewardCreate = {
        name: formData.name,
        description: formData.description || undefined,
        points_cost: parseInt(formData.points_cost),
        stock_quantity: formData.stock_quantity
          ? parseInt(formData.stock_quantity)
          : undefined,
        image_url: formData.image_url || undefined,
        category_id:
          formData.category_id && formData.category_id !== "none"
            ? formData.category_id
            : undefined,
      };

      await RewardAPI.createReward(token, rewardData);

      toast({
        title: "Success",
        description: "Reward created successfully!",
      });

      setIsCreateOpen(false);
      setFormData(initialFormData);
      loadRewards();
    } catch (error) {
      console.error("Failed to create reward:", error);
      toast({
        title: "Error",
        description: "Failed to create reward. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditReward = async () => {
    if (!token || !editingReward) return;

    try {
      setIsSubmitting(true);

      const updateData: RewardUpdate = {
        name: formData.name,
        description: formData.description || undefined,
        points_cost: parseInt(formData.points_cost),
        stock_quantity: formData.stock_quantity
          ? parseInt(formData.stock_quantity)
          : undefined,
        image_url: formData.image_url || undefined,
        category_id:
          formData.category_id && formData.category_id !== "none"
            ? formData.category_id
            : undefined,
      };

      await RewardAPI.updateReward(token, editingReward.id, updateData);

      toast({
        title: "Success",
        description: "Reward updated successfully!",
      });

      setIsEditOpen(false);
      setEditingReward(null);
      setFormData(initialFormData);
      loadRewards();
    } catch (error) {
      console.error("Failed to update reward:", error);
      toast({
        title: "Error",
        description: "Failed to update reward. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openDeleteDialog = (reward: Reward) => {
    setRewardToDelete(reward);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteReward = async () => {
    if (!token || !rewardToDelete || deletingRewardId) return;

    try {
      setDeletingRewardId(rewardToDelete.id);
      console.log("Deleting reward:", rewardToDelete.id);

      await RewardAPI.deleteReward(token, rewardToDelete.id);

      console.log("Reward deleted successfully");

      toast({
        title: "Success",
        description: "Reward deleted successfully!",
      });

      // Close dialog and reset state
      setIsDeleteDialogOpen(false);
      setRewardToDelete(null);

      // Reload the rewards list
      loadRewards();
    } catch (error: any) {
      console.error("Delete reward error:", error);
      console.error("Error details:", {
        status: error?.status_code,
        detail: error?.detail,
        message: error?.message,
        response: error?.response,
      });

      // Handle specific error cases
      if (error?.status_code === 404 || error?.response?.status === 404) {
        toast({
          title: "Already Deleted",
          description: "This reward was already deleted or doesn't exist.",
          variant: "destructive",
        });
        // Close dialog and refresh the list to sync with server state
        setIsDeleteDialogOpen(false);
        setRewardToDelete(null);
        loadRewards();
      } else {
        toast({
          title: "Error",
          description:
            error?.detail ||
            error?.response?.data?.detail ||
            error?.message ||
            "Failed to delete reward. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setDeletingRewardId(null);
    }
  };

  const openEditDialog = (reward: Reward) => {
    setEditingReward(reward);
    setFormData({
      name: reward.name,
      description: reward.description || "",
      points_cost: reward.points_cost.toString(),
      stock_quantity: reward.stock_quantity?.toString() || "",
      image_url: reward.image_url || "",
      category_id: reward.category_id || "none",
    });
    setIsEditOpen(true);
  };

  const handleSeedRewards = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      // Call the seeding endpoint using the API client
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/v1/seeding/seed-sample-data`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to seed rewards");
      }

      const result = await response.json();

      toast({
        title: "Success",
        description: `Sample data seeded successfully! Created ${
          result.summary?.rewards_created || 0
        } rewards.`,
      });

      loadRewards();
    } catch (error) {
      console.error("Failed to seed rewards:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to seed sample rewards. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSeedCategories = async () => {
    if (!token) return;

    try {
      setIsSubmitting(true);
      await CategoryAPI.seedDefaultCategories(token);

      toast({
        title: "Success",
        description: "Default categories seeded successfully!",
      });

      // Reload categories to update the UI
      loadCategories();
    } catch (error) {
      console.error("Failed to seed categories:", error);
      toast({
        title: "Error",
        description: "Failed to seed default categories. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Rewards</h1>
          <p className="text-muted-foreground">
            Manage your rewards catalog and redemption requests
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleSeedRewards}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Seed Sample Data
          </Button>
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Reward
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Reward</DialogTitle>
                <DialogDescription>
                  Create a new reward for your customers to redeem
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="reward-name">Reward Name *</Label>
                  <Input
                    id="reward-name"
                    placeholder="Enter reward name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="reward-description">Description</Label>
                  <Textarea
                    id="reward-description"
                    placeholder="Describe the reward"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="category">Category</Label>
                    {categories.length === 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSeedCategories}
                        disabled={isSubmitting}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Seed Categories
                      </Button>
                    )}
                  </div>
                  <Select
                    value={formData.category_id}
                    onValueChange={(value) =>
                      setFormData({ ...formData, category_id: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          categories.length === 0
                            ? "No categories available - seed some first"
                            : "Select a category (optional)"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground text-center">
                          No categories available. Seed default categories
                          first.
                        </div>
                      ) : (
                        <>
                          <SelectItem value="none">
                            <span className="text-muted-foreground">
                              No category
                            </span>
                          </SelectItem>
                          {categories
                            .filter(
                              (category) =>
                                category.id && category.id.trim() !== ""
                            )
                            .map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                <div className="flex items-center gap-2">
                                  {category.icon && (
                                    <span>{category.icon}</span>
                                  )}
                                  <span>{category.name}</span>
                                  <div
                                    className="w-3 h-3 rounded-full ml-auto"
                                    style={{ backgroundColor: category.color }}
                                  />
                                </div>
                              </SelectItem>
                            ))}
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="points">Points Required *</Label>
                    <Input
                      id="points"
                      type="number"
                      placeholder="500"
                      value={formData.points_cost}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          points_cost: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="stock">Stock Quantity</Label>
                    <Input
                      id="stock"
                      type="number"
                      placeholder="100 (leave empty for unlimited)"
                      value={formData.stock_quantity}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          stock_quantity: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="image-url">Image URL</Label>
                  <Input
                    id="image-url"
                    placeholder="https://example.com/image.jpg"
                    value={formData.image_url}
                    onChange={(e) =>
                      setFormData({ ...formData, image_url: e.target.value })
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsCreateOpen(false);
                    setFormData(initialFormData);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateReward}
                  disabled={
                    isSubmitting || !formData.name || !formData.points_cost
                  }
                >
                  {isSubmitting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Add Reward
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Edit Dialog */}
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Edit Reward</DialogTitle>
              <DialogDescription>Update the reward details</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-reward-name">Reward Name *</Label>
                <Input
                  id="edit-reward-name"
                  placeholder="Enter reward name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-reward-description">Description</Label>
                <Textarea
                  id="edit-reward-description"
                  placeholder="Describe the reward"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="edit-category">Category</Label>
                  {categories.length === 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleSeedCategories}
                      disabled={isSubmitting}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Seed Categories
                    </Button>
                  )}
                </div>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) =>
                    setFormData({ ...formData, category_id: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        categories.length === 0
                          ? "No categories available - seed some first"
                          : "Select a category (optional)"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <div className="p-2 text-sm text-muted-foreground text-center">
                        No categories available. Seed default categories first.
                      </div>
                    ) : (
                      <>
                        <SelectItem value="none">
                          <span className="text-muted-foreground">
                            No category
                          </span>
                        </SelectItem>
                        {categories
                          .filter(
                            (category) =>
                              category.id && category.id.trim() !== ""
                          )
                          .map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center gap-2">
                                {category.icon && <span>{category.icon}</span>}
                                <span>{category.name}</span>
                                <div
                                  className="w-3 h-3 rounded-full ml-auto"
                                  style={{ backgroundColor: category.color }}
                                />
                              </div>
                            </SelectItem>
                          ))}
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-points">Points Required *</Label>
                  <Input
                    id="edit-points"
                    type="number"
                    placeholder="500"
                    value={formData.points_cost}
                    onChange={(e) =>
                      setFormData({ ...formData, points_cost: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-stock">Stock Quantity</Label>
                  <Input
                    id="edit-stock"
                    type="number"
                    placeholder="100 (leave empty for unlimited)"
                    value={formData.stock_quantity}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        stock_quantity: e.target.value,
                      })
                    }
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-image-url">Image URL</Label>
                <Input
                  id="edit-image-url"
                  placeholder="https://example.com/image.jpg"
                  value={formData.image_url}
                  onChange={(e) =>
                    setFormData({ ...formData, image_url: e.target.value })
                  }
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditOpen(false);
                  setEditingReward(null);
                  setFormData(initialFormData);
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleEditReward}
                disabled={
                  isSubmitting || !formData.name || !formData.points_cost
                }
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Update Reward
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Delete Reward</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this reward? This action cannot
                be undone.
              </DialogDescription>
            </DialogHeader>
            {rewardToDelete && (
              <div className="py-4">
                <div className="bg-muted rounded-lg p-4">
                  <h4 className="font-medium text-sm mb-2">
                    Reward to be deleted:
                  </h4>
                  <div className="space-y-1">
                    <p className="font-medium">{rewardToDelete.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {rewardToDelete.description}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Points:</span>{" "}
                      {rewardToDelete.points_cost}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setRewardToDelete(null);
                }}
                disabled={deletingRewardId !== null}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteReward}
                disabled={deletingRewardId !== null}
              >
                {deletingRewardId ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Delete Reward
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-blue-600" />
          <div>
            <p className="font-medium text-blue-900">
              Want seamless reward fulfillment?
            </p>
            <p className="text-sm text-blue-700">
              Set up SDK, API or Webhooks for automated reward delivery.{" "}
              <Button
                variant="link"
                className="p-0 h-auto text-blue-700 underline"
              >
                Setup Guide
              </Button>
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="catalog" className="space-y-4">
        <TabsList>
          <TabsTrigger value="catalog">Rewards Catalog</TabsTrigger>
          <TabsTrigger value="requests">Redemption Requests</TabsTrigger>
        </TabsList>

        <TabsContent value="catalog">
          <Card>
            <CardHeader>
              <CardTitle>Rewards Catalog</CardTitle>
              <CardDescription>
                Manage available rewards for your customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading rewards...</p>
                  </div>
                </div>
              ) : rewards.length === 0 ? (
                <div className="text-center py-8">
                  <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No rewards yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first reward or seed sample data to get started.
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => setIsCreateOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Reward
                    </Button>
                    <Button variant="outline" onClick={handleSeedRewards}>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Seed Sample Data
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {rewards.map((reward) => (
                    <Card key={reward.id} className="overflow-hidden">
                      <div className="aspect-video bg-muted flex items-center justify-center">
                        {reward.image_url ? (
                          <img
                            src={reward.image_url}
                            alt={reward.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Gift className="h-8 w-8 text-muted-foreground" />
                        )}
                      </div>
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h3
                              className="font-semibold truncate"
                              title={reward.name}
                            >
                              {reward.name}
                            </h3>
                            <Badge className={getStatusColor(reward.status)}>
                              {getStatusDisplay(reward.status)}
                            </Badge>
                          </div>
                          {reward.description && (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {reward.description}
                            </p>
                          )}
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <span>{reward.points_cost} points</span>
                            <span>
                              {reward.stock_quantity !== null
                                ? `${reward.stock_quantity} in stock`
                                : "Unlimited"}
                            </span>
                          </div>
                          {reward.category && (
                            <div className="flex items-center gap-1">
                              <span
                                className="w-2 h-2 rounded-full"
                                style={{
                                  backgroundColor: reward.category.color,
                                }}
                              />
                              <span className="text-xs text-muted-foreground">
                                {reward.category.name}
                              </span>
                            </div>
                          )}
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={() => openEditDialog(reward)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={() => openDeleteDialog(reward)}
                              disabled={deletingRewardId === reward.id}
                            >
                              {deletingRewardId === reward.id ? (
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              ) : (
                                <Trash2 className="h-3 w-3 mr-1" />
                              )}
                              Delete
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests">
          <Card>
            <CardHeader>
              <CardTitle>Redemption Requests</CardTitle>
              <CardDescription>
                Review and manage customer redemption requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Reward</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {redemptionRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.user}</div>
                          <div className="text-sm text-muted-foreground">
                            {request.email}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{request.reward}</TableCell>
                      <TableCell>{request.points}</TableCell>
                      <TableCell>{request.date}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(request.status)}>
                          {request.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {request.status === "Pending" && (
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Check className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button size="sm" variant="outline">
                              <X className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </div>
                        )}
                        {request.status === "Approved" && (
                          <div className="flex items-center gap-2 text-sm text-green-600">
                            <Check className="h-4 w-4" />
                            Approved
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
