"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { usePermissions, PermissionGate } from "@/hooks/use-permissions";
import { PermissionAPI, Permission } from "@/lib/api/permissions";
import { <PERSON><PERSON><PERSON>, Role } from "@/lib/api/roles";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Plus,
  Shield,
  Search,
  Filter,
  Eye,
  Settings,
  Users,
  Database,
  BarChart3,
  FileText,
  Lock,
  Unlock,
  Info,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

interface PermissionManagementProps {}

// Permission category icons mapping
const CATEGORY_ICONS: Record<string, any> = {
  Dashboard: BarChart3,
  Users: Users,
  Roles: Shield,
  Campaigns: FileText,
  Quests: Settings,
  Badges: Shield,
  Rewards: Settings,
  Analytics: BarChart3,
  Organizations: Database,
  Categories: Database,
  System: Lock,
};

// Permission action colors
const ACTION_COLORS: Record<string, string> = {
  read: "#10b981",
  create: "#3b82f6",
  update: "#f59e0b",
  delete: "#ef4444",
  manage: "#8b5cf6",
  export: "#06b6d4",
  publish: "#84cc16",
  archive: "#6b7280",
};

export function PermissionManagement({}: PermissionManagementProps) {
  const { token } = useAuth();
  const { hasPermission } = usePermissions();

  const [isLoading, setIsLoading] = useState(true);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);

  // Dialog states
  const [isCreatePermissionOpen, setIsCreatePermissionOpen] = useState(false);
  const [isPermissionDetailsOpen, setIsPermissionDetailsOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] =
    useState<Permission | null>(null);

  // Form states
  const [newPermission, setNewPermission] = useState({
    name: "",
    description: "",
    resource: "",
    action: "",
    scope: "organization",
    category: "",
  });

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [actionFilter, setActionFilter] = useState("all");
  const [activeTab, setActiveTab] = useState("overview");

  // Load data
  const loadData = async () => {
    if (!token) return;

    try {
      setIsLoading(true);

      const [permissionsData, rolesData] = await Promise.all([
        PermissionAPI.getPermissions(token),
        RoleAPI.getRoles(token),
      ]);

      setPermissions(permissionsData);
      setRoles(rolesData);
    } catch (error: any) {
      console.error("Failed to load permission data:", error);
      toast.error("Failed to load permissions");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [token]);

  // Create permission
  const handleCreatePermission = async () => {
    if (
      !token ||
      !newPermission.name.trim() ||
      !newPermission.resource.trim() ||
      !newPermission.action.trim()
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const permissionData = {
        name: newPermission.name,
        description: newPermission.description,
        resource: newPermission.resource,
        action: newPermission.action,
        scope: newPermission.scope,
        category: newPermission.category || newPermission.resource,
      };

      await PermissionAPI.createPermission(token, permissionData);
      toast.success("Permission created successfully!");

      setNewPermission({
        name: "",
        description: "",
        resource: "",
        action: "",
        scope: "organization",
        category: "",
      });
      setIsCreatePermissionOpen(false);
      loadData();
    } catch (error: any) {
      console.error("Failed to create permission:", error);
      toast.error(error.detail || "Failed to create permission");
    }
  };

  // Group permissions by category
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    const category = permission.category || "Other";
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  // Filter permissions
  const filteredPermissions = permissions.filter((permission) => {
    const matchesSearch =
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      categoryFilter === "all" || permission.category === categoryFilter;
    const matchesAction =
      actionFilter === "all" || permission.action === actionFilter;

    return matchesSearch && matchesCategory && matchesAction;
  });

  // Get unique categories and actions
  const categories = Array.from(
    new Set(permissions.map((p) => p.category))
  ).sort();
  const actions = Array.from(new Set(permissions.map((p) => p.action))).sort();

  // Get roles that have a specific permission
  const getRolesWithPermission = (permissionId: string) => {
    return roles.filter((role) => role.permissions.includes(permissionId));
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    const IconComponent = CATEGORY_ICONS[category] || Settings;
    return IconComponent;
  };

  // Get action color
  const getActionColor = (action: string) => {
    return ACTION_COLORS[action] || "#6b7280";
  };

  // Format permission ID
  const formatPermissionId = (resource: string, action: string) => {
    return `${resource}:${action}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading permissions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Permission Management
          </h2>
          <p className="text-muted-foreground">
            View and manage system permissions and access control
          </p>
        </div>
        <PermissionGate permissions="permissions:create">
          <Button onClick={() => setIsCreatePermissionOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Permission
          </Button>
        </PermissionGate>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            By Category
          </TabsTrigger>
          <TabsTrigger value="table" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            All Permissions
          </TabsTrigger>
          <TabsTrigger value="matrix" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Role Matrix
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Permissions
                </CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{permissions.length}</div>
                <p className="text-xs text-muted-foreground">
                  Across {categories.length} categories
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Categories
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{categories.length}</div>
                <p className="text-xs text-muted-foreground">
                  Permission categories
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Actions</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{actions.length}</div>
                <p className="text-xs text-muted-foreground">
                  Available actions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Roles Using
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roles.length}</div>
                <p className="text-xs text-muted-foreground">
                  Roles with permissions
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Permission Categories</CardTitle>
              <CardDescription>
                Overview of permissions organized by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(permissionsByCategory).map(
                  ([category, categoryPermissions]) => {
                    const IconComponent = getCategoryIcon(category);
                    return (
                      <Card key={category} className="relative">
                        <CardHeader>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <CardTitle className="text-lg">
                              {category}
                            </CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">
                                Permissions
                              </span>
                              <span className="font-medium">
                                {categoryPermissions.length}
                              </span>
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">
                                Actions
                              </span>
                              <span className="font-medium">
                                {
                                  Array.from(
                                    new Set(
                                      categoryPermissions.map((p) => p.action)
                                    )
                                  ).length
                                }
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {Array.from(
                                new Set(
                                  categoryPermissions.map((p) => p.action)
                                )
                              )
                                .slice(0, 3)
                                .map((action) => (
                                  <Badge
                                    key={action}
                                    variant="secondary"
                                    className="text-xs"
                                    style={{
                                      backgroundColor: `${getActionColor(
                                        action
                                      )}20`,
                                      color: getActionColor(action),
                                    }}
                                  >
                                    {action}
                                  </Badge>
                                ))}
                              {Array.from(
                                new Set(
                                  categoryPermissions.map((p) => p.action)
                                )
                              ).length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +
                                  {Array.from(
                                    new Set(
                                      categoryPermissions.map((p) => p.action)
                                    )
                                  ).length - 3}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  }
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Permissions by Category</CardTitle>
              <CardDescription>
                Detailed view of permissions organized by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="multiple" className="w-full">
                {Object.entries(permissionsByCategory).map(
                  ([category, categoryPermissions]) => {
                    const IconComponent = getCategoryIcon(category);
                    return (
                      <AccordionItem key={category} value={category}>
                        <AccordionTrigger className="hover:no-underline">
                          <div className="flex items-center gap-3">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <span className="font-medium">{category}</span>
                            <Badge variant="secondary" className="ml-auto mr-2">
                              {categoryPermissions.length}
                            </Badge>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="grid gap-3 pt-4">
                            {categoryPermissions.map((permission) => (
                              <div
                                key={permission.id}
                                className="flex items-start justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                                onClick={() => {
                                  setSelectedPermission(permission);
                                  setIsPermissionDetailsOpen(true);
                                }}
                              >
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-medium">
                                      {permission.name}
                                    </span>
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                      style={{
                                        backgroundColor: `${getActionColor(
                                          permission.action
                                        )}20`,
                                        color: getActionColor(
                                          permission.action
                                        ),
                                        borderColor: getActionColor(
                                          permission.action
                                        ),
                                      }}
                                    >
                                      {permission.action}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground mb-2">
                                    {permission.description}
                                  </p>
                                  <code className="text-xs bg-muted px-2 py-1 rounded">
                                    {permission.id}
                                  </code>
                                </div>
                                <div className="text-right">
                                  <div className="text-sm font-medium">
                                    {
                                      getRolesWithPermission(permission.id)
                                        .length
                                    }{" "}
                                    roles
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {permission.scope}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  }
                )}
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Table Tab */}
        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>All Permissions</CardTitle>
                  <CardDescription>
                    Complete list of system permissions
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex items-center gap-4 mb-6">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search permissions..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories
                      .filter((category) => category && category.trim() !== "")
                      .map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <Select value={actionFilter} onValueChange={setActionFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    {actions
                      .filter((action) => action && action.trim() !== "")
                      .map((action) => (
                        <SelectItem key={action} value={action}>
                          {action}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Permissions Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Permission</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Scope</TableHead>
                      <TableHead>Roles</TableHead>
                      <TableHead className="text-right">Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPermissions.map((permission) => (
                      <TableRow key={permission.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{permission.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {permission.description}
                            </div>
                            <code className="text-xs bg-muted px-1 rounded mt-1 inline-block">
                              {permission.id}
                            </code>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {(() => {
                              const IconComponent = getCategoryIcon(
                                permission.category
                              );
                              return <IconComponent className="h-4 w-4" />;
                            })()}
                            {permission.category}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            style={{
                              backgroundColor: `${getActionColor(
                                permission.action
                              )}20`,
                              color: getActionColor(permission.action),
                              borderColor: getActionColor(permission.action),
                            }}
                          >
                            {permission.action}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{permission.scope}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {getRolesWithPermission(permission.id).length}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedPermission(permission);
                              setIsPermissionDetailsOpen(true);
                            }}
                          >
                            <Info className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredPermissions.length === 0 && (
                <div className="text-center py-8">
                  <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold">
                    No permissions found
                  </h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Try adjusting your search or filters
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Role Matrix Tab */}
        <TabsContent value="matrix" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role Permission Matrix</CardTitle>
              <CardDescription>
                View which roles have which permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <div className="min-w-full">
                  <div className="grid gap-4">
                    {roles.map((role) => (
                      <Card key={role.id} className="p-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: role.color }}
                            />
                            <h3 className="font-semibold">{role.name}</h3>
                            <Badge variant="secondary">
                              Level {role.level}
                            </Badge>
                            {role.is_system && (
                              <Badge variant="outline">System</Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {role.permissions.length} permissions
                          </div>
                        </div>

                        <div className="space-y-3">
                          {Object.entries(permissionsByCategory).map(
                            ([category, categoryPermissions]) => {
                              const rolePermissionsInCategory =
                                categoryPermissions.filter((p) =>
                                  role.permissions.includes(p.id)
                                );

                              if (rolePermissionsInCategory.length === 0)
                                return null;

                              const IconComponent = getCategoryIcon(category);
                              return (
                                <div
                                  key={category}
                                  className="border rounded-lg p-3"
                                >
                                  <div className="flex items-center gap-2 mb-2">
                                    <IconComponent className="h-4 w-4 text-primary" />
                                    <span className="font-medium text-sm">
                                      {category}
                                    </span>
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {rolePermissionsInCategory.length}/
                                      {categoryPermissions.length}
                                    </Badge>
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {rolePermissionsInCategory.map(
                                      (permission) => (
                                        <Badge
                                          key={permission.id}
                                          variant="outline"
                                          className="text-xs"
                                          style={{
                                            backgroundColor: `${getActionColor(
                                              permission.action
                                            )}20`,
                                            color: getActionColor(
                                              permission.action
                                            ),
                                            borderColor: getActionColor(
                                              permission.action
                                            ),
                                          }}
                                        >
                                          {permission.action}
                                        </Badge>
                                      )
                                    )}
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Permission Dialog */}
      <Dialog
        open={isCreatePermissionOpen}
        onOpenChange={setIsCreatePermissionOpen}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Permission</DialogTitle>
            <DialogDescription>
              Define a new permission for the system. This will be available for
              assignment to roles.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="permission_name">Permission Name</Label>
                <Input
                  id="permission_name"
                  value={newPermission.name}
                  onChange={(e) =>
                    setNewPermission((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., View Dashboard"
                />
              </div>
              <div>
                <Label htmlFor="permission_category">Category</Label>
                <Input
                  id="permission_category"
                  value={newPermission.category}
                  onChange={(e) =>
                    setNewPermission((prev) => ({
                      ...prev,
                      category: e.target.value,
                    }))
                  }
                  placeholder="e.g., Dashboard"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="permission_resource">Resource</Label>
                <Input
                  id="permission_resource"
                  value={newPermission.resource}
                  onChange={(e) =>
                    setNewPermission((prev) => ({
                      ...prev,
                      resource: e.target.value,
                    }))
                  }
                  placeholder="e.g., dashboard"
                />
              </div>
              <div>
                <Label htmlFor="permission_action">Action</Label>
                <Select
                  value={newPermission.action}
                  onValueChange={(value) =>
                    setNewPermission((prev) => ({ ...prev, action: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="read">Read</SelectItem>
                    <SelectItem value="create">Create</SelectItem>
                    <SelectItem value="update">Update</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                    <SelectItem value="manage">Manage</SelectItem>
                    <SelectItem value="export">Export</SelectItem>
                    <SelectItem value="publish">Publish</SelectItem>
                    <SelectItem value="archive">Archive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="permission_description">Description</Label>
              <Textarea
                id="permission_description"
                value={newPermission.description}
                onChange={(e) =>
                  setNewPermission((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Describe what this permission allows users to do"
              />
            </div>
            <div>
              <Label htmlFor="permission_scope">Scope</Label>
              <Select
                value={newPermission.scope}
                onValueChange={(value) =>
                  setNewPermission((prev) => ({ ...prev, scope: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select scope" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="organization">Organization</SelectItem>
                  <SelectItem value="global">Global</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {newPermission.resource && newPermission.action && (
              <div className="p-3 bg-muted rounded-lg">
                <Label className="text-sm font-medium">
                  Generated Permission ID:
                </Label>
                <code className="block text-sm mt-1">
                  {formatPermissionId(
                    newPermission.resource,
                    newPermission.action
                  )}
                </code>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreatePermissionOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleCreatePermission}>Create Permission</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Permission Details Dialog */}
      <Dialog
        open={isPermissionDetailsOpen}
        onOpenChange={setIsPermissionDetailsOpen}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Permission Details</DialogTitle>
            <DialogDescription>
              Detailed information about this permission and its usage
            </DialogDescription>
          </DialogHeader>
          {selectedPermission && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Permission Name</Label>
                  <p className="text-sm mt-1">{selectedPermission.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {(() => {
                      const IconComponent = getCategoryIcon(
                        selectedPermission.category
                      );
                      return <IconComponent className="h-4 w-4" />;
                    })()}
                    <span className="text-sm">
                      {selectedPermission.category}
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Resource</Label>
                  <p className="text-sm mt-1">{selectedPermission.resource}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Action</Label>
                  <Badge
                    variant="outline"
                    className="mt-1"
                    style={{
                      backgroundColor: `${getActionColor(
                        selectedPermission.action
                      )}20`,
                      color: getActionColor(selectedPermission.action),
                      borderColor: getActionColor(selectedPermission.action),
                    }}
                  >
                    {selectedPermission.action}
                  </Badge>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Description</Label>
                <p className="text-sm mt-1 text-muted-foreground">
                  {selectedPermission.description}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Permission ID</Label>
                  <code className="block text-sm mt-1 bg-muted px-2 py-1 rounded">
                    {selectedPermission.id}
                  </code>
                </div>
                <div>
                  <Label className="text-sm font-medium">Scope</Label>
                  <Badge variant="secondary" className="mt-1">
                    {selectedPermission.scope}
                  </Badge>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">
                  Roles with this Permission
                </Label>
                <div className="mt-2">
                  {getRolesWithPermission(selectedPermission.id).length > 0 ? (
                    <div className="space-y-2">
                      {getRolesWithPermission(selectedPermission.id).map(
                        (role) => (
                          <div
                            key={role.id}
                            className="flex items-center gap-3 p-2 border rounded-lg"
                          >
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: role.color }}
                            />
                            <span className="font-medium">{role.name}</span>
                            <Badge variant="secondary" className="text-xs">
                              Level {role.level}
                            </Badge>
                            {role.is_system && (
                              <Badge variant="outline" className="text-xs">
                                System
                              </Badge>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No roles currently have this permission
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsPermissionDetailsOpen(false);
                setSelectedPermission(null);
              }}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
