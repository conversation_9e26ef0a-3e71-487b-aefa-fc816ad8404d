"use client"

import React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Star, Users, Code, Shield, Lightbulb, BarChart3, Plus, Check, Info, Zap, Target } from "lucide-react"
import {
  PERMISSION_TEMPLATES,
  QUICK_ROLE_PRESETS,
  PermissionTemplateService,
  type PermissionTemplate,
} from "../../lib/permission-templates"
import { PERMISSIONS, type Role } from "../../lib/rbac"

interface PermissionTemplatesProps {
  onApplyTemplate: (template: PermissionTemplate, roleName?: string) => void
  existingRoles: Role[]
  currentUserLevel: number
}

const categoryIcons = {
  business: Users,
  technical: Code,
  specialized: Shield,
}

const categoryColors = {
  business: "bg-blue-100 text-blue-800",
  technical: "bg-purple-100 text-purple-800",
  specialized: "bg-green-100 text-green-800",
}

export function PermissionTemplates({ onApplyTemplate, existingRoles, currentUserLevel }: PermissionTemplatesProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedLevel, setSelectedLevel] = useState("all")
  const [selectedTemplate, setSelectedTemplate] = useState<PermissionTemplate | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [isApplyOpen, setIsApplyOpen] = useState(false)
  const [customRoleName, setCustomRoleName] = useState("")
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null)

  const filteredTemplates = PERMISSION_TEMPLATES.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.useCases.some((useCase) => useCase.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory

    const matchesLevel =
      selectedLevel === "all" ||
      (selectedLevel === "low" && template.level <= 30) ||
      (selectedLevel === "medium" && template.level > 30 && template.level <= 70) ||
      (selectedLevel === "high" && template.level > 70)

    const canApply = template.level < currentUserLevel

    return matchesSearch && matchesCategory && matchesLevel && canApply
  })

  const recommendedTemplates = PermissionTemplateService.getRecommendedTemplates(existingRoles.map((role) => role.id))

  const templateStats = PermissionTemplateService.getTemplateStats()

  const handlePreviewTemplate = (template: PermissionTemplate) => {
    setSelectedTemplate(template)
    setIsPreviewOpen(true)
  }

  const handleApplyTemplate = (template: PermissionTemplate) => {
    setSelectedTemplate(template)
    setCustomRoleName(template.name)
    setIsApplyOpen(true)
  }

  const confirmApplyTemplate = () => {
    if (selectedTemplate) {
      onApplyTemplate(selectedTemplate, customRoleName || selectedTemplate.name)
      setIsApplyOpen(false)
      setCustomRoleName("")
      setSelectedTemplate(null)
    }
  }

  const handleApplyPreset = (presetName: string) => {
    const preset = QUICK_ROLE_PRESETS.find((p) => p.name === presetName)
    if (preset) {
      preset.templates.forEach((templateId) => {
        const template = PermissionTemplateService.getTemplateById(templateId)
        if (template) {
          onApplyTemplate(template)
        }
      })
      setSelectedPreset(null)
    }
  }

  const getPermissionsByCategory = (permissions: string[]) => {
    const userPermissions = PERMISSIONS.filter((p) => permissions.includes(p.id))
    return userPermissions.reduce(
      (acc, permission) => {
        if (!acc[permission.category]) {
          acc[permission.category] = []
        }
        acc[permission.category].push(permission)
        return acc
      },
      {} as Record<string, any[]>,
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Permission Templates</h3>
          <p className="text-sm text-muted-foreground">Pre-configured permission sets for common roles and use cases</p>
        </div>
        <Badge variant="secondary">{templateStats.totalTemplates} templates available</Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">Business</div>
                <div className="text-xs text-muted-foreground">{templateStats.byCategory.business || 0} templates</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-purple-600" />
              <div>
                <div className="text-sm font-medium">Technical</div>
                <div className="text-xs text-muted-foreground">{templateStats.byCategory.technical || 0} templates</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">Specialized</div>
                <div className="text-xs text-muted-foreground">
                  {templateStats.byCategory.specialized || 0} templates
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-orange-600" />
              <div>
                <div className="text-sm font-medium">Avg. Permissions</div>
                <div className="text-xs text-muted-foreground">{templateStats.averagePermissions} per template</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="browse" className="space-y-4">
        <TabsList>
          <TabsTrigger value="browse">Browse Templates</TabsTrigger>
          <TabsTrigger value="recommended">
            Recommended
            {recommendedTemplates.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {recommendedTemplates.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="presets">Quick Presets</TabsTrigger>
        </TabsList>

        {/* Browse Templates */}
        <TabsContent value="browse" className="space-y-4">
          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search templates..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="technical">Technical</SelectItem>
                <SelectItem value="specialized">Specialized</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedLevel} onValueChange={setSelectedLevel}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="low">Low (0-30)</SelectItem>
                <SelectItem value="medium">Medium (31-70)</SelectItem>
                <SelectItem value="high">High (71-100)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Templates Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map((template) => {
              const IconComponent = categoryIcons[template.category]
              const isExisting = existingRoles.some((role) => role.name === template.name)

              return (
                <Card key={template.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-8 h-8 rounded-lg flex items-center justify-center"
                          style={{ backgroundColor: `${template.color}20` }}
                        >
                          <IconComponent className="h-4 w-4" style={{ color: template.color }} />
                        </div>
                        <div>
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <Badge className={categoryColors[template.category]} variant="secondary">
                            {template.category}
                          </Badge>
                        </div>
                      </div>
                      {isExisting && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          <Check className="mr-1 h-3 w-3" />
                          Active
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="text-sm">{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Level</span>
                      <span className="font-medium">{template.level}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Permissions</span>
                      <span className="font-medium">{template.permissions.length}</span>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Common Use Cases:</div>
                      <div className="flex flex-wrap gap-1">
                        {template.useCases.slice(0, 2).map((useCase, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {useCase}
                          </Badge>
                        ))}
                        {template.useCases.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{template.useCases.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewTemplate(template)}
                        className="flex-1"
                      >
                        <Info className="mr-1 h-3 w-3" />
                        Preview
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleApplyTemplate(template)}
                        disabled={isExisting}
                        className="flex-1"
                      >
                        <Plus className="mr-1 h-3 w-3" />
                        {isExisting ? "Applied" : "Apply"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8">
              <div className="text-muted-foreground">No templates match your current filters</div>
              <Button
                variant="outline"
                className="mt-2"
                onClick={() => {
                  setSearchTerm("")
                  setSelectedCategory("all")
                  setSelectedLevel("all")
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </TabsContent>

        {/* Recommended Templates */}
        <TabsContent value="recommended" className="space-y-4">
          {recommendedTemplates.length > 0 ? (
            <>
              <Alert>
                <Lightbulb className="h-4 w-4" />
                <AlertDescription>
                  Based on your current roles, these templates might help fill gaps in your team structure.
                </AlertDescription>
              </Alert>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {recommendedTemplates.map((template) => {
                  const IconComponent = categoryIcons[template.category]
                  return (
                    <Card key={template.id} className="border-orange-200 bg-orange-50">
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-lg flex items-center justify-center"
                            style={{ backgroundColor: `${template.color}20` }}
                          >
                            <IconComponent className="h-4 w-4" style={{ color: template.color }} />
                          </div>
                          <div>
                            <CardTitle className="text-base flex items-center gap-2">
                              {template.name}
                              <Star className="h-4 w-4 text-orange-500" />
                            </CardTitle>
                            <Badge className={categoryColors[template.category]} variant="secondary">
                              {template.category}
                            </Badge>
                          </div>
                        </div>
                        <CardDescription className="text-sm">{template.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="text-sm">
                          <div className="font-medium mb-1">Why recommended:</div>
                          <div className="text-muted-foreground">
                            Fills level {template.level} gap in your current role hierarchy
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreviewTemplate(template)}
                            className="flex-1"
                          >
                            Preview
                          </Button>
                          <Button size="sm" onClick={() => handleApplyTemplate(template)} className="flex-1">
                            Apply
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <div className="text-lg font-medium">No recommendations available</div>
              <div className="text-muted-foreground">Your current role structure looks comprehensive!</div>
            </div>
          )}
        </TabsContent>

        {/* Quick Presets */}
        <TabsContent value="presets" className="space-y-4">
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertDescription>Quick presets create multiple roles at once for common team structures.</AlertDescription>
          </Alert>
          <div className="grid gap-4 md:grid-cols-2">
            {QUICK_ROLE_PRESETS.map((preset) => (
              <Card key={preset.name}>
                <CardHeader>
                  <CardTitle className="text-lg">{preset.name}</CardTitle>
                  <CardDescription>{preset.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="text-sm font-medium mb-2">Includes {preset.templates.length} roles:</div>
                    <div className="space-y-1">
                      {preset.templates.map((templateId) => {
                        const template = PermissionTemplateService.getTemplateById(templateId)
                        if (!template) return null
                        return (
                          <div key={templateId} className="flex items-center gap-2 text-sm">
                            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: template.color }} />
                            {template.name}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                  <Button onClick={() => handleApplyPreset(preset.name)} className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Apply Preset
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Template Preview Modal */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedTemplate && (
                <>
                  <div
                    className="w-6 h-6 rounded flex items-center justify-center"
                    style={{ backgroundColor: `${selectedTemplate.color}20` }}
                  >
                    {React.createElement(categoryIcons[selectedTemplate.category], {
                      className: "h-4 w-4",
                      style: { color: selectedTemplate.color },
                    })}
                  </div>
                  {selectedTemplate.name}
                </>
              )}
            </DialogTitle>
            <DialogDescription>{selectedTemplate?.description}</DialogDescription>
          </DialogHeader>

          {selectedTemplate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="mt-1">
                    <Badge className={categoryColors[selectedTemplate.category]} variant="secondary">
                      {selectedTemplate.category}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Access Level</Label>
                  <div className="mt-1 text-sm">{selectedTemplate.level}</div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Suggested Role Names</Label>
                <div className="mt-1 flex flex-wrap gap-1">
                  {selectedTemplate.suggestedRoles.map((role, index) => (
                    <Badge key={index} variant="outline">
                      {role}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Common Use Cases</Label>
                <ul className="mt-1 text-sm text-muted-foreground space-y-1">
                  {selectedTemplate.useCases.map((useCase, index) => (
                    <li key={index}>• {useCase}</li>
                  ))}
                </ul>
              </div>

              <div>
                <Label className="text-sm font-medium">Permissions ({selectedTemplate.permissions.length})</Label>
                <div className="mt-2 space-y-3 max-h-60 overflow-y-auto">
                  {Object.entries(getPermissionsByCategory(selectedTemplate.permissions)).map(
                    ([category, permissions]) => (
                      <div key={category}>
                        <div className="font-medium text-sm mb-1">{category}</div>
                        <div className="grid grid-cols-1 gap-1 ml-4">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="text-sm text-muted-foreground">
                              • {permission.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                setIsPreviewOpen(false)
                if (selectedTemplate) handleApplyTemplate(selectedTemplate)
              }}
            >
              Apply Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Apply Template Modal */}
      <Dialog open={isApplyOpen} onOpenChange={setIsApplyOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Apply Permission Template</DialogTitle>
            <DialogDescription>Create a new role based on the selected template</DialogDescription>
          </DialogHeader>

          {selectedTemplate && (
            <div className="space-y-4">
              <div className="p-3 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-6 h-6 rounded flex items-center justify-center"
                    style={{ backgroundColor: `${selectedTemplate.color}20` }}
                  >
                    {React.createElement(categoryIcons[selectedTemplate.category], {
                      className: "h-4 w-4",
                      style: { color: selectedTemplate.color },
                    })}
                  </div>
                  <div className="font-medium">{selectedTemplate.name}</div>
                </div>
                <div className="text-sm text-muted-foreground">{selectedTemplate.description}</div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="customRoleName">Role Name</Label>
                <Input
                  id="customRoleName"
                  value={customRoleName}
                  onChange={(e) => setCustomRoleName(e.target.value)}
                  placeholder="Enter custom role name"
                />
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This will create a new role with {selectedTemplate.permissions.length} permissions based on the{" "}
                  {selectedTemplate.name} template.
                </AlertDescription>
              </Alert>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApplyOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmApplyTemplate} disabled={!customRoleName.trim()}>
              Create Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
