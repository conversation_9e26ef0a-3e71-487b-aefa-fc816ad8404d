"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { usePermissions, PermissionGate } from "@/hooks/use-permissions";
import { User<PERSON><PERSON>, User } from "@/lib/api/users";
import { <PERSON><PERSON><PERSON>, Role } from "@/lib/api/roles";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  Users,
  Search,
  MoreHorizontal,
  Loader2,
  Mail,
  Shield,
  Clock,
  UserCheck,
  UserX,
  Crown,
  Filter,
} from "lucide-react";
import { toast } from "sonner";

interface UserManagementProps {}

export function UserManagement({}: UserManagementProps) {
  const { token } = useAuth();
  const { hasPermission } = usePermissions();

  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);

  // Dialog states
  const [isInviteUserOpen, setIsInviteUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [isDeleteUserOpen, setIsDeleteUserOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);

  // Form states
  const [inviteForm, setInviteForm] = useState({
    email: "",
    first_name: "",
    last_name: "",
    role_id: "",
  });

  const [editForm, setEditForm] = useState({
    first_name: "",
    last_name: "",
    role_id: "",
    is_active: true,
  });

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Load data
  const loadData = async () => {
    if (!token) return;

    try {
      setIsLoading(true);

      const [usersData, rolesData] = await Promise.all([
        UserAPI.getUsers(token),
        RoleAPI.getRoles(token),
      ]);

      setUsers(usersData);
      setRoles(rolesData);
    } catch (error: any) {
      console.error("Failed to load user management data:", error);
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [token]);

  // Invite user
  const handleInviteUser = async () => {
    if (
      !token ||
      !inviteForm.email.trim() ||
      !inviteForm.first_name.trim() ||
      !inviteForm.last_name.trim()
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const invitationData = {
        email: inviteForm.email,
        first_name: inviteForm.first_name,
        last_name: inviteForm.last_name,
        role_id: inviteForm.role_id || undefined,
        invitation_message: `Welcome to our organization! You've been invited to join as ${
          inviteForm.role_id ? getRoleName(inviteForm.role_id) : "a team member"
        }.`,
      };

      const response = await UserAPI.inviteUser(token, invitationData);

      if (response.email_sent) {
        toast.success(`Invitation sent successfully to ${response.email}!`);
      } else {
        toast.success(
          "Invitation created successfully, but email could not be sent. Please contact the user directly."
        );
      }

      setInviteForm({
        email: "",
        first_name: "",
        last_name: "",
        role_id: "",
      });
      setIsInviteUserOpen(false);
      loadData();
    } catch (error: any) {
      console.error("Failed to send invitation:", error);
      toast.error(error.detail || "Failed to send invitation");
    }
  };

  // Update user
  const handleUpdateUser = async () => {
    if (!token || !selectedUser) return;

    try {
      const updateData = {
        first_name: editForm.first_name,
        last_name: editForm.last_name,
        role_id: editForm.role_id || undefined,
      };

      await UserAPI.updateUser(token, selectedUser.id, updateData);
      toast.success("User updated successfully!");

      setIsEditUserOpen(false);
      setSelectedUser(null);
      loadData();
    } catch (error: any) {
      console.error("Failed to update user:", error);
      toast.error(error.detail || "Failed to update user");
    }
  };

  // Delete user
  const handleDeleteUser = async () => {
    if (!token || !selectedUser || deletingUserId) return;

    try {
      setDeletingUserId(selectedUser.id);
      await UserAPI.deleteUser(token, selectedUser.id);

      toast.success("User deleted successfully!");
      setIsDeleteUserOpen(false);
      setSelectedUser(null);
      loadData();
    } catch (error: any) {
      console.error("Failed to delete user:", error);
      toast.error(error.detail || "Failed to delete user");
    } finally {
      setDeletingUserId(null);
    }
  };

  // Assign role to user
  const handleAssignRole = async (userId: string, roleId: string) => {
    if (!token) return;

    try {
      await UserAPI.assignRole(token, userId, roleId);
      toast.success("Role assigned successfully!");
      loadData();
    } catch (error: any) {
      console.error("Failed to assign role:", error);
      toast.error(error.detail || "Failed to assign role");
    }
  };

  // Filter users
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.first_name} ${user.last_name}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesRole =
      roleFilter === "all" ||
      (roleFilter === "no-role" && !user.role_id) ||
      user.role_id === roleFilter;

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && user.is_active) ||
      (statusFilter === "inactive" && !user.is_active) ||
      (statusFilter === "superuser" && user.is_superuser);

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Get role name for user
  const getRoleName = (roleId: string | null) => {
    if (!roleId) return "No Role";
    const role = roles.find((r) => r.id === roleId);
    return role?.name || "Unknown Role";
  };

  // Get role color for user
  const getRoleColor = (roleId: string | null) => {
    if (!roleId) return "#6b7280";
    const role = roles.find((r) => r.id === roleId);
    return role?.color || "#6b7280";
  };

  // Get user initials
  const getUserInitials = (user: User) => {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(
      0
    )}`.toUpperCase();
  };

  // Format last login
  const formatLastLogin = (lastLogin: string | null) => {
    if (!lastLogin) return "Never";
    return new Date(lastLogin).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading users...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">User Management</h2>
          <p className="text-muted-foreground">
            Manage users, roles, and access permissions
          </p>
        </div>
        <PermissionGate permissions="users:create">
          <Button onClick={() => setIsInviteUserOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Invite User
          </Button>
        </PermissionGate>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organization Users</CardTitle>
              <CardDescription>
                Manage team members and their access
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="no-role">No Role</SelectItem>
                {roles
                  .filter((role) => role.id && role.id.trim() !== "")
                  .map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="superuser">Super Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src=""
                            alt={`${user.first_name} ${user.last_name}`}
                          />
                          <AvatarFallback className="text-xs">
                            {getUserInitials(user)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {user.first_name} {user.last_name}
                            {user.is_superuser && (
                              <Crown className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.role_id ? (
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: `${getRoleColor(user.role_id)}20`,
                            color: getRoleColor(user.role_id),
                          }}
                        >
                          {getRoleName(user.role_id)}
                        </Badge>
                      ) : (
                        <Badge variant="outline">No Role</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {user.is_active ? (
                          <UserCheck className="h-4 w-4 text-green-500" />
                        ) : (
                          <UserX className="h-4 w-4 text-red-500" />
                        )}
                        <span
                          className={
                            user.is_active ? "text-green-600" : "text-red-600"
                          }
                        >
                          {user.is_active ? "Active" : "Inactive"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {formatLastLogin(user.last_login)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <PermissionGate
                        permissions={["users:update", "users:delete"]}
                        mode="any"
                      >
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <PermissionGate permissions="users:update">
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedUser(user);
                                  setEditForm({
                                    first_name: user.first_name,
                                    last_name: user.last_name,
                                    role_id: user.role_id || "",
                                    is_active: user.is_active,
                                  });
                                  setIsEditUserOpen(true);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit User
                              </DropdownMenuItem>
                            </PermissionGate>
                            <PermissionGate permissions="users:delete">
                              {!user.is_superuser && (
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setIsDeleteUserOpen(true);
                                  }}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              )}
                            </PermissionGate>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </PermissionGate>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No users found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || roleFilter !== "all" || statusFilter !== "all"
                  ? "Try adjusting your filters"
                  : "Get started by inviting your first user"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invite User Dialog */}
      <Dialog open={isInviteUserOpen} onOpenChange={setIsInviteUserOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Invite New User</DialogTitle>
            <DialogDescription>
              Send an invitation to a new team member. They will receive an
              email with login instructions.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={inviteForm.first_name}
                  onChange={(e) =>
                    setInviteForm((prev) => ({
                      ...prev,
                      first_name: e.target.value,
                    }))
                  }
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={inviteForm.last_name}
                  onChange={(e) =>
                    setInviteForm((prev) => ({
                      ...prev,
                      last_name: e.target.value,
                    }))
                  }
                  placeholder="Enter last name"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={inviteForm.email}
                onChange={(e) =>
                  setInviteForm((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter email address"
              />
            </div>
            <div>
              <Label htmlFor="role">Role (Optional)</Label>
              <Select
                value={inviteForm.role_id}
                onValueChange={(value) =>
                  setInviteForm((prev) => ({ ...prev, role_id: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No Role</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsInviteUserOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleInviteUser}>
              <Mail className="mr-2 h-4 w-4" />
              Send Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and role assignment.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit_first_name">First Name</Label>
                  <Input
                    id="edit_first_name"
                    value={editForm.first_name}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        first_name: e.target.value,
                      }))
                    }
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <Label htmlFor="edit_last_name">Last Name</Label>
                  <Input
                    id="edit_last_name"
                    value={editForm.last_name}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        last_name: e.target.value,
                      }))
                    }
                    placeholder="Enter last name"
                  />
                </div>
              </div>
              <div>
                <Label>Email Address</Label>
                <Input
                  value={selectedUser.email}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Email cannot be changed
                </p>
              </div>
              <div>
                <Label htmlFor="edit_role">Role</Label>
                <Select
                  value={editForm.role_id}
                  onValueChange={(value) =>
                    setEditForm((prev) => ({ ...prev, role_id: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Role</SelectItem>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsEditUserOpen(false);
                setSelectedUser(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateUser}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteUserOpen} onOpenChange={setIsDeleteUserOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <div className="bg-muted rounded-lg p-4">
                <h4 className="font-medium text-sm mb-2">
                  User to be deleted:
                </h4>
                <div className="space-y-1">
                  <p className="font-medium">
                    {selectedUser.first_name} {selectedUser.last_name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {selectedUser.email}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Role:</span>{" "}
                    {getRoleName(selectedUser.role_id)}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteUserOpen(false);
                setSelectedUser(null);
              }}
              disabled={deletingUserId !== null}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
              disabled={deletingUserId !== null}
            >
              {deletingUserId ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
