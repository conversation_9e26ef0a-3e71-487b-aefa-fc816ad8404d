"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { usePermissions, PermissionGate } from "@/hooks/use-permissions";
import { User<PERSON><PERSON>, User, PendingInvitation } from "@/lib/api/users";
import { <PERSON><PERSON><PERSON>, Role } from "@/lib/api/roles";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  Users,
  Search,
  MoreHorizontal,
  Loader2,
  Mail,
  Shield,
  Clock,
  UserCheck,
  UserX,
  Crown,
  Filter,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UserManagementProps {}

export function UserManagement({}: UserManagementProps) {
  const { token } = useAuth();
  const { hasPermission } = usePermissions();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<PendingInvitation[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);

  // Dialog states
  const [isInviteUserOpen, setIsInviteUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [isDeleteUserOpen, setIsDeleteUserOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);

  // Form states
  const [inviteForm, setInviteForm] = useState({
    email: "",
    first_name: "",
    last_name: "",
    role_id: "no-role",
  });

  const [editForm, setEditForm] = useState({
    first_name: "",
    last_name: "",
    role_id: "no-role",
    is_active: true,
  });

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Load data
  const loadData = async () => {
    if (!token) return;

    try {
      setIsLoading(true);

      const [usersData, pendingInvitationsData, rolesData] = await Promise.all([
        UserAPI.getUsers(token),
        UserAPI.getPendingInvitations(token),
        RoleAPI.getRoles(token),
      ]);

      setUsers(usersData);
      setPendingInvitations(pendingInvitationsData);
      setRoles(rolesData);
    } catch (error: any) {
      console.error("Failed to load user management data:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load data",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [token]);

  // Invite user
  const handleInviteUser = async () => {
    if (
      !token ||
      !inviteForm.email.trim() ||
      !inviteForm.first_name.trim() ||
      !inviteForm.last_name.trim()
    ) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill in all required fields",
      });
      return;
    }

    try {
      const invitationData = {
        email: inviteForm.email,
        first_name: inviteForm.first_name,
        last_name: inviteForm.last_name,
        role_id: inviteForm.role_id && inviteForm.role_id !== "no-role" ? inviteForm.role_id : undefined,
        invitation_message: `Welcome to our organization! You've been invited to join as ${
          inviteForm.role_id && inviteForm.role_id !== "no-role" ? getRoleName(inviteForm.role_id) : "a team member"
        }.`,
      };

      const response = await UserAPI.inviteUser(token, invitationData);

      if (response.email_sent) {
        toast({
          title: "Success",
          description: `Invitation sent successfully to ${response.email}!`,
        });
      } else {
        toast({
          title: "Invitation Created",
          description: "Invitation created successfully, but email could not be sent. Please contact the user directly.",
        });
      }

      setInviteForm({
        email: "",
        first_name: "",
        last_name: "",
        role_id: "no-role",
      });
      setIsInviteUserOpen(false);
      loadData();
    } catch (error: any) {
      console.error("Failed to send invitation:", error);

      // Handle specific error cases using error codes
      const errorCode = error.error_code;

      if (errorCode) {
        switch (errorCode) {
          case "INVITATION_ALREADY_EXISTS":
            toast({
              variant: "destructive",
              title: "Invitation Already Exists",
              description: `An invitation has already been sent to ${inviteForm.email}. Please check if they received it or wait for them to accept it.`,
            });
            break;

          case "USER_EMAIL_ALREADY_EXISTS":
            toast({
              variant: "destructive",
              title: "User Already Exists",
              description: `A user with email ${inviteForm.email} already exists in the system. They may already be a member of your organization.`,
            });
            break;

          case "ROLE_NOT_FOUND":
            toast({
              variant: "destructive",
              title: "Role Not Found",
              description: "The selected role is no longer available. Please refresh the page and try again.",
            });
            break;

          case "ORGANIZATION_NOT_FOUND":
            toast({
              variant: "destructive",
              title: "Organization Not Found",
              description: "Organization not found. Please refresh the page and try again.",
            });
            break;

          default:
            toast({
              variant: "destructive",
              title: "Error",
              description: error.detail || "Failed to send invitation. Please try again.",
            });
            break;
        }
      } else {
        // Fallback for errors without error codes
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to send invitation. Please try again.",
        });
      }
    }
  };

  // Update user
  const handleUpdateUser = async () => {
    if (!token || !selectedUser) return;

    try {
      const updateData = {
        first_name: editForm.first_name,
        last_name: editForm.last_name,
        role_id: editForm.role_id && editForm.role_id !== "no-role" ? editForm.role_id : undefined,
      };

      await UserAPI.updateUser(token, selectedUser.id, updateData);
      toast({
        title: "Success",
        description: "User updated successfully!",
      });

      setIsEditUserOpen(false);
      setSelectedUser(null);
      loadData();
    } catch (error: any) {
      console.error("Failed to update user:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.detail || "Failed to update user",
      });
    }
  };

  // Delete user
  const handleDeleteUser = async () => {
    if (!token || !selectedUser || deletingUserId) return;

    try {
      setDeletingUserId(selectedUser.id);
      await UserAPI.deleteUser(token, selectedUser.id);

      toast({
        title: "Success",
        description: "User deleted successfully!",
      });
      setIsDeleteUserOpen(false);
      setSelectedUser(null);
      loadData();
    } catch (error: any) {
      console.error("Failed to delete user:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.detail || "Failed to delete user",
      });
    } finally {
      setDeletingUserId(null);
    }
  };

  // Assign role to user
  const handleAssignRole = async (userId: string, roleId: string) => {
    if (!token) return;

    try {
      await UserAPI.assignRole(token, userId, roleId);
      toast({
        title: "Success",
        description: "Role assigned successfully!",
      });
      loadData();
    } catch (error: any) {
      console.error("Failed to assign role:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.detail || "Failed to assign role",
      });
    }
  };

  // Combined user and invitation data for display
  type DisplayUser = (User & { type: 'user' }) | (PendingInvitation & {
    type: 'invitation';
    is_active: boolean;
    is_superuser: boolean;
    last_login: string | null;
    created_at: string;
    updated_at: string;
    organization_id: string;
    role_id: string | null;
  });

  const getCombinedUserList = (): DisplayUser[] => {
    const userList: DisplayUser[] = users.map(user => ({ ...user, type: 'user' as const }));
    const invitationList: DisplayUser[] = pendingInvitations.map(invitation => ({
      ...invitation,
      type: 'invitation' as const,
      // Map invitation fields to user-like structure for consistent display
      is_active: false, // Invitations are not active users yet
      is_superuser: false,
      last_login: null,
      created_at: invitation.created_at,
      updated_at: invitation.created_at, // Use created_at as updated_at
      organization_id: '', // Will be filled from context
      role_id: null, // Invitations don't have direct role_id, we'll use role_name
    }));

    return [...userList, ...invitationList];
  };

  // Filter combined users and invitations
  const filteredUsers = getCombinedUserList().filter((item) => {
    const matchesSearch =
      item.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${item.first_name} ${item.last_name}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesRole =
      roleFilter === "all" ||
      (roleFilter === "no-role" && !item.role_id) ||
      item.role_id === roleFilter;

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && item.is_active) ||
      (statusFilter === "inactive" && !item.is_active) ||
      (statusFilter === "superuser" && item.is_superuser) ||
      (statusFilter === "pending" && item.type === "invitation");

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Get role name for user
  const getRoleName = (roleId: string | null) => {
    if (!roleId) return "No Role";
    const role = roles.find((r) => r.id === roleId);
    return role?.name || "Unknown Role";
  };

  // Get role color for user
  const getRoleColor = (roleId: string | null) => {
    if (!roleId) return "#6b7280";
    const role = roles.find((r) => r.id === roleId);
    return role?.color || "#6b7280";
  };

  // Get user initials
  const getUserInitials = (user: User) => {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(
      0
    )}`.toUpperCase();
  };

  // Format last login
  const formatLastLogin = (lastLogin: string | null) => {
    if (!lastLogin) return "Never";
    return new Date(lastLogin).toLocaleDateString();
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading users...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">User Management</h2>
          <p className="text-muted-foreground">
            Manage users, roles, and access permissions
          </p>
        </div>
        <PermissionGate permissions="users:create">
          <Button onClick={() => setIsInviteUserOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Invite User
          </Button>
        </PermissionGate>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organization Users</CardTitle>
              <CardDescription>
                Manage team members and their access
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="no-role">No Role</SelectItem>
                {roles
                  .filter((role) => role.id && role.id.trim() !== "")
                  .map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="superuser">Super Admin</SelectItem>
                <SelectItem value="pending">Pending Invitations</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((item) => (
                  <TableRow key={item.id} className={item.type === 'invitation' ? 'bg-blue-50/50 dark:bg-blue-950/20' : ''}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src=""
                            alt={`${item.first_name} ${item.last_name}`}
                          />
                          <AvatarFallback className="text-xs">
                            {getUserInitials(item as User)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {item.first_name} {item.last_name}
                            {item.type === 'invitation' && (
                              <Badge variant="outline" className="text-xs">
                                <Mail className="h-3 w-3 mr-1" />
                                Invited
                              </Badge>
                            )}
                            {item.type === 'user' && item.is_superuser && (
                              <Crown className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {item.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.type === 'invitation' ? (
                        <Badge variant="outline" className="text-blue-600">
                          {(item as PendingInvitation).role_name || "No Role"}
                        </Badge>
                      ) : item.role_id ? (
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: `${getRoleColor(item.role_id)}20`,
                            color: getRoleColor(item.role_id),
                          }}
                        >
                          {getRoleName(item.role_id)}
                        </Badge>
                      ) : (
                        <Badge variant="outline">No Role</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {item.type === 'invitation' ? (
                          <>
                            <Mail className="h-4 w-4 text-blue-500" />
                            <span className="text-sm text-blue-600">Pending</span>
                          </>
                        ) : item.is_active ? (
                          <>
                            <UserCheck className="h-4 w-4 text-green-500" />
                            <span className="text-green-600">Active</span>
                          </>
                        ) : (
                          <>
                            <UserX className="h-4 w-4 text-red-500" />
                            <span className="text-red-600">Inactive</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {item.type === 'invitation' ? 'Never' : formatLastLogin(item.last_login)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {item.type === 'invitation' ? (
                        <PermissionGate permissions="users:create">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              try {
                                await UserAPI.cancelInvitation(token!, item.id);
                                toast({
                                  title: "Success",
                                  description: "Invitation cancelled successfully",
                                });
                                loadData();
                              } catch (error: any) {
                                toast({
                                  variant: "destructive",
                                  title: "Error",
                                  description: error.detail || "Failed to cancel invitation",
                                });
                              }
                            }}
                          >
                            Cancel Invitation
                          </Button>
                        </PermissionGate>
                      ) : (
                        <PermissionGate
                          permissions={["users:update", "users:delete"]}
                          mode="any"
                        >
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <PermissionGate permissions="users:update">
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedUser(item as User);
                                  setEditForm({
                                    first_name: item.first_name,
                                    last_name: item.last_name,
                                    role_id: item.role_id || "no-role",
                                    is_active: item.is_active,
                                  });
                                  setIsEditUserOpen(true);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit User
                              </DropdownMenuItem>
                            </PermissionGate>
                            <PermissionGate permissions="users:delete">
                              {item.type === 'user' && !item.is_superuser && (
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => {
                                    setSelectedUser(item as User);
                                    setIsDeleteUserOpen(true);
                                  }}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              )}
                            </PermissionGate>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </PermissionGate>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No users found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || roleFilter !== "all" || statusFilter !== "all"
                  ? "Try adjusting your filters"
                  : "Get started by inviting your first user"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invite User Dialog */}
      <Dialog open={isInviteUserOpen} onOpenChange={setIsInviteUserOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Invite New User</DialogTitle>
            <DialogDescription>
              Send an invitation to a new team member. They will receive an
              email with login instructions.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={inviteForm.first_name}
                  onChange={(e) =>
                    setInviteForm((prev) => ({
                      ...prev,
                      first_name: e.target.value,
                    }))
                  }
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={inviteForm.last_name}
                  onChange={(e) =>
                    setInviteForm((prev) => ({
                      ...prev,
                      last_name: e.target.value,
                    }))
                  }
                  placeholder="Enter last name"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={inviteForm.email}
                onChange={(e) =>
                  setInviteForm((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter email address"
              />
            </div>
            <div>
              <Label htmlFor="role">Role (Optional)</Label>
              <Select
                value={inviteForm.role_id}
                onValueChange={(value) =>
                  setInviteForm((prev) => ({ ...prev, role_id: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-role">No Role</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsInviteUserOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleInviteUser}>
              <Mail className="mr-2 h-4 w-4" />
              Send Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and role assignment.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit_first_name">First Name</Label>
                  <Input
                    id="edit_first_name"
                    value={editForm.first_name}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        first_name: e.target.value,
                      }))
                    }
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <Label htmlFor="edit_last_name">Last Name</Label>
                  <Input
                    id="edit_last_name"
                    value={editForm.last_name}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        last_name: e.target.value,
                      }))
                    }
                    placeholder="Enter last name"
                  />
                </div>
              </div>
              <div>
                <Label>Email Address</Label>
                <Input
                  value={selectedUser.email}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Email cannot be changed
                </p>
              </div>
              <div>
                <Label htmlFor="edit_role">Role</Label>
                <Select
                  value={editForm.role_id}
                  onValueChange={(value) =>
                    setEditForm((prev) => ({ ...prev, role_id: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-role">No Role</SelectItem>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsEditUserOpen(false);
                setSelectedUser(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateUser}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteUserOpen} onOpenChange={setIsDeleteUserOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <div className="bg-muted rounded-lg p-4">
                <h4 className="font-medium text-sm mb-2">
                  User to be deleted:
                </h4>
                <div className="space-y-1">
                  <p className="font-medium">
                    {selectedUser.first_name} {selectedUser.last_name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {selectedUser.email}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Role:</span>{" "}
                    {getRoleName(selectedUser.role_id)}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteUserOpen(false);
                setSelectedUser(null);
              }}
              disabled={deletingUserId !== null}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
              disabled={deletingUserId !== null}
            >
              {deletingUserId ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
