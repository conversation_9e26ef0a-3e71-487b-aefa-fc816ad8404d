"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { usePermissions, PermissionGate } from "@/hooks/use-permissions";
import { Role<PERSON><PERSON>, Role } from "@/lib/api/roles";
import { <PERSON><PERSON><PERSON><PERSON>, User } from "@/lib/api/users";
import { PermissionAPI, Permission } from "@/lib/api/permissions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  Shield,
  Users,
  Eye,
  Settings,
  UserCheck,
  UserX,
  Copy,
  Search,
  MoreHorizontal,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

interface RoleManagementProps {
  currentUserRole?: { level: number };
}

export function RoleManagement({ currentUserRole }: RoleManagementProps) {
  const { token } = useAuth();
  const { hasPermission } = usePermissions();
  
  const [activeTab, setActiveTab] = useState("roles");
  const [isLoading, setIsLoading] = useState(true);
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  
  // Dialog states
  const [isCreateRoleOpen, setIsCreateRoleOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [isDeleteRoleOpen, setIsDeleteRoleOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null);
  
  // Form states
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
    color: "#6b7280",
    level: 40,
  });
  
  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");

  // Load data
  const loadData = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      
      const [rolesData, usersData, permissionsData] = await Promise.all([
        RoleAPI.getRoles(token),
        hasPermission("users:read") ? UserAPI.getUsers(token) : Promise.resolve([]),
        PermissionAPI.getPermissions(token),
      ]);

      setRoles(rolesData);
      setUsers(usersData);
      setPermissions(permissionsData);
    } catch (error: any) {
      console.error("Failed to load role management data:", error);
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [token]);

  // Create role
  const handleCreateRole = async () => {
    if (!token || !newRole.name.trim()) return;

    try {
      const roleData = {
        name: newRole.name,
        description: newRole.description,
        permissions: newRole.permissions,
        color: newRole.color,
        level: newRole.level,
      };

      await RoleAPI.createRole(token, roleData);
      toast.success("Role created successfully!");
      
      setNewRole({
        name: "",
        description: "",
        permissions: [],
        color: "#6b7280",
        level: 40,
      });
      setIsCreateRoleOpen(false);
      loadData();
    } catch (error: any) {
      console.error("Failed to create role:", error);
      toast.error(error.detail || "Failed to create role");
    }
  };

  // Delete role
  const handleDeleteRole = async () => {
    if (!token || !selectedRole || deletingRoleId) return;

    try {
      setDeletingRoleId(selectedRole.id);
      await RoleAPI.deleteRole(token, selectedRole.id);
      
      toast.success("Role deleted successfully!");
      setIsDeleteRoleOpen(false);
      setSelectedRole(null);
      loadData();
    } catch (error: any) {
      console.error("Failed to delete role:", error);
      toast.error(error.detail || "Failed to delete role");
    } finally {
      setDeletingRoleId(null);
    }
  };

  // Clone role
  const handleCloneRole = async (role: Role) => {
    if (!token) return;

    try {
      const cloneName = `${role.name} (Copy)`;
      await RoleAPI.cloneRole(token, role.id, {
        name: cloneName,
        description: `Copy of ${role.name}`,
      });
      
      toast.success("Role cloned successfully!");
      loadData();
    } catch (error: any) {
      console.error("Failed to clone role:", error);
      toast.error(error.detail || "Failed to clone role");
    }
  };

  // Filter roles
  const filteredRoles = roles.filter((role) => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = roleFilter === "all" || 
                         (roleFilter === "system" && role.is_system) ||
                         (roleFilter === "custom" && !role.is_system);
    return matchesSearch && matchesFilter;
  });

  // Get user count for role
  const getUserCountForRole = (roleId: string) => {
    return users.filter(user => user.role_id === roleId).length;
  };

  // Permission categories
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading role management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Role Management</h2>
          <p className="text-muted-foreground">
            Manage roles and permissions for your organization
          </p>
        </div>
        <PermissionGate permissions="roles:create">
          <Button onClick={() => setIsCreateRoleOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Role
          </Button>
        </PermissionGate>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles ({roles.length})
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Permissions ({permissions.length})
          </TabsTrigger>
        </TabsList>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Organization Roles</CardTitle>
                  <CardDescription>Manage roles and their permissions</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex items-center gap-4 mb-6">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search roles..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="system">System Roles</SelectItem>
                    <SelectItem value="custom">Custom Roles</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredRoles.map((role) => (
                  <Card key={role.id} className="relative">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: role.color }}
                          />
                          <CardTitle className="text-lg">{role.name}</CardTitle>
                          {role.is_system && (
                            <Badge variant="secondary" className="text-xs">
                              System
                            </Badge>
                          )}
                        </div>
                        <PermissionGate permissions={["roles:update", "roles:delete"]} mode="any">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <PermissionGate permissions="roles:update">
                                <DropdownMenuItem onClick={() => handleCloneRole(role)}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Clone
                                </DropdownMenuItem>
                              </PermissionGate>
                              <PermissionGate permissions="roles:delete">
                                {!role.is_system && (
                                  <DropdownMenuItem
                                    className="text-red-600"
                                    onClick={() => {
                                      setSelectedRole(role);
                                      setIsDeleteRoleOpen(true);
                                    }}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                )}
                              </PermissionGate>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </PermissionGate>
                      </div>
                      <CardDescription>{role.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Level</span>
                          <span className="font-medium">{role.level}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Permissions</span>
                          <span className="font-medium">{role.permissions.length}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Users</span>
                          <span className="font-medium">{getUserCountForRole(role.id)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Permissions</CardTitle>
              <CardDescription>
                All available permissions organized by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                  <div key={category}>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      {category} ({categoryPermissions.length})
                    </h3>
                    <div className="grid gap-3 md:grid-cols-2">
                      {categoryPermissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="flex items-start gap-3 p-3 border rounded-lg"
                        >
                          <div className="flex-1">
                            <div className="font-medium text-sm">{permission.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {permission.description}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              <code className="bg-muted px-1 rounded">{permission.id}</code>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Role Dialog */}
      <Dialog open={isCreateRoleOpen} onOpenChange={setIsCreateRoleOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
            <DialogDescription>
              Create a new role with specific permissions for your organization.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Role Name</Label>
                <Input
                  id="name"
                  value={newRole.name}
                  onChange={(e) => setNewRole(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter role name"
                />
              </div>
              <div>
                <Label htmlFor="level">Level</Label>
                <Input
                  id="level"
                  type="number"
                  value={newRole.level}
                  onChange={(e) => setNewRole(prev => ({ ...prev, level: parseInt(e.target.value) || 40 }))}
                  placeholder="40"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newRole.description}
                onChange={(e) => setNewRole(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe this role's purpose and responsibilities"
              />
            </div>
            <div>
              <Label>Color</Label>
              <Input
                type="color"
                value={newRole.color}
                onChange={(e) => setNewRole(prev => ({ ...prev, color: e.target.value }))}
                className="w-20 h-10"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateRoleOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateRole} disabled={!newRole.name.trim()}>
              Create Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Role Dialog */}
      <Dialog open={isDeleteRoleOpen} onOpenChange={setIsDeleteRoleOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Role</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this role? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedRole && (
            <div className="py-4">
              <div className="bg-muted rounded-lg p-4">
                <h4 className="font-medium text-sm mb-2">Role to be deleted:</h4>
                <div className="space-y-1">
                  <p className="font-medium">{selectedRole.name}</p>
                  <p className="text-sm text-muted-foreground">{selectedRole.description}</p>
                  <p className="text-sm">
                    <span className="font-medium">Users:</span> {getUserCountForRole(selectedRole.id)}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteRoleOpen(false);
                setSelectedRole(null);
              }}
              disabled={deletingRoleId !== null}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteRole}
              disabled={deletingRoleId !== null}
            >
              {deletingRoleId ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Delete Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
