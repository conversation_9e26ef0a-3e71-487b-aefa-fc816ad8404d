/**
 * Roles API service
 */

import { apiClient } from './client';

export interface Role {
  id: string;
  name: string;
  description?: string;
  level: number;
  color: string;
  permissions: string[];
  is_system: boolean;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

export interface RoleCreate {
  name: string;
  description?: string;
  level?: number;
  color?: string;
  permissions: string[];
  is_system?: boolean;
}

export interface RoleUpdate {
  name?: string;
  description?: string;
  level?: number;
  color?: string;
  permissions?: string[];
  is_system?: boolean;
}

export interface RoleClone {
  name: string;
  description?: string;
}

export interface RoleWithUsers extends Role {
  user_count: number;
}

export interface RoleValidation {
  role_id: string;
  role_name: string;
  total_permissions: number;
  valid_permissions: number;
  invalid_permissions: number;
  conflicts: any[];
  validation_details: Record<string, boolean>;
}

export interface RoleUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
}

export class RoleAPI {
  /**
   * Get all roles for the organization
   */
  static async getRoles(
    token: string,
    includeSystem: boolean = true,
    skip: number = 0,
    limit: number = 100
  ): Promise<Role[]> {
    const params = new URLSearchParams();
    params.append('include_system', includeSystem.toString());
    params.append('skip', skip.toString());
    params.append('limit', limit.toString());

    return await apiClient.get(`/roles?${params.toString()}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get a specific role by ID
   */
  static async getRole(token: string, id: string): Promise<Role> {
    return await apiClient.get(`/roles/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create a new role
   */
  static async createRole(token: string, role: RoleCreate): Promise<Role> {
    return await apiClient.post('/roles', role, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update an existing role
   */
  static async updateRole(token: string, id: string, role: RoleUpdate): Promise<Role> {
    return await apiClient.put(`/roles/${id}`, role, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Delete a role
   */
  static async deleteRole(token: string, id: string): Promise<void> {
    await apiClient.delete(`/roles/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Clone an existing role
   */
  static async cloneRole(token: string, id: string, cloneData: RoleClone): Promise<Role> {
    return await apiClient.post(`/roles/${id}/clone`, cloneData, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create role from template
   */
  static async createRoleFromTemplate(
    token: string,
    templateId: string,
    roleName: string,
    roleDescription?: string
  ): Promise<Role> {
    const params = new URLSearchParams();
    params.append('template_id', templateId);
    params.append('role_name', roleName);
    if (roleDescription) {
      params.append('role_description', roleDescription);
    }

    return await apiClient.post(`/roles/from-template?${params.toString()}`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get users assigned to a role
   */
  static async getRoleUsers(token: string, roleId: string): Promise<RoleUser[]> {
    return await apiClient.get(`/roles/${roleId}/users`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permissions for a role
   */
  static async getRolePermissions(token: string, roleId: string): Promise<string[]> {
    return await apiClient.get(`/roles/${roleId}/permissions`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update role permissions
   */
  static async updateRolePermissions(
    token: string,
    roleId: string,
    permissions: string[]
  ): Promise<Role> {
    return await apiClient.put(`/roles/${roleId}/permissions`, permissions, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get role permission conflicts
   */
  static async getRoleConflicts(token: string, roleId: string): Promise<any[]> {
    return await apiClient.get(`/roles/${roleId}/conflicts`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get role permission recommendations
   */
  static async getRoleRecommendations(token: string, roleId: string): Promise<any[]> {
    return await apiClient.get(`/roles/${roleId}/recommendations`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Validate role permissions
   */
  static async validateRole(token: string, roleId: string): Promise<RoleValidation> {
    return await apiClient.post(`/roles/${roleId}/validate`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get available role templates
   */
  static async getAvailableTemplates(token: string): Promise<any[]> {
    return await apiClient.get('/roles/templates/available', {
      Authorization: `Bearer ${token}`,
    });
  }
}
