/**
 * Badge API service
 */

import { apiClient } from './client';

export interface CategoryInfo {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
}

export interface Badge {
  id: string;
  name: string;
  description?: string;
  icon: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  criteria: string;
  points_reward: number;
  status: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  times_earned: number;
  category_id?: string;
  category?: CategoryInfo;
  organization_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface BadgeCreate {
  name: string;
  description?: string;
  icon: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  criteria: string;
  points_reward: number;
  status: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  category_id?: string;
}

export interface BadgeUpdate {
  name?: string;
  description?: string;
  icon?: string;
  tier?: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  criteria?: string;
  points_reward?: number;
  status?: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  category_id?: string;
}

export interface BadgeFilters {
  tier_filter?: string;
  status_filter?: string;
  search?: string;
  skip?: number;
  limit?: number;
}

export class BadgeAPI {
  /**
   * Get all badges with optional filters
   */
  static async getBadges(token: string, filters: BadgeFilters = {}): Promise<Badge[]> {
    const params = new URLSearchParams();

    if (filters.tier_filter) params.append('tier_filter', filters.tier_filter);
    if (filters.status_filter) params.append('status_filter', filters.status_filter);
    if (filters.search) params.append('search', filters.search);
    if (filters.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters.limit !== undefined) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `/badges${queryString ? `?${queryString}` : ''}`;

    return await apiClient.get(url, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get a specific badge by ID
   */
  static async getBadge(token: string, id: string): Promise<Badge> {
    return await apiClient.get(`/badges/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create a new badge
   */
  static async createBadge(token: string, badge: BadgeCreate): Promise<Badge> {
    return await apiClient.post('/badges', badge, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update an existing badge
   */
  static async updateBadge(token: string, id: string, badge: BadgeUpdate): Promise<Badge> {
    return await apiClient.put(`/badges/${id}`, badge, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Delete a badge
   */
  static async deleteBadge(token: string, id: string): Promise<void> {
    await apiClient.delete(`/badges/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get badges by tier
   */
  static async getBadgesByTier(
    token: string,
    tier: string,
    skip: number = 0,
    limit: number = 100
  ): Promise<Badge[]> {
    return await apiClient.get(`/badges/tier/${tier}?skip=${skip}&limit=${limit}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Seed sample badges for development and testing
   */
  static async seedBadges(token: string): Promise<{ message: string; badges_created: number; badges: any[] }> {
    return await apiClient.post('/seeding/seed-badges', {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Clear all badges for the organization (development only)
   */
  static async clearBadges(token: string): Promise<{ message: string; badges_deleted: number }> {
    return await apiClient.delete('/seeding/clear-badges', {
      Authorization: `Bearer ${token}`,
    });
  }
}
