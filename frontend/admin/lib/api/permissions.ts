/**
 * Permissions API service
 */

import { apiClient } from './client';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  scope: string;
  category: string;
}

export interface PermissionTemplate {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  level: number;
  color: string;
  is_system: boolean;
  category?: string;
}

export interface PermissionCheck {
  permissions: string[];
}

export interface PermissionCheckResult {
  [permissionId: string]: boolean;
}

export interface PermissionMatrix {
  [resource: string]: string[];
}

export interface PermissionHierarchy {
  [permission: string]: string[];
}

export interface PermissionValidation {
  permission_id: string;
  valid: boolean;
  details?: any;
}

export interface UserPermissions {
  user_id: string;
  permissions: string[];
  effective_permissions: string[];
  role_id?: string;
  role_name?: string;
}

export class PermissionAPI {
  /**
   * Get all available permissions
   */
  static async getPermissions(
    token: string,
    resource?: string,
    action?: string
  ): Promise<Permission[]> {
    const params = new URLSearchParams();
    if (resource) params.append('resource', resource);
    if (action) params.append('action', action);

    const queryString = params.toString();
    const endpoint = queryString ? `/permissions?${queryString}` : '/permissions';

    return await apiClient.get(endpoint, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get available permission resources
   */
  static async getPermissionResources(token: string): Promise<string[]> {
    return await apiClient.get('/permissions/resources', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get available permission actions
   */
  static async getPermissionActions(token: string): Promise<string[]> {
    return await apiClient.get('/permissions/actions', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permission templates
   */
  static async getPermissionTemplates(token: string): Promise<PermissionTemplate[]> {
    return await apiClient.get('/permissions/templates', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permission matrix
   */
  static async getPermissionMatrix(token: string): Promise<PermissionMatrix> {
    return await apiClient.get('/permissions/matrix', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Validate a permission
   */
  static async validatePermission(
    token: string,
    permissionId: string
  ): Promise<PermissionValidation> {
    return await apiClient.get(`/permissions/validate/${permissionId}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Check multiple permissions for current user
   */
  static async checkPermissions(
    token: string,
    permissionIds: string[]
  ): Promise<PermissionCheckResult> {
    return await apiClient.post('/permissions/check', permissionIds, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permissions for a specific user
   */
  static async getUserPermissions(
    token: string,
    userId: string
  ): Promise<string[]> {
    return await apiClient.get(`/permissions/user/${userId}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get effective permissions for current user
   */
  static async getCurrentUserPermissions(token: string): Promise<string[]> {
    return await apiClient.get('/permissions/effective', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permission hierarchy
   */
  static async getPermissionHierarchy(token: string): Promise<PermissionHierarchy> {
    return await apiClient.get('/permissions/hierarchy', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get permissions organized by category
   */
  static async getPermissionsByCategory(token: string): Promise<PermissionMatrix> {
    return await apiClient.get('/permissions/categories', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Check if user has specific permission
   */
  static async hasPermission(token: string, permission: string): Promise<boolean> {
    try {
      const result = await this.checkPermissions(token, [permission]);
      return result[permission] || false;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  static async hasAnyPermission(token: string, permissions: string[]): Promise<boolean> {
    try {
      const results = await this.checkPermissions(token, permissions);
      return Object.values(results).some(hasPermission => hasPermission);
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  }

  /**
   * Check if user has all of the specified permissions
   */
  static async hasAllPermissions(token: string, permissions: string[]): Promise<boolean> {
    try {
      const results = await this.checkPermissions(token, permissions);
      return permissions.every(permission => results[permission]);
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  }

  /**
   * Get permissions by resource
   */
  static async getPermissionsByResource(
    token: string,
    resource: string
  ): Promise<Permission[]> {
    return await this.getPermissions(token, resource);
  }

  /**
   * Get permissions by action
   */
  static async getPermissionsByAction(
    token: string,
    action: string
  ): Promise<Permission[]> {
    return await this.getPermissions(token, undefined, action);
  }

  /**
   * Validate multiple permissions
   */
  static async validatePermissions(
    token: string,
    permissionIds: string[]
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const permissionId of permissionIds) {
      try {
        const validation = await this.validatePermission(token, permissionId);
        results[permissionId] = validation.valid;
      } catch (error) {
        results[permissionId] = false;
      }
    }
    
    return results;
  }
}
