/**
 * Rewards API service
 */

import { apiClient } from './client';

export interface CategoryInfo {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
}

export interface Reward {
  id: string;
  name: string;
  description?: string;
  image_url?: string;
  points_cost: number;
  stock_quantity?: number;
  status: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  category_id?: string;
  category?: CategoryInfo;
  organization_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface RewardCreate {
  name: string;
  description?: string;
  image_url?: string;
  points_cost: number;
  stock_quantity?: number;
  category_id?: string;
}

export interface RewardUpdate {
  name?: string;
  description?: string;
  image_url?: string;
  points_cost?: number;
  stock_quantity?: number;
  status?: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  category_id?: string;
}

export interface RewardFilters {
  status_filter?: 'ACTIVE' | 'DRAFT' | 'PAUSED' | 'ARCHIVED';
  min_points?: number;
  max_points?: number;
  skip?: number;
  limit?: number;
}

export class RewardAPI {
  /**
   * Get all rewards for the organization
   */
  static async getRewards(token: string, filters?: RewardFilters): Promise<Reward[]> {
    const params = new URLSearchParams();
    
    if (filters?.status_filter) params.append('status_filter', filters.status_filter);
    if (filters?.min_points !== undefined) params.append('min_points', filters.min_points.toString());
    if (filters?.max_points !== undefined) params.append('max_points', filters.max_points.toString());
    if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const endpoint = queryString ? `/rewards?${queryString}` : '/rewards';

    return await apiClient.get(endpoint, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get available rewards (that user can afford)
   */
  static async getAvailableRewards(
    token: string, 
    userPoints: number, 
    skip: number = 0, 
    limit: number = 100
  ): Promise<Reward[]> {
    return await apiClient.get(`/rewards/available?user_points=${userPoints}&skip=${skip}&limit=${limit}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get a specific reward by ID
   */
  static async getReward(token: string, id: string): Promise<Reward> {
    return await apiClient.get(`/rewards/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create a new reward
   */
  static async createReward(token: string, reward: RewardCreate): Promise<Reward> {
    return await apiClient.post('/rewards', reward, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update an existing reward
   */
  static async updateReward(token: string, id: string, reward: RewardUpdate): Promise<Reward> {
    return await apiClient.put(`/rewards/${id}`, reward, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Delete a reward
   */
  static async deleteReward(token: string, id: string): Promise<void> {
    await apiClient.delete(`/rewards/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Activate a reward
   */
  static async activateReward(token: string, id: string): Promise<Reward> {
    return await apiClient.post(`/rewards/${id}/activate`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Deactivate a reward
   */
  static async deactivateReward(token: string, id: string): Promise<Reward> {
    return await apiClient.post(`/rewards/${id}/deactivate`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Redeem a reward for a user
   */
  static async redeemReward(token: string, rewardId: string, userId: string): Promise<any> {
    return await apiClient.post(`/rewards/${rewardId}/redeem`, { user_id: userId }, {
      Authorization: `Bearer ${token}`,
    });
  }
}
