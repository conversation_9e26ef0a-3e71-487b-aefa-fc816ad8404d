import { apiClient } from './client';
import { Role } from './roles';

export interface Invitation {
  id: string;
  email: string;
  role: Role;
  organization_id: string;
  invited_by: string;
  created_at: string;
  expires_at: string;
}

export interface InvitationCreate {
  email: string;
  role: string;
}

export const InvitationAPI = {
  createInvitation: async (token: string, invitation: InvitationCreate): Promise<Invitation> => {
    return apiClient.post('/invitations', invitation, {
      Authorization: `Bearer ${token}`,
    });
  },
};