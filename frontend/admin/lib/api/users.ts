/**
 * Users API service
 */

import { apiClient } from './client';

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_superuser: boolean;
  organization_id: string;
  role_id?: string;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface UserCreate {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role_id?: string;
  is_active?: boolean;
  organization_id?: string;
}

export interface UserUpdate {
  email?: string;
  first_name?: string;
  last_name?: string;
  role_id?: string;
  is_active?: boolean;
  password?: string;
}

export interface UserWithRole extends User {
  role?: {
    id: string;
    name: string;
    color: string;
    level: number;
  };
}

export interface UserInvitation {
  email: string;
  first_name: string;
  last_name: string;
  role_id?: string;
  invitation_message?: string;
}

export interface InvitationResponse {
  invitation_id: string;
  email: string;
  token: string;
  expires_at: string;
  email_sent: boolean;
  organization_name: string;
  role_name?: string;
}

export interface InvitationStatus {
  valid: boolean;
  expired: boolean;
  accepted: boolean;
  email?: string;
  first_name?: string;
  last_name?: string;
  organization_name?: string;
  role_name?: string;
  expires_at?: string;
}

export interface InvitationAcceptance {
  token: string;
  password: string;
}

export interface PendingInvitation {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role_name?: string;
  invited_by: string;
  is_accepted: boolean;
  is_expired: boolean;
  expires_at: string;
  created_at: string;
  accepted_at?: string;
}

export interface UserFilters {
  role_id?: string;
  is_active?: boolean;
  search?: string;
  skip?: number;
  limit?: number;
}

export class UserAPI {
  /**
   * Get current user information
   */
  static async getCurrentUser(token: string): Promise<User> {
    return await apiClient.get('/users/me', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get all users in the organization
   */
  static async getUsers(
    token: string,
    filters: UserFilters = {}
  ): Promise<User[]> {
    const params = new URLSearchParams();
    
    if (filters.role_id) params.append('role_id', filters.role_id);
    if (filters.is_active !== undefined) params.append('is_active', filters.is_active.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters.limit !== undefined) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const endpoint = queryString ? `/users?${queryString}` : '/users';

    return await apiClient.get(endpoint, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get a specific user by ID
   */
  static async getUser(token: string, id: string): Promise<User> {
    return await apiClient.get(`/users/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create a new user
   */
  static async createUser(token: string, user: UserCreate): Promise<User> {
    return await apiClient.post('/users', user, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update an existing user
   */
  static async updateUser(token: string, id: string, user: UserUpdate): Promise<User> {
    return await apiClient.put(`/users/${id}`, user, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Delete a user
   */
  static async deleteUser(token: string, id: string): Promise<void> {
    await apiClient.delete(`/users/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Assign a role to a user
   */
  static async assignRole(token: string, userId: string, roleId: string): Promise<User> {
    const params = new URLSearchParams();
    params.append('role_id', roleId);

    return await apiClient.put(`/users/${userId}/role?${params.toString()}`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get user permissions
   */
  static async getUserPermissions(token: string, userId: string): Promise<string[]> {
    return await apiClient.get(`/users/${userId}/permissions`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Invite a user via email
   */
  static async inviteUser(token: string, invitation: UserInvitation): Promise<InvitationResponse> {
    return await apiClient.post('/invitations/invite', invitation, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get invitation status by token (public)
   */
  static async getInvitationStatus(token: string): Promise<InvitationStatus> {
    return await apiClient.get(`/invitations/status/${token}`);
  }

  /**
   * Accept invitation and create account (public)
   */
  static async acceptInvitation(acceptance: InvitationAcceptance): Promise<any> {
    return await apiClient.post('/invitations/accept', acceptance);
  }

  /**
   * Get pending invitations for organization
   */
  static async getPendingInvitations(token: string): Promise<PendingInvitation[]> {
    return await apiClient.get('/invitations?pending_only=true', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get all invitations for organization
   */
  static async getAllInvitations(token: string): Promise<PendingInvitation[]> {
    return await apiClient.get('/invitations', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Cancel a pending invitation
   */
  static async cancelInvitation(token: string, invitationId: string): Promise<void> {
    return await apiClient.delete(`/invitations/${invitationId}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Resend invitation email
   */
  static async resendInvitation(token: string, invitationId: string): Promise<any> {
    return await apiClient.post(`/invitations/resend/${invitationId}`, {}, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get invitation link for sharing
   */
  static async getInvitationLink(token: string, invitationId: string): Promise<{
    invitation_link: string;
    token: string;
    expires_at: string;
    email: string;
  }> {
    return await apiClient.get(`/invitations/${invitationId}/link`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(token: string, roleId: string): Promise<User[]> {
    return await this.getUsers(token, { role_id: roleId });
  }

  /**
   * Activate/Deactivate user
   */
  static async toggleUserStatus(token: string, userId: string, isActive: boolean): Promise<User> {
    return await this.updateUser(token, userId, { is_active: isActive });
  }

  /**
   * Reset user password
   */
  static async resetPassword(token: string, userId: string, newPassword: string): Promise<User> {
    return await this.updateUser(token, userId, { password: newPassword });
  }

  /**
   * Get user activity/audit log
   */
  static async getUserActivity(token: string, userId: string): Promise<any[]> {
    return await apiClient.get(`/users/${userId}/activity`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Bulk update users
   */
  static async bulkUpdateUsers(
    token: string,
    userIds: string[],
    updates: Partial<UserUpdate>
  ): Promise<User[]> {
    return await apiClient.put('/users/bulk', {
      user_ids: userIds,
      updates: updates
    }, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Bulk assign role to users
   */
  static async bulkAssignRole(
    token: string,
    userIds: string[],
    roleId: string
  ): Promise<User[]> {
    return await this.bulkUpdateUsers(token, userIds, { role_id: roleId });
  }

  /**
   * Get user statistics
   */
  static async getUserStats(token: string): Promise<any> {
    return await apiClient.get('/users/stats', {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Search users
   */
  static async searchUsers(
    token: string,
    query: string,
    limit: number = 10
  ): Promise<User[]> {
    return await this.getUsers(token, { search: query, limit });
  }

  /**
   * Check if user has permission
   */
  static async checkUserPermission(
    token: string,
    userId: string,
    permission: string
  ): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(token, userId);
      return permissions.includes(permission);
    } catch (error) {
      console.error('Error checking user permission:', error);
      return false;
    }
  }

  /**
   * Get users with specific permission
   */
  static async getUsersWithPermission(
    token: string,
    permission: string
  ): Promise<User[]> {
    // This would need a backend endpoint, for now we'll get all users and filter
    const users = await this.getUsers(token);
    const usersWithPermission: User[] = [];

    for (const user of users) {
      try {
        const hasPermission = await this.checkUserPermission(token, user.id, permission);
        if (hasPermission) {
          usersWithPermission.push(user);
        }
      } catch (error) {
        console.error(`Error checking permission for user ${user.id}:`, error);
      }
    }

    return usersWithPermission;
  }
}
