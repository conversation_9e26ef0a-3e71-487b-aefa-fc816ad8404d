/**
 * Category API service
 */

import { apiClient } from './client';

export interface Category {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

export interface CategoryCreate {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  organization_id: string;
}

export interface CategoryUpdate {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
}

export class CategoryAPI {
  /**
   * Get all categories for the organization
   */
  static async getCategories(token: string, skip: number = 0, limit: number = 100): Promise<Category[]> {
    return await apiClient.get(`/categories?skip=${skip}&limit=${limit}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Get a specific category by ID
   */
  static async getCategory(token: string, id: string): Promise<Category> {
    return await apiClient.get(`/categories/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Create a new category
   */
  static async createCategory(token: string, category: CategoryCreate): Promise<Category> {
    return await apiClient.post('/categories', category, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Update an existing category
   */
  static async updateCategory(token: string, id: string, category: CategoryUpdate): Promise<Category> {
    return await apiClient.put(`/categories/${id}`, category, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Delete a category
   */
  static async deleteCategory(token: string, id: string): Promise<void> {
    await apiClient.delete(`/categories/${id}`, {
      Authorization: `Bearer ${token}`,
    });
  }

  /**
   * Seed default categories for the organization
   */
  static async seedDefaultCategories(token: string): Promise<Category[]> {
    return await apiClient.post('/categories/seed-defaults', {}, {
      Authorization: `Bearer ${token}`,
    });
  }
}
