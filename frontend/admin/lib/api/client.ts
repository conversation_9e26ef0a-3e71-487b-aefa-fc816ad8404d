/**
 * API client for making authenticated requests
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const API_V1_BASE = `${API_BASE_URL}/api/v1`

export interface ApiError {
  detail: string
  status_code?: number
}

// HTTP client utility
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        let errorMessage = 'An error occurred'
        try {
          const errorData = await response.json()
          errorMessage = errorData.detail || errorMessage
        } catch {
          errorMessage = response.statusText || errorMessage
        }
        
        const error: ApiError = {
          detail: errorMessage,
          status_code: response.status,
        }
        throw error
      }

      // Handle empty responses (like DELETE operations)
      // Check if response has content
      if (response.status === 204) {
        return {} as T
      }

      // Check content length
      const contentLength = response.headers.get('content-length')
      if (contentLength === '0') {
        return {} as T
      }

      // Try to get response text first to check if it's empty
      const responseText = await response.text()

      // If response is empty, return empty object
      if (!responseText || responseText.trim() === '') {
        return {} as T
      }

      // Check if it's JSON content
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        try {
          return JSON.parse(responseText)
        } catch (e) {
          console.warn('Failed to parse JSON response:', responseText)
          return {} as T
        }
      } else {
        // For non-JSON responses, return the text
        return responseText as T
      }
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw {
          detail: 'Network error. Please check your connection.',
          status_code: 0,
        } as ApiError
      }
      throw error
    }
  }

  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', headers })
  }

  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  }

  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', headers })
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_V1_BASE)
export default apiClient
