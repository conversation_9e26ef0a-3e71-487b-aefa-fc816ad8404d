// Permission Templates for Common Use Cases

import { PERMISSIONS } from "./rbac"

export interface PermissionTemplate {
  id: string
  name: string
  description: string
  category: "business" | "technical" | "specialized"
  icon: string
  permissions: string[]
  suggestedRoles: string[]
  useCases: string[]
  level: number
  color: string
}

export const PERMISSION_TEMPLATES: PermissionTemplate[] = [
  // Business Templates
  {
    id: "marketing-manager",
    name: "Marketing Manager",
    description: "Full campaign and content management with analytics access",
    category: "business",
    icon: "megaphone",
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "campaigns.create",
      "campaigns.edit",
      "campaigns.publish",
      "quests.view",
      "quests.create",
      "quests.edit",
      "badges.view",
      "badges.create",
      "badges.edit",
      "rewards.view",
      "rewards.create",
      "rewards.edit",
      "users.view",
      "analytics.view",
      "analytics.export",
      "analytics.advanced",
      "settings.view",
      "settings.branding",
    ],
    suggestedRoles: ["Marketing Manager", "Campaign Director", "Growth Manager"],
    useCases: [
      "Managing marketing campaigns",
      "Creating customer engagement programs",
      "Analyzing campaign performance",
      "Brand management",
    ],
    level: 70,
    color: "#8b5cf6",
  },
  {
    id: "customer-success",
    name: "Customer Success",
    description: "Customer support and engagement with limited content creation",
    category: "business",
    icon: "heart-handshake",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "quests.approve",
      "badges.view",
      "badges.award",
      "rewards.view",
      "rewards.fulfill",
      "users.view",
      "users.edit",
      "analytics.view",
    ],
    suggestedRoles: ["Customer Success Manager", "Support Specialist", "Community Manager"],
    useCases: [
      "Resolving customer issues",
      "Manual reward fulfillment",
      "Customer data management",
      "Quest completion approval",
    ],
    level: 50,
    color: "#059669",
  },
  {
    id: "content-creator",
    name: "Content Creator",
    description: "Quest and badge creation with limited campaign access",
    category: "business",
    icon: "pen-tool",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "quests.create",
      "quests.edit",
      "badges.view",
      "badges.create",
      "badges.edit",
      "rewards.view",
      "users.view",
      "analytics.view",
    ],
    suggestedRoles: ["Content Creator", "Quest Designer", "Gamification Specialist"],
    useCases: [
      "Creating engaging quests",
      "Designing achievement badges",
      "Content strategy development",
      "User experience optimization",
    ],
    level: 45,
    color: "#ea580c",
  },
  {
    id: "business-analyst",
    name: "Business Analyst",
    description: "Comprehensive analytics and reporting access",
    category: "business",
    icon: "bar-chart-3",
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "analytics.export",
      "analytics.advanced",
      "system.audit",
    ],
    suggestedRoles: ["Business Analyst", "Data Analyst", "Performance Manager"],
    useCases: ["Performance analysis", "ROI measurement", "User behavior insights", "Strategic reporting"],
    level: 35,
    color: "#0891b2",
  },
  {
    id: "operations-manager",
    name: "Operations Manager",
    description: "Operational oversight with team and process management",
    category: "business",
    icon: "settings",
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "campaigns.edit",
      "quests.view",
      "quests.edit",
      "quests.approve",
      "badges.view",
      "badges.award",
      "rewards.view",
      "rewards.fulfill",
      "users.view",
      "users.edit",
      "team.view",
      "analytics.view",
      "analytics.export",
      "settings.view",
    ],
    suggestedRoles: ["Operations Manager", "Process Manager", "Quality Assurance"],
    useCases: ["Process optimization", "Quality control", "Operational reporting", "Team coordination"],
    level: 65,
    color: "#dc2626",
  },

  // Technical Templates
  {
    id: "developer",
    name: "Developer",
    description: "Technical integration and API management",
    category: "technical",
    icon: "code",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "settings.view",
      "settings.integrations",
      "system.audit",
    ],
    suggestedRoles: ["Developer", "Integration Specialist", "Technical Lead"],
    useCases: ["API integration", "Webhook configuration", "Technical troubleshooting", "System monitoring"],
    level: 60,
    color: "#7c3aed",
  },
  {
    id: "devops",
    name: "DevOps Engineer",
    description: "System administration and maintenance",
    category: "technical",
    icon: "server",
    permissions: [
      "dashboard.view",
      "analytics.view",
      "settings.view",
      "settings.integrations",
      "settings.security",
      "system.audit",
      "system.backup",
      "system.maintenance",
    ],
    suggestedRoles: ["DevOps Engineer", "System Administrator", "Infrastructure Manager"],
    useCases: ["System maintenance", "Security management", "Backup operations", "Performance monitoring"],
    level: 80,
    color: "#374151",
  },
  {
    id: "qa-tester",
    name: "QA Tester",
    description: "Quality assurance and testing access",
    category: "technical",
    icon: "bug",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "quests.create",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "system.audit",
    ],
    suggestedRoles: ["QA Tester", "Quality Assurance", "Test Engineer"],
    useCases: ["Feature testing", "Bug reproduction", "User acceptance testing", "Quality validation"],
    level: 40,
    color: "#f59e0b",
  },

  // Specialized Templates
  {
    id: "compliance-officer",
    name: "Compliance Officer",
    description: "Audit and compliance monitoring",
    category: "specialized",
    icon: "shield-check",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "analytics.export",
      "system.audit",
      "settings.view",
      "settings.security",
    ],
    suggestedRoles: ["Compliance Officer", "Audit Manager", "Risk Manager"],
    useCases: ["Compliance monitoring", "Audit trail review", "Risk assessment", "Regulatory reporting"],
    level: 55,
    color: "#991b1b",
  },
  {
    id: "finance-manager",
    name: "Finance Manager",
    description: "Financial oversight and billing management",
    category: "specialized",
    icon: "dollar-sign",
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "analytics.export",
      "analytics.advanced",
      "settings.view",
      "settings.billing",
    ],
    suggestedRoles: ["Finance Manager", "CFO", "Budget Manager"],
    useCases: ["Cost analysis", "Budget management", "Financial reporting", "ROI calculation"],
    level: 75,
    color: "#16a34a",
  },
  {
    id: "product-manager",
    name: "Product Manager",
    description: "Product strategy and feature management",
    category: "specialized",
    icon: "lightbulb",
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "campaigns.create",
      "campaigns.edit",
      "quests.view",
      "quests.create",
      "quests.edit",
      "badges.view",
      "badges.create",
      "badges.edit",
      "rewards.view",
      "rewards.create",
      "rewards.edit",
      "users.view",
      "analytics.view",
      "analytics.export",
      "analytics.advanced",
      "settings.view",
      "settings.branding",
    ],
    suggestedRoles: ["Product Manager", "Product Owner", "Strategy Manager"],
    useCases: ["Product strategy", "Feature planning", "User experience design", "Market analysis"],
    level: 75,
    color: "#db2777",
  },
  {
    id: "external-consultant",
    name: "External Consultant",
    description: "Limited access for external advisors",
    category: "specialized",
    icon: "user-check",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "analytics.view",
      "analytics.export",
    ],
    suggestedRoles: ["Consultant", "Advisor", "External Auditor"],
    useCases: ["Strategic consulting", "Performance review", "Best practice analysis", "External audit"],
    level: 25,
    color: "#6b7280",
  },
  {
    id: "intern",
    name: "Intern/Trainee",
    description: "Basic access for learning and training",
    category: "specialized",
    icon: "graduation-cap",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
    ],
    suggestedRoles: ["Intern", "Trainee", "Junior Associate"],
    useCases: ["Learning platform features", "Shadowing team members", "Basic data analysis", "Training exercises"],
    level: 15,
    color: "#a3a3a3",
  },

  // Department-Specific Templates
  {
    id: "sales-team",
    name: "Sales Team",
    description: "Customer-facing sales with limited platform access",
    category: "business",
    icon: "trending-up",
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
    ],
    suggestedRoles: ["Sales Representative", "Account Manager", "Sales Director"],
    useCases: ["Customer demonstrations", "Sales presentations", "Account management", "Customer onboarding"],
    level: 30,
    color: "#2563eb",
  },
  {
    id: "hr-manager",
    name: "HR Manager",
    description: "Team management and user administration",
    category: "business",
    icon: "users",
    permissions: [
      "dashboard.view",
      "team.view",
      "team.invite",
      "team.edit",
      "users.view",
      "analytics.view",
      "settings.view",
      "system.audit",
    ],
    suggestedRoles: ["HR Manager", "People Operations", "Team Lead"],
    useCases: ["Team member onboarding", "Access management", "Performance tracking", "Compliance monitoring"],
    level: 60,
    color: "#be185d",
  },
]

// Template utility functions
export class PermissionTemplateService {
  static getTemplatesByCategory(category: string): PermissionTemplate[] {
    return PERMISSION_TEMPLATES.filter((template) => template.category === category)
  }

  static getTemplateById(id: string): PermissionTemplate | undefined {
    return PERMISSION_TEMPLATES.find((template) => template.id === id)
  }

  static getTemplatesByLevel(minLevel: number, maxLevel: number): PermissionTemplate[] {
    return PERMISSION_TEMPLATES.filter((template) => template.level >= minLevel && template.level <= maxLevel)
  }

  static searchTemplates(query: string): PermissionTemplate[] {
    const lowercaseQuery = query.toLowerCase()
    return PERMISSION_TEMPLATES.filter(
      (template) =>
        template.name.toLowerCase().includes(lowercaseQuery) ||
        template.description.toLowerCase().includes(lowercaseQuery) ||
        template.useCases.some((useCase) => useCase.toLowerCase().includes(lowercaseQuery)) ||
        template.suggestedRoles.some((role) => role.toLowerCase().includes(lowercaseQuery)),
    )
  }

  static getRecommendedTemplates(existingRoles: string[]): PermissionTemplate[] {
    // Logic to recommend templates based on existing roles
    const existingLevels = existingRoles.map((roleId) => {
      const template = this.getTemplateById(roleId)
      return template?.level || 0
    })

    const maxLevel = Math.max(...existingLevels, 0)
    const minLevel = Math.min(...existingLevels, 100)

    // Recommend templates that fill gaps in the level hierarchy
    return PERMISSION_TEMPLATES.filter((template) => {
      return !existingRoles.includes(template.id) && template.level > minLevel && template.level < maxLevel
    }).slice(0, 5)
  }

  static validateTemplatePermissions(templateId: string): boolean {
    const template = this.getTemplateById(templateId)
    if (!template) return false

    return template.permissions.every((permission) => PERMISSIONS.some((p) => p.id === permission))
  }

  static getPermissionOverlap(templateId1: string, templateId2: string): string[] {
    const template1 = this.getTemplateById(templateId1)
    const template2 = this.getTemplateById(templateId2)

    if (!template1 || !template2) return []

    return template1.permissions.filter((permission) => template2.permissions.includes(permission))
  }

  static getTemplateStats(): {
    totalTemplates: number
    byCategory: Record<string, number>
    averagePermissions: number
    levelDistribution: Record<string, number>
  } {
    const byCategory = PERMISSION_TEMPLATES.reduce(
      (acc, template) => {
        acc[template.category] = (acc[template.category] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    const averagePermissions =
      PERMISSION_TEMPLATES.reduce((sum, template) => sum + template.permissions.length, 0) / PERMISSION_TEMPLATES.length

    const levelDistribution = PERMISSION_TEMPLATES.reduce(
      (acc, template) => {
        const range = `${Math.floor(template.level / 20) * 20}-${Math.floor(template.level / 20) * 20 + 19}`
        acc[range] = (acc[range] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    return {
      totalTemplates: PERMISSION_TEMPLATES.length,
      byCategory,
      averagePermissions: Math.round(averagePermissions),
      levelDistribution,
    }
  }
}

// Quick role creation presets
export const QUICK_ROLE_PRESETS = [
  {
    name: "Marketing Team",
    templates: ["marketing-manager", "content-creator", "business-analyst"],
    description: "Complete marketing team setup",
  },
  {
    name: "Customer Support",
    templates: ["customer-success", "operations-manager"],
    description: "Customer-facing support roles",
  },
  {
    name: "Technical Team",
    templates: ["developer", "devops", "qa-tester"],
    description: "Development and operations team",
  },
  {
    name: "Leadership Team",
    templates: ["product-manager", "finance-manager", "hr-manager"],
    description: "Executive and management roles",
  },
  {
    name: "External Access",
    templates: ["external-consultant", "intern"],
    description: "Limited access for external users",
  },
]
