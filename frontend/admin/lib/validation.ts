/**
 * Form validation utilities
 */

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = []
  
  if (!email) {
    errors.push('Email is required')
  } else if (!email.includes('@')) {
    errors.push('Please enter a valid email address')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please enter a valid email address')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = []
  
  if (!password) {
    errors.push('Password is required')
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number')
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validatePasswordConfirmation = (
  password: string,
  confirmPassword: string
): ValidationResult => {
  const errors: string[] = []
  
  if (!confirmPassword) {
    errors.push('Password confirmation is required')
  } else if (password !== confirmPassword) {
    errors.push('Passwords do not match')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  const errors: string[] = []
  
  if (!value || value.trim() === '') {
    errors.push(`${fieldName} is required`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const calculatePasswordStrength = (password: string): number => {
  let strength = 0
  
  if (password.length >= 8) strength += 25
  if (/[A-Z]/.test(password)) strength += 25
  if (/[a-z]/.test(password)) strength += 25
  if (/[0-9]/.test(password)) strength += 25
  
  return Math.min(strength, 100)
}

export const getPasswordStrengthText = (strength: number): string => {
  if (strength < 25) return 'Very weak'
  if (strength < 50) return 'Weak'
  if (strength < 75) return 'Good'
  return 'Strong'
}

export const getPasswordStrengthColor = (strength: number): string => {
  if (strength < 50) return 'bg-red-500'
  if (strength < 75) return 'bg-yellow-500'
  return 'bg-green-500'
}
