/**
 * API client configuration and service functions
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const API_V1_BASE = `${API_BASE_URL}/api/v1`

// Types for API requests and responses
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  first_name: string
  last_name: string
  organization_name?: string
  industry?: string
  company_size?: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
  expires_in: number
}

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  organization_id?: string
  created_at: string
  updated_at: string
}

export interface ApiError {
  detail: string
  status_code?: number
}

// HTTP client utility
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        let errorMessage = 'An error occurred'
        try {
          const errorData = await response.json()
          errorMessage = errorData.detail || errorMessage
        } catch {
          errorMessage = response.statusText || errorMessage
        }
        
        const error: ApiError = {
          detail: errorMessage,
          status_code: response.status,
        }
        throw error
      }

      return await response.json()
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw {
          detail: 'Network error. Please check your connection.',
          status_code: 0,
        } as ApiError
      }
      throw error
    }
  }

  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', headers })
  }

  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  }

  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', headers })
  }
}

// Create API client instance
const apiClient = new ApiClient(API_V1_BASE)

// Authentication API functions
export const authApi = {
  /**
   * Login user with email and password
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    // Convert to form data for OAuth2PasswordRequestForm compatibility
    const formData = new FormData()
    formData.append('username', credentials.email)
    formData.append('password', credentials.password)

    const response = await fetch(`${API_V1_BASE}/auth/login`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      let errorMessage = 'Login failed'
      try {
        const errorData = await response.json()
        errorMessage = errorData.detail || errorMessage
      } catch {
        errorMessage = response.statusText || errorMessage
      }
      
      const error: ApiError = {
        detail: errorMessage,
        status_code: response.status,
      }
      throw error
    }

    return await response.json()
  },

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<any> {
    const response = await fetch(`${API_V1_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(userData),
    })

    if (!response.ok) {
      let errorMessage = 'Registration failed'
      try {
        const errorData = await response.json()
        errorMessage = errorData.detail || errorMessage
      } catch {
        errorMessage = response.statusText || errorMessage
      }

      const error: ApiError = {
        detail: errorMessage,
        status_code: response.status,
      }
      throw error
    }

    return await response.json()
  },

  /**
   * Get current user information
   */
  async getCurrentUser(token: string): Promise<User> {
    return apiClient.get<User>('/users/me', {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Seed default admin role for the user's organization
   */
  async seedDefaultRole(token: string): Promise<any> {
    return apiClient.post<any>('/auth/seed-default-role', {}, {
      Authorization: `Bearer ${token}`,
    })
  },
}

// Campaign types
export interface Campaign {
  id: string
  name: string
  description?: string
  banner_url?: string
  start_date?: string
  end_date?: string
  status: 'draft' | 'active' | 'paused' | 'ended' | 'archived'
  target_audience?: Record<string, any>
  organization_id: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface CampaignCreate {
  name: string
  description?: string
  banner_url?: string
  start_date?: string
  end_date?: string
  target_audience?: Record<string, any>
  organization_id: string
}

export interface CampaignUpdate {
  name?: string
  description?: string
  banner_url?: string
  start_date?: string
  end_date?: string
  status?: 'draft' | 'active' | 'paused' | 'ended' | 'archived'
  target_audience?: Record<string, any>
}

// Quest types
export interface Quest {
  id: string
  title: string
  description?: string
  points_reward: number
  frequency: 'one_time' | 'daily' | 'weekly' | 'monthly' | 'unlimited'
  validation_type: 'automatic' | 'manual' | 'code' | 'upload'
  validation_criteria?: Record<string, any>
  status: 'draft' | 'active' | 'paused' | 'archived'
  campaign_id: string
  category_id?: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface QuestCreate {
  title: string
  description?: string
  points_reward: number
  frequency: 'one_time' | 'daily' | 'weekly' | 'monthly' | 'unlimited'
  validation_type: 'automatic' | 'manual' | 'code' | 'upload'
  validation_criteria?: Record<string, any>
  campaign_id: string
  category_id?: string
}

// Campaign API functions
export const campaignApi = {
  /**
   * Get all campaigns for the current organization
   */
  async getCampaigns(token: string, params?: {
    status_filter?: string
    skip?: number
    limit?: number
  }): Promise<Campaign[]> {
    const searchParams = new URLSearchParams()
    if (params?.status_filter) searchParams.append('status_filter', params.status_filter)
    if (params?.skip) searchParams.append('skip', params.skip.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())

    const endpoint = `/campaigns/${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return apiClient.get<Campaign[]>(endpoint, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Create a new campaign
   */
  async createCampaign(token: string, campaignData: CampaignCreate): Promise<Campaign> {
    return apiClient.post<Campaign>('/campaigns/', campaignData, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Get a specific campaign by ID
   */
  async getCampaign(token: string, campaignId: string): Promise<Campaign> {
    return apiClient.get<Campaign>(`/campaigns/${campaignId}`, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Update a campaign
   */
  async updateCampaign(token: string, campaignId: string, updateData: CampaignUpdate): Promise<Campaign> {
    return apiClient.put<Campaign>(`/campaigns/${campaignId}`, updateData, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Delete a campaign
   */
  async deleteCampaign(token: string, campaignId: string): Promise<void> {
    return apiClient.delete<void>(`/campaigns/${campaignId}`, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Activate a campaign
   */
  async activateCampaign(token: string, campaignId: string): Promise<Campaign> {
    return apiClient.post<Campaign>(`/campaigns/${campaignId}/activate`, {}, {
      Authorization: `Bearer ${token}`,
    })
  },
}

// Quest API functions
export const questApi = {
  /**
   * Get all quests
   */
  async getQuests(token: string, params?: {
    campaign_id?: string
    skip?: number
    limit?: number
  }): Promise<Quest[]> {
    const searchParams = new URLSearchParams()
    if (params?.campaign_id) searchParams.append('campaign_id', params.campaign_id)
    if (params?.skip) searchParams.append('skip', params.skip.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())

    const endpoint = `/quests/${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return apiClient.get<Quest[]>(endpoint, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Create a new quest
   */
  async createQuest(token: string, questData: QuestCreate): Promise<Quest> {
    return apiClient.post<Quest>('/quests/', questData, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Get a specific quest by ID
   */
  async getQuest(token: string, questId: string): Promise<Quest> {
    return apiClient.get<Quest>(`/quests/${questId}`, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Update an existing quest
   */
  async updateQuest(token: string, questId: string, questData: Partial<QuestCreate & { status?: string }>): Promise<Quest> {
    return apiClient.put<Quest>(`/quests/${questId}`, questData, {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Delete a quest
   */
  async deleteQuest(token: string, questId: string): Promise<void> {
    return apiClient.delete<void>(`/quests/${questId}`, {
      Authorization: `Bearer ${token}`,
    })
  },
}

// Category types
export interface Category {
  id: string
  name: string
  description?: string
  color: string
  icon?: string
  organization_id: string
  created_at: string
  updated_at: string
}

// Category API functions
export const categoryApi = {
  /**
   * Get all categories for the current organization
   */
  async getCategories(token: string): Promise<Category[]> {
    return apiClient.get<Category[]>('/categories/', {
      Authorization: `Bearer ${token}`,
    })
  },

  /**
   * Seed default categories for the organization
   */
  async seedDefaultCategories(token: string): Promise<Category[]> {
    return apiClient.post<Category[]>('/categories/seed-defaults', {}, {
      Authorization: `Bearer ${token}`,
    })
  },
}

// Audience segments API functions
export const audienceApi = {
  /**
   * Get available audience segments
   */
  async getAudienceSegments(token: string): Promise<string[]> {
    return apiClient.get<string[]>('/campaigns/audience-segments', {
      Authorization: `Bearer ${token}`,
    })
  },
}

// Export the API client for other services
export default apiClient
