// Role-based Access Control System

export interface Permission {
  id: string
  name: string
  description: string
  category: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description: string
  level: number
  permissions: string[]
  isSystem: boolean
  color: string
}

export interface User {
  id: string
  name: string
  email: string
  roleId: string
  status: "active" | "inactive" | "pending"
  lastActive: string
  avatar?: string
  invitedBy?: string
  joinedAt: string
}

// Define all available permissions
export const PERMISSIONS: Permission[] = [
  // Dashboard permissions
  {
    id: "dashboard.view",
    name: "View Dashboard",
    description: "Access to main dashboard",
    category: "Dashboard",
    resource: "dashboard",
    action: "view",
  },
  {
    id: "dashboard.analytics",
    name: "View Analytics",
    description: "Access to analytics and reports",
    category: "Dashboard",
    resource: "dashboard",
    action: "analytics",
  },

  // Campaign permissions
  {
    id: "campaigns.view",
    name: "View Campaigns",
    description: "View campaign list and details",
    category: "Campaigns",
    resource: "campaigns",
    action: "view",
  },
  {
    id: "campaigns.create",
    name: "Create Campaigns",
    description: "Create new campaigns",
    category: "Campaigns",
    resource: "campaigns",
    action: "create",
  },
  {
    id: "campaigns.edit",
    name: "Edit Campaigns",
    description: "Modify existing campaigns",
    category: "Campaigns",
    resource: "campaigns",
    action: "edit",
  },
  {
    id: "campaigns.delete",
    name: "Delete Campaigns",
    description: "Delete campaigns",
    category: "Campaigns",
    resource: "campaigns",
    action: "delete",
  },
  {
    id: "campaigns.publish",
    name: "Publish Campaigns",
    description: "Activate/deactivate campaigns",
    category: "Campaigns",
    resource: "campaigns",
    action: "publish",
  },

  // Quest permissions
  {
    id: "quests.view",
    name: "View Quests",
    description: "View quest list and details",
    category: "Quests",
    resource: "quests",
    action: "view",
  },
  {
    id: "quests.create",
    name: "Create Quests",
    description: "Create new quests",
    category: "Quests",
    resource: "quests",
    action: "create",
  },
  {
    id: "quests.edit",
    name: "Edit Quests",
    description: "Modify existing quests",
    category: "Quests",
    resource: "quests",
    action: "edit",
  },
  {
    id: "quests.delete",
    name: "Delete Quests",
    description: "Delete quests",
    category: "Quests",
    resource: "quests",
    action: "delete",
  },
  {
    id: "quests.approve",
    name: "Approve Quest Completions",
    description: "Approve manual quest completions",
    category: "Quests",
    resource: "quests",
    action: "approve",
  },

  // Badge permissions
  {
    id: "badges.view",
    name: "View Badges",
    description: "View badge list and details",
    category: "Badges",
    resource: "badges",
    action: "view",
  },
  {
    id: "badges.create",
    name: "Create Badges",
    description: "Create new badges",
    category: "Badges",
    resource: "badges",
    action: "create",
  },
  {
    id: "badges.edit",
    name: "Edit Badges",
    description: "Modify existing badges",
    category: "Badges",
    resource: "badges",
    action: "edit",
  },
  {
    id: "badges.delete",
    name: "Delete Badges",
    description: "Delete badges",
    category: "Badges",
    resource: "badges",
    action: "delete",
  },
  {
    id: "badges.award",
    name: "Award Badges",
    description: "Manually award badges to users",
    category: "Badges",
    resource: "badges",
    action: "award",
  },

  // Reward permissions
  {
    id: "rewards.view",
    name: "View Rewards",
    description: "View reward catalog",
    category: "Rewards",
    resource: "rewards",
    action: "view",
  },
  {
    id: "rewards.create",
    name: "Create Rewards",
    description: "Add new rewards",
    category: "Rewards",
    resource: "rewards",
    action: "create",
  },
  {
    id: "rewards.edit",
    name: "Edit Rewards",
    description: "Modify existing rewards",
    category: "Rewards",
    resource: "rewards",
    action: "edit",
  },
  {
    id: "rewards.delete",
    name: "Delete Rewards",
    description: "Remove rewards",
    category: "Rewards",
    resource: "rewards",
    action: "delete",
  },
  {
    id: "rewards.fulfill",
    name: "Fulfill Rewards",
    description: "Process reward redemptions",
    category: "Rewards",
    resource: "rewards",
    action: "fulfill",
  },

  // User management permissions
  {
    id: "users.view",
    name: "View Users",
    description: "View customer user data",
    category: "Users",
    resource: "users",
    action: "view",
  },
  {
    id: "users.edit",
    name: "Edit Users",
    description: "Modify user profiles and data",
    category: "Users",
    resource: "users",
    action: "edit",
  },
  {
    id: "users.delete",
    name: "Delete Users",
    description: "Remove user accounts",
    category: "Users",
    resource: "users",
    action: "delete",
  },
  {
    id: "users.impersonate",
    name: "Impersonate Users",
    description: "Login as customer users",
    category: "Users",
    resource: "users",
    action: "impersonate",
  },

  // Team management permissions
  {
    id: "team.view",
    name: "View Team",
    description: "View team members",
    category: "Team",
    resource: "team",
    action: "view",
  },
  {
    id: "team.invite",
    name: "Invite Members",
    description: "Invite new team members",
    category: "Team",
    resource: "team",
    action: "invite",
  },
  {
    id: "team.edit",
    name: "Edit Members",
    description: "Modify team member roles",
    category: "Team",
    resource: "team",
    action: "edit",
  },
  {
    id: "team.remove",
    name: "Remove Members",
    description: "Remove team members",
    category: "Team",
    resource: "team",
    action: "remove",
  },

  // Settings permissions
  {
    id: "settings.view",
    name: "View Settings",
    description: "Access settings pages",
    category: "Settings",
    resource: "settings",
    action: "view",
  },
  {
    id: "settings.company",
    name: "Edit Company Settings",
    description: "Modify company information",
    category: "Settings",
    resource: "settings",
    action: "company",
  },
  {
    id: "settings.branding",
    name: "Edit Branding",
    description: "Customize platform branding",
    category: "Settings",
    resource: "settings",
    action: "branding",
  },
  {
    id: "settings.integrations",
    name: "Manage Integrations",
    description: "Configure API keys and webhooks",
    category: "Settings",
    resource: "settings",
    action: "integrations",
  },
  {
    id: "settings.billing",
    name: "Manage Billing",
    description: "Access billing and subscription",
    category: "Settings",
    resource: "settings",
    action: "billing",
  },
  {
    id: "settings.security",
    name: "Security Settings",
    description: "Manage security configurations",
    category: "Settings",
    resource: "settings",
    action: "security",
  },

  // Analytics permissions
  {
    id: "analytics.view",
    name: "View Analytics",
    description: "Access analytics dashboard",
    category: "Analytics",
    resource: "analytics",
    action: "view",
  },
  {
    id: "analytics.export",
    name: "Export Data",
    description: "Export analytics data",
    category: "Analytics",
    resource: "analytics",
    action: "export",
  },
  {
    id: "analytics.advanced",
    name: "Advanced Analytics",
    description: "Access detailed analytics",
    category: "Analytics",
    resource: "analytics",
    action: "advanced",
  },

  // System permissions
  {
    id: "system.audit",
    name: "View Audit Logs",
    description: "Access system audit logs",
    category: "System",
    resource: "system",
    action: "audit",
  },
  {
    id: "system.backup",
    name: "System Backup",
    description: "Create and manage backups",
    category: "System",
    resource: "system",
    action: "backup",
  },
  {
    id: "system.maintenance",
    name: "System Maintenance",
    description: "Perform system maintenance",
    category: "System",
    resource: "system",
    action: "maintenance",
  },
]

// Define system roles
export const SYSTEM_ROLES: Role[] = [
  {
    id: "owner",
    name: "Owner",
    description: "Full access to all features and settings. Can manage billing and delete account.",
    level: 100,
    permissions: PERMISSIONS.map((p) => p.id), // All permissions
    isSystem: true,
    color: "#dc2626",
  },
  {
    id: "admin",
    name: "Administrator",
    description: "Full access to platform features. Cannot manage billing or delete account.",
    level: 90,
    permissions: PERMISSIONS.filter((p) => !["settings.billing", "system.maintenance"].includes(p.id)).map((p) => p.id),
    isSystem: true,
    color: "#7c3aed",
  },
  {
    id: "manager",
    name: "Manager",
    description: "Can manage campaigns, quests, badges, and rewards. Limited settings access.",
    level: 70,
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "campaigns.create",
      "campaigns.edit",
      "campaigns.publish",
      "quests.view",
      "quests.create",
      "quests.edit",
      "quests.approve",
      "badges.view",
      "badges.create",
      "badges.edit",
      "badges.award",
      "rewards.view",
      "rewards.create",
      "rewards.edit",
      "rewards.fulfill",
      "users.view",
      "users.edit",
      "team.view",
      "analytics.view",
      "analytics.export",
      "settings.view",
    ],
    isSystem: true,
    color: "#059669",
  },
  {
    id: "moderator",
    name: "Moderator",
    description: "Can approve quest completions and manage reward fulfillment.",
    level: 50,
    permissions: [
      "dashboard.view",
      "campaigns.view",
      "quests.view",
      "quests.approve",
      "badges.view",
      "badges.award",
      "rewards.view",
      "rewards.fulfill",
      "users.view",
      "analytics.view",
    ],
    isSystem: true,
    color: "#ea580c",
  },
  {
    id: "analyst",
    name: "Analyst",
    description: "Read-only access to analytics and reporting features.",
    level: 30,
    permissions: [
      "dashboard.view",
      "dashboard.analytics",
      "campaigns.view",
      "quests.view",
      "badges.view",
      "rewards.view",
      "users.view",
      "analytics.view",
      "analytics.export",
      "analytics.advanced",
    ],
    isSystem: true,
    color: "#0891b2",
  },
  {
    id: "viewer",
    name: "Viewer",
    description: "Read-only access to basic platform features.",
    level: 10,
    permissions: ["dashboard.view", "campaigns.view", "quests.view", "badges.view", "rewards.view", "users.view"],
    isSystem: true,
    color: "#6b7280",
  },
]

// RBAC utility functions
export class RBACService {
  static hasPermission(userRole: Role, permission: string): boolean {
    return userRole.permissions.includes(permission)
  }

  static hasAnyPermission(userRole: Role, permissions: string[]): boolean {
    return permissions.some((permission) => userRole.permissions.includes(permission))
  }

  static hasAllPermissions(userRole: Role, permissions: string[]): boolean {
    return permissions.every((permission) => userRole.permissions.includes(permission))
  }

  static canAccessResource(userRole: Role, resource: string, action: string): boolean {
    const permissionId = `${resource}.${action}`
    return this.hasPermission(userRole, permissionId)
  }

  static getPermissionsByCategory(permissions: string[]): Record<string, Permission[]> {
    const userPermissions = PERMISSIONS.filter((p) => permissions.includes(p.id))
    return userPermissions.reduce(
      (acc, permission) => {
        if (!acc[permission.category]) {
          acc[permission.category] = []
        }
        acc[permission.category].push(permission)
        return acc
      },
      {} as Record<string, Permission[]>,
    )
  }

  static getRoleByLevel(level: number): Role | undefined {
    return SYSTEM_ROLES.find((role) => role.level === level)
  }

  static canManageRole(managerRole: Role, targetRole: Role): boolean {
    return managerRole.level > targetRole.level
  }

  static getAvailableRoles(userRole: Role): Role[] {
    return SYSTEM_ROLES.filter((role) => role.level < userRole.level)
  }

  static validateRolePermissions(permissions: string[]): boolean {
    return permissions.every((permission) => PERMISSIONS.some((p) => p.id === permission))
  }
}

// Permission checking hook
export function usePermissions(userRole: Role) {
  const hasPermission = (permission: string) => {
    return RBACService.hasPermission(userRole, permission)
  }

  const hasAnyPermission = (permissions: string[]) => {
    return RBACService.hasAnyPermission(userRole, permissions)
  }

  const hasAllPermissions = (permissions: string[]) => {
    return RBACService.hasAllPermissions(userRole, permissions)
  }

  const canAccessResource = (resource: string, action: string) => {
    return RBACService.canAccessResource(userRole, resource, action)
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessResource,
    userRole,
  }
}
