"use client"

import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { RefreshCw, Home, AlertCircle, Bug } from "lucide-react"
import Link from "next/link"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error)
  }, [error])

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 pattern-dots">
      <div className="w-full max-w-2xl mx-auto">
        {/* Main Error Card */}
        <Card className="glass-effect border-destructive/20 overflow-hidden">
          <CardContent className="p-0">
            {/* Header Section */}
            <div className="relative bg-gradient-to-br from-background to-destructive/5 p-8 text-center">
              {/* Animated Background Elements */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-destructive/10 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-destructive/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
              </div>
              
              {/* Error Icon */}
              <div className="relative z-10">
                <div className="inline-flex items-center justify-center w-20 h-20 mb-6 rounded-full bg-destructive/10 border border-destructive/20">
                  <AlertCircle className="w-10 h-10 text-destructive" />
                </div>
                
                <h1 className="text-4xl font-bold text-destructive mb-4 tracking-tight">
                  Oops! Something went wrong
                </h1>
                
                <div className="w-24 h-1 bg-gradient-to-r from-transparent via-destructive to-transparent mx-auto mb-6"></div>
              </div>
            </div>

            {/* Content Section */}
            <div className="p-8 space-y-6">
              <div className="text-center space-y-4">
                <h2 className="text-xl font-semibold text-foreground">
                  Unexpected Error
                </h2>
                <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                  We encountered an unexpected error. This has been logged and our team 
                  will investigate. Please try refreshing the page or go back to the dashboard.
                </p>
                
                {/* Error Details (Development Only) */}
                {process.env.NODE_ENV === 'development' && (
                  <details className="mt-4 p-4 bg-muted/50 rounded-lg text-left">
                    <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                      <Bug className="w-4 h-4 inline mr-2" />
                      Error Details (Development)
                    </summary>
                    <pre className="mt-2 text-xs text-muted-foreground overflow-auto">
                      {error.message}
                      {error.digest && `\nDigest: ${error.digest}`}
                    </pre>
                  </details>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button 
                  onClick={reset}
                  variant="neon" 
                  className="w-full sm:w-auto"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                
                <Button 
                  asChild 
                  variant="outline" 
                  className="w-full sm:w-auto hover:border-primary/50 hover:text-primary"
                >
                  <Link href="/" className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Go to Dashboard
                  </Link>
                </Button>
              </div>

              {/* Help Section */}
              <div className="text-center pt-4 border-t border-border/50">
                <p className="text-sm text-muted-foreground mb-3">
                  Still having issues?
                </p>
                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                  <Button 
                    asChild 
                    variant="ghost" 
                    size="sm"
                    className="hover:bg-primary/10 hover:text-primary"
                  >
                    <Link href="/support">
                      Contact Support
                    </Link>
                  </Button>
                  <Button 
                    asChild 
                    variant="ghost" 
                    size="sm"
                    className="hover:bg-primary/10 hover:text-primary"
                  >
                    <Link href="/docs">
                      View Documentation
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-muted-foreground">
            Error ID: {error.digest || 'Unknown'} • {new Date().toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  )
}
