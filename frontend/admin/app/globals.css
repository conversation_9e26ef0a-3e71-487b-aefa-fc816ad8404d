@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Nightsable-inspired utility classes - refined and minimalistic */
  .glass-effect {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.06);
  }

  .neon-border {
    border: 1px solid hsl(var(--primary));
    box-shadow: 0 0 0 1px hsl(var(--primary) / 0.2);
  }

  .gradient-text {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)),
      hsl(var(--neon-blue))
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Subtle hover effects for modern feel */
  .hover-glow {
    transition: all 0.2s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.15);
    transform: translateY(-1px);
  }

  /* Minimal background pattern for depth */
  .pattern-dots {
    background-image: radial-gradient(
      circle,
      hsl(var(--primary) / 0.03) 1px,
      transparent 1px
    );
    background-size: 24px 24px;
  }

  /* Clean focus states */
  .focus-neon:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}

@layer base {
  :root {
    /* Light mode - Nightsable inspired but keeping light theme compatibility */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 76 100% 51%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 76 100% 51%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 0%;
    --chart-1: 76 100% 51%;
    --chart-2: 142 76% 36%;
    --chart-3: 346 77% 49%;
    --chart-4: 43 96% 56%;
    --chart-5: 262 83% 58%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 76 100% 51%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 0% 0%;

    /* Nightsable accent colors - updated with #cdff05 lime green */
    --neon-cyan: 76 100% 51%;
    --neon-blue: 224 76% 48%;
    --neon-purple: 262 83% 58%;
    --neon-pink: 346 77% 49%;

    /* Glass effect colors */
    --glass: 0 0% 100% / 0.05;
    --glass-border: 0 0% 100% / 0.1;
  }
  .dark {
    /* Dark mode - Refined Nightsable aesthetic with lime green */
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;
    --card: 222 84% 6%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 6%;
    --popover-foreground: 210 40% 98%;
    --primary: 76 100% 51%;
    --primary-foreground: 0 0% 0%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 76 100% 51%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 210 40% 98%;
    --chart-1: 76 100% 51%;
    --chart-2: 142 76% 36%;
    --chart-3: 346 77% 49%;
    --chart-4: 43 96% 56%;
    --chart-5: 262 83% 58%;
    --sidebar-background: 222 84% 4%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 76 100% 51%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 217 32% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 32% 17%;
    --sidebar-ring: 210 40% 98%;

    /* Refined accent colors for dark mode with #cdff05 lime green */
    --neon-cyan: 76 100% 51%;
    --neon-blue: 224 76% 48%;
    --neon-purple: 262 83% 58%;
    --neon-pink: 346 77% 49%;

    /* Glass effect colors for dark mode */
    --glass: 255 255 255 / 0.03;
    --glass-border: 255 255 255 / 0.08;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: "Inter", ui-sans-serif, system-ui, sans-serif;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced scrollbar styling for dark theme */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }

  /* Dark mode specific scrollbar */
  .dark ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--neon-cyan) / 0.5);
  }
}
