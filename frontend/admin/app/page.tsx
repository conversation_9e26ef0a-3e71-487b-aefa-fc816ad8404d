"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { AppSidebar } from "@/components/app-sidebar";
import { Dashboard } from "@/components/dashboard";
import { Campaigns } from "@/components/campaigns";
import { Rewards } from "@/components/rewards";
import { Analytics } from "@/components/analytics";
import { Quests } from "@/components/quests";
import { Badges } from "@/components/badges";
import { Settings } from "@/components/settings";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell, HelpCircle, SettingsIcon, LogOut, User } from "lucide-react";

const pages = {
  "/": { title: "Dashboard", component: Dashboard },
  "/campaigns": { title: "Campaigns", component: Campaigns },
  "/quests": { title: "Quests", component: Quests },
  "/badges": { title: "Badges", component: Badges },
  "/rewards": { title: "Rewards", component: Rewards },
  "/analytics": { title: "Analytics", component: Analytics },
  "/settings": { title: "Settings", component: Settings },
};

export default function RootPage() {
  const { isAuthenticated, isLoading, logout, user } = useAuth();
  const router = useRouter();
  const [currentPath, setCurrentPath] = useState("/");

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  const CurrentComponent =
    pages[currentPath as keyof typeof pages]?.component || Dashboard;
  const currentTitle =
    pages[currentPath as keyof typeof pages]?.title || "Dashboard";

  const handleLogout = () => {
    logout();
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
          <SidebarTrigger className="-ml-1 hover:bg-accent/50" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={() => setCurrentPath("/")}
                  className="text-foreground/80 hover:text-primary transition-colors"
                >
                  Admin Console
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-primary font-medium">
                  {currentTitle}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="ml-auto flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <Bell className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src="/placeholder.svg?height=32&width=32"
                      alt="Admin"
                    />
                    <AvatarFallback>AD</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user
                        ? `${user.first_name} ${user.last_name}`
                        : "Admin User"}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.email || "<EMAIL>"}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCurrentPath("/settings")}>
                  <SettingsIcon className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Navigation buttons for demo purposes */}
        <div className="flex gap-2 p-4 border-b border-border/50 bg-muted/30 backdrop-blur-sm">
          {Object.entries(pages).map(([path, { title }]) => (
            <Button
              key={path}
              variant={currentPath === path ? "neon" : "outline"}
              size="sm"
              onClick={() => setCurrentPath(path)}
              className={
                currentPath === path
                  ? ""
                  : "hover:border-primary/50 hover:text-primary"
              }
            >
              {title}
            </Button>
          ))}
        </div>

        <CurrentComponent />
      </SidebarInset>
    </SidebarProvider>
  );
}
