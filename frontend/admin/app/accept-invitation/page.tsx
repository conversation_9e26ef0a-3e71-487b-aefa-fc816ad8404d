"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { UserAPI, InvitationStatus, InvitationAcceptance } from "@/lib/api/users";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, XCircle, Clock, Mail, Building, Shield } from "lucide-react";
import { toast } from "sonner";

export default function AcceptInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [invitationStatus, setInvitationStatus] = useState<InvitationStatus | null>(null);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Load invitation status
  useEffect(() => {
    const loadInvitationStatus = async () => {
      if (!token) {
        setIsLoading(false);
        return;
      }

      try {
        const status = await UserAPI.getInvitationStatus(token);
        setInvitationStatus(status);
      } catch (error) {
        console.error("Failed to load invitation status:", error);
        toast.error("Failed to load invitation details");
      } finally {
        setIsLoading(false);
      }
    };

    loadInvitationStatus();
  }, [token]);

  // Validate password
  const validatePassword = (pwd: string) => {
    if (pwd.length < 8) {
      return "Password must be at least 8 characters long";
    }
    if (!/(?=.*[a-z])/.test(pwd)) {
      return "Password must contain at least one lowercase letter";
    }
    if (!/(?=.*[A-Z])/.test(pwd)) {
      return "Password must contain at least one uppercase letter";
    }
    if (!/(?=.*\d)/.test(pwd)) {
      return "Password must contain at least one number";
    }
    return "";
  };

  // Handle password change
  const handlePasswordChange = (value: string) => {
    setPassword(value);
    const error = validatePassword(value);
    setPasswordError(error);
  };

  // Handle invitation acceptance
  const handleAcceptInvitation = async () => {
    if (!token || !invitationStatus) return;

    // Validate password
    const passwordValidationError = validatePassword(password);
    if (passwordValidationError) {
      setPasswordError(passwordValidationError);
      return;
    }

    // Check password confirmation
    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      setIsAccepting(true);

      const acceptanceData: InvitationAcceptance = {
        token,
        password
      };

      await UserAPI.acceptInvitation(acceptanceData);
      
      toast.success("Account created successfully! You can now log in.");
      
      // Redirect to login page
      router.push("/login?message=account-created");
      
    } catch (error: any) {
      console.error("Failed to accept invitation:", error);
      toast.error(error.detail || "Failed to create account");
    } finally {
      setIsAccepting(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading invitation...</p>
        </div>
      </div>
    );
  }

  // No token provided
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle>Invalid Invitation</CardTitle>
            <CardDescription>
              No invitation token was provided in the URL.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push("/login")} 
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Invalid or expired invitation
  if (!invitationStatus || !invitationStatus.valid) {
    const isExpired = invitationStatus?.expired;
    const isAccepted = invitationStatus?.accepted;

    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle>
              {isAccepted ? "Invitation Already Used" : isExpired ? "Invitation Expired" : "Invalid Invitation"}
            </CardTitle>
            <CardDescription>
              {isAccepted 
                ? "This invitation has already been accepted and an account has been created."
                : isExpired 
                ? "This invitation has expired. Please request a new invitation from your administrator."
                : "This invitation is no longer valid."
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push("/login")} 
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Valid invitation - show acceptance form
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <CardTitle>You're Invited!</CardTitle>
          <CardDescription>
            Complete your account setup to join {invitationStatus.organization_name}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Invitation Details */}
          <div className="bg-muted rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                <strong>Email:</strong> {invitationStatus.email}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                <strong>Organization:</strong> {invitationStatus.organization_name}
              </span>
            </div>
            
            {invitationStatus.role_name && (
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  <strong>Role:</strong> {invitationStatus.role_name}
                </span>
              </div>
            )}
            
            {invitationStatus.expires_at && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  <strong>Expires:</strong> {new Date(invitationStatus.expires_at).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          {/* Account Setup Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={`${invitationStatus.first_name} ${invitationStatus.last_name}`}
                disabled
                className="bg-muted"
              />
            </div>

            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => handlePasswordChange(e.target.value)}
                placeholder="Create a secure password"
              />
              {passwordError && (
                <p className="text-sm text-red-600 mt-1">{passwordError}</p>
              )}
            </div>

            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your password"
              />
            </div>

            {password && confirmPassword && password !== confirmPassword && (
              <Alert>
                <AlertDescription>
                  Passwords do not match
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Password Requirements */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>Password requirements:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>At least 8 characters long</li>
              <li>Contains uppercase and lowercase letters</li>
              <li>Contains at least one number</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleAcceptInvitation}
              disabled={isAccepting || !password || !confirmPassword || !!passwordError || password !== confirmPassword}
              className="w-full"
            >
              {isAccepting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                "Accept Invitation & Create Account"
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push("/login")}
              className="w-full"
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
