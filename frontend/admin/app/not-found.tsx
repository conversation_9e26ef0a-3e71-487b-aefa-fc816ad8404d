"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Home, ArrowLeft, Search, AlertTriangle } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 pattern-dots">
      <div className="w-full max-w-2xl mx-auto">
        {/* Main 404 Card */}
        <Card className="glass-effect border-primary/20 overflow-hidden">
          <CardContent className="p-0">
            {/* Header Section */}
            <div className="relative bg-gradient-to-br from-background to-muted/20 p-8 text-center">
              {/* Animated Background Elements */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-primary/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
              </div>
              
              {/* 404 Number */}
              <div className="relative z-10">
                <div className="inline-flex items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 border border-primary/20">
                  <AlertTriangle className="w-10 h-10 text-primary" />
                </div>
                
                <h1 className="text-8xl font-bold text-primary mb-4 tracking-tight">
                  404
                </h1>
                
                <div className="w-24 h-1 bg-gradient-to-r from-transparent via-primary to-transparent mx-auto mb-6"></div>
              </div>
            </div>

            {/* Content Section */}
            <div className="p-8 space-y-6">
              <div className="text-center space-y-4">
                <h2 className="text-2xl font-semibold text-foreground">
                  Page Not Found
                </h2>
                <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                  The page you're looking for doesn't exist or has been moved. 
                  Don't worry, let's get you back on track.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button 
                  asChild 
                  variant="neon" 
                  className="w-full sm:w-auto"
                >
                  <Link href="/" className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Go Home
                  </Link>
                </Button>
                
                <Button 
                  asChild 
                  variant="outline" 
                  className="w-full sm:w-auto hover:border-primary/50 hover:text-primary"
                >
                  <Link href="javascript:history.back()" className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    Go Back
                  </Link>
                </Button>
              </div>

              {/* Search Suggestion */}
              <div className="text-center pt-4 border-t border-border/50">
                <p className="text-sm text-muted-foreground mb-3">
                  Looking for something specific?
                </p>
                <Button 
                  asChild 
                  variant="ghost" 
                  size="sm"
                  className="hover:bg-primary/10 hover:text-primary"
                >
                  <Link href="/" className="flex items-center gap-2">
                    <Search className="w-4 h-4" />
                    Search Dashboard
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { href: "/", label: "Dashboard", icon: Home },
            { href: "/campaigns", label: "Campaigns", icon: AlertTriangle },
            { href: "/rewards", label: "Rewards", icon: Search },
            { href: "/settings", label: "Settings", icon: ArrowLeft },
          ].map((link) => (
            <Card 
              key={link.href}
              className="group hover:shadow-neon-sm transition-all duration-200 hover:scale-[1.02] cursor-pointer"
            >
              <CardContent className="p-4 text-center">
                <Link href={link.href} className="block">
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-2 rounded-md bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <link.icon className="w-4 h-4 text-primary" />
                    </div>
                    <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                      {link.label}
                    </span>
                  </div>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-muted-foreground">
            Need help? Contact our{" "}
            <Link 
              href="/support" 
              className="text-primary hover:underline font-medium"
            >
              support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
