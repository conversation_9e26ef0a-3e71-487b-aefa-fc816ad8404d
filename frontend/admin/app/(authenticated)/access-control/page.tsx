"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { usePermissions, PermissionGate } from "@/hooks/use-permissions";
import { RoleAP<PERSON>, Role } from "@/lib/api/roles";
import { <PERSON><PERSON><PERSON><PERSON>, User } from "@/lib/api/users";
import { PermissionAPI, Permission } from "@/lib/api/permissions";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Users,
  Shield,
  Settings,
  Plus,
  Eye,
  AlertTriangle,
  Bell,
  HelpCircle,
} from "lucide-react";
import { toast } from "sonner";

// Import the new role management component
import { RoleManagement } from "@/components/rbac/role-management-new";
import { UserManagement } from "@/components/rbac/user-management";
import { PermissionManagement } from "@/components/rbac/permission-management";

function AccessControlContent() {
  const { token, user } = useAuth();
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();

  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    activeUsers: 0,
    systemRoles: 0,
    customRoles: 0,
  });

  // Load overview statistics
  const loadStats = async () => {
    if (!token) return;

    try {
      setIsLoading(true);

      const [users, roles, permissions] = await Promise.all([
        UserAPI.getUsers(token),
        RoleAPI.getRoles(token),
        PermissionAPI.getPermissions(token),
      ]);

      setStats({
        totalUsers: users.length,
        totalRoles: roles.length,
        totalPermissions: permissions.length,
        activeUsers: users.filter((u) => u.is_active).length,
        systemRoles: roles.filter((r) => r.is_system).length,
        customRoles: roles.filter((r) => !r.is_system).length,
      });
    } catch (error: any) {
      console.error("Failed to load access control stats:", error);
      toast.error("Failed to load access control data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, [token]);

  // Check if user has access to access control features
  const canViewUsers = hasPermission("users:read");
  const canViewRoles = hasPermission("roles:read");
  const canManageAccess =
    hasPermission("roles:manage") || hasPermission("users:manage");

  if (permissionsLoading || isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Access Control
            </h1>
            <p className="text-muted-foreground">
              Manage user roles, permissions, and access control
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading access control...</p>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has any access control permissions
  if (!canViewUsers && !canViewRoles) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Access Control
            </h1>
            <p className="text-muted-foreground">
              Manage user roles, permissions, and access control
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground text-center max-w-md">
              You don't have permission to view access control settings. Contact
              your administrator to request access.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Access Control</h1>
          <p className="text-muted-foreground">
            Manage user roles, permissions, and access control
          </p>
        </div>
        <PermissionGate
          permissions={["roles:create", "users:create"]}
          mode="any"
        >
          <div className="flex gap-2">
            <PermissionGate permissions="roles:create">
              <Button variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Create Role
              </Button>
            </PermissionGate>
            <PermissionGate permissions="users:create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Invite User
              </Button>
            </PermissionGate>
          </div>
        </PermissionGate>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <PermissionGate permissions="users:read">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Users ({stats.totalUsers})
            </TabsTrigger>
          </PermissionGate>
          <PermissionGate permissions="roles:read">
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Roles ({stats.totalRoles})
            </TabsTrigger>
          </PermissionGate>
          <PermissionGate permissions="roles:read">
            <TabsTrigger
              value="permissions"
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Permissions ({stats.totalPermissions})
            </TabsTrigger>
          </PermissionGate>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.activeUsers} active users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Roles
                </CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalRoles}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.systemRoles} system, {stats.customRoles} custom
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Permissions
                </CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.totalPermissions}
                </div>
                <p className="text-xs text-muted-foreground">
                  Available permissions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Access Level
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  {canManageAccess ? "Full Access" : "Read Only"}
                </Badge>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {canManageAccess ? "Admin" : "Viewer"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Your access level
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common access control tasks</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <PermissionGate permissions="users:create">
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => setActiveTab("users")}
                  >
                    <div className="text-left">
                      <div className="font-medium">Invite New User</div>
                      <div className="text-sm text-muted-foreground">
                        Send email invitation to new team member
                      </div>
                    </div>
                  </Button>
                </PermissionGate>

                <PermissionGate permissions="roles:create">
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => setActiveTab("roles")}
                  >
                    <div className="text-left">
                      <div className="font-medium">Create Custom Role</div>
                      <div className="text-sm text-muted-foreground">
                        Define new role with specific permissions
                      </div>
                    </div>
                  </Button>
                </PermissionGate>

                <PermissionGate permissions="users:read">
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => setActiveTab("users")}
                  >
                    <div className="text-left">
                      <div className="font-medium">Review User Access</div>
                      <div className="text-sm text-muted-foreground">
                        Audit user permissions and roles
                      </div>
                    </div>
                  </Button>
                </PermissionGate>

                <PermissionGate permissions="roles:read">
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => setActiveTab("permissions")}
                  >
                    <div className="text-left">
                      <div className="font-medium">Manage Permissions</div>
                      <div className="text-sm text-muted-foreground">
                        View and organize system permissions
                      </div>
                    </div>
                  </Button>
                </PermissionGate>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab */}
        <PermissionGate permissions="users:read">
          <TabsContent value="users" className="space-y-4">
            <UserManagement />
          </TabsContent>
        </PermissionGate>

        {/* Roles Tab */}
        <PermissionGate permissions="roles:read">
          <TabsContent value="roles" className="space-y-4">
            <RoleManagement />
          </TabsContent>
        </PermissionGate>

        {/* Permissions Tab */}
        <PermissionGate permissions="roles:read">
          <TabsContent value="permissions" className="space-y-4">
            <PermissionManagement />
          </TabsContent>
        </PermissionGate>
      </Tabs>
    </div>
  );
}

export default function AccessControlPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
          <SidebarTrigger className="-ml-1 hover:bg-accent/50" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="/"
                  className="text-foreground/80 hover:text-primary transition-colors"
                >
                  Admin Console
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-primary font-medium">
                  Access Control
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="ml-auto flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <Bell className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <AccessControlContent />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
