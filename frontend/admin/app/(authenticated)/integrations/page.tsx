"use client";

import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Bell,
  HelpCircle,
  Zap,
  Plus,
  Settings,
  ExternalLink,
} from "lucide-react";

export default function IntegrationsPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
          <SidebarTrigger className="-ml-1 hover:bg-accent/50" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="/"
                  className="text-foreground/80 hover:text-primary transition-colors"
                >
                  Admin Console
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-primary font-medium">
                  Integrations
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="ml-auto flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <Bell className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 hover:text-primary"
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-6 p-6 pt-4 animate-fade-in">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-primary">Integrations</h1>
              <p className="text-muted-foreground mt-1">
                Connect your rewards platform with external services and APIs
              </p>
            </div>
            <Button variant="neon">
              <Plus className="w-4 h-4 mr-2" />
              Add Integration
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            {[
              {
                title: "Active Integrations",
                value: "8",
                icon: Zap,
                change: "+2 this month",
              },
              {
                title: "API Calls Today",
                value: "12.4K",
                icon: ExternalLink,
                change: "+15% from yesterday",
              },
              {
                title: "Success Rate",
                value: "99.2%",
                icon: Settings,
                change: "Excellent performance",
              },
            ].map((stat) => (
              <Card
                key={stat.title}
                className="group hover:shadow-neon-sm transition-all duration-200 hover:scale-[1.02]"
              >
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">
                    {stat.title}
                  </CardTitle>
                  <div className="p-2 rounded-md bg-primary/10 group-hover:bg-primary/15 transition-colors">
                    <stat.icon className="h-4 w-4 text-primary" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stat.change}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Available Integrations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-primary/10">
                  <Zap className="h-5 w-5 text-primary" />
                </div>
                Available Integrations
              </CardTitle>
              <CardDescription>
                Connect with popular services to enhance your rewards platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[
                  {
                    name: "Stripe",
                    description: "Payment processing",
                    status: "Connected",
                  },
                  {
                    name: "SendGrid",
                    description: "Email notifications",
                    status: "Available",
                  },
                  {
                    name: "Slack",
                    description: "Team notifications",
                    status: "Available",
                  },
                  {
                    name: "Webhook",
                    description: "Custom webhooks",
                    status: "Connected",
                  },
                  {
                    name: "Analytics",
                    description: "Data tracking",
                    status: "Available",
                  },
                  {
                    name: "CRM",
                    description: "Customer management",
                    status: "Available",
                  },
                ].map((integration) => (
                  <Card
                    key={integration.name}
                    className="group hover:shadow-neon-sm transition-all duration-200 hover:scale-[1.02]"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-foreground">
                          {integration.name}
                        </h3>
                        <span
                          className={`text-xs px-2 py-1 rounded-full ${
                            integration.status === "Connected"
                              ? "bg-primary/10 text-primary"
                              : "bg-muted text-muted-foreground"
                          }`}
                        >
                          {integration.status}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {integration.description}
                      </p>
                      <Button
                        variant={
                          integration.status === "Connected" ? "outline" : "neon"
                        }
                        size="sm"
                        className="w-full"
                      >
                        {integration.status === "Connected" ? (
                          <>
                            <Settings className="w-4 h-4 mr-2" />
                            Configure
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Connect
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
