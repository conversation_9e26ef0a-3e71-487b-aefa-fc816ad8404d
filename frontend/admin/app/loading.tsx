import { Card, CardContent } from "@/components/ui/card"
import { Loader2, Trophy } from "lucide-react"

export default function Loading() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        <Card className="glass-effect border-primary/20">
          <CardContent className="p-8">
            <div className="text-center space-y-6">
              {/* Logo/Brand */}
              <div className="flex items-center justify-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-md bg-primary text-primary-foreground">
                  <Trophy className="h-6 w-6" />
                </div>
              </div>
              
              {/* Loading Animation */}
              <div className="space-y-4">
                <div className="flex items-center justify-center">
                  <Loader2 className="h-8 w-8 text-primary animate-spin" />
                </div>
                
                <div className="space-y-2">
                  <h2 className="text-lg font-semibold text-foreground">
                    Loading...
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Please wait while we prepare your dashboard
                  </p>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-muted rounded-full h-1">
                <div className="bg-primary h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
