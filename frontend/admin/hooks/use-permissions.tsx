/**
 * Permission hook for checking user permissions in UI components
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/lib/auth-context';
import { PermissionAPI } from '@/lib/api/permissions';

export interface UsePermissionsReturn {
  permissions: string[];
  isLoading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  checkPermission: (permission: string) => Promise<boolean>;
  checkPermissions: (permissions: string[]) => Promise<Record<string, boolean>>;
  refreshPermissions: () => Promise<void>;
}

/**
 * Hook for managing user permissions
 */
export function usePermissions(): UsePermissionsReturn {
  const { token, user } = useAuth();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user permissions
  const loadPermissions = useCallback(async () => {
    if (!token) {
      setPermissions([]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const userPermissions = await PermissionAPI.getCurrentUserPermissions(token);
      setPermissions(userPermissions);
    } catch (err: any) {
      console.error('Failed to load permissions:', err);
      setError(err.detail || 'Failed to load permissions');
      setPermissions([]);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  // Load permissions on mount and when token changes
  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  // Check if user has a specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!permissions.length) return false;
    
    // Direct permission check
    if (permissions.includes(permission)) return true;
    
    // Check for manage permission (implies all other permissions for the resource)
    if (permission.includes(':')) {
      const resource = permission.split(':')[0];
      const managePermission = `${resource}:manage`;
      if (permissions.includes(managePermission)) return true;
    }
    
    return false;
  }, [permissions]);

  // Check if user has any of the specified permissions
  const hasAnyPermission = useCallback((permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission));
  }, [hasPermission]);

  // Check if user has all of the specified permissions
  const hasAllPermissions = useCallback((permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission));
  }, [hasPermission]);

  // Async permission check (useful for server validation)
  const checkPermission = useCallback(async (permission: string): Promise<boolean> => {
    if (!token) return false;
    
    try {
      return await PermissionAPI.hasPermission(token, permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }, [token]);

  // Async multiple permissions check
  const checkPermissions = useCallback(async (permissionList: string[]): Promise<Record<string, boolean>> => {
    if (!token) {
      return permissionList.reduce((acc, permission) => {
        acc[permission] = false;
        return acc;
      }, {} as Record<string, boolean>);
    }
    
    try {
      return await PermissionAPI.checkPermissions(token, permissionList);
    } catch (error) {
      console.error('Error checking permissions:', error);
      return permissionList.reduce((acc, permission) => {
        acc[permission] = false;
        return acc;
      }, {} as Record<string, boolean>);
    }
  }, [token]);

  // Refresh permissions
  const refreshPermissions = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    permissions,
    isLoading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    checkPermission,
    checkPermissions,
    refreshPermissions,
  };
}

/**
 * Hook for checking specific permissions with loading state
 */
export function usePermissionCheck(permission: string | string[]) {
  const { hasPermission, hasAnyPermission, checkPermission, checkPermissions, isLoading } = usePermissions();
  const [isChecking, setIsChecking] = useState(false);
  const [serverResult, setServerResult] = useState<boolean | Record<string, boolean> | null>(null);

  const permissions = Array.isArray(permission) ? permission : [permission];

  // Client-side check (immediate)
  const clientResult = useMemo(() => {
    if (Array.isArray(permission)) {
      return hasAnyPermission(permission);
    }
    return hasPermission(permission);
  }, [permission, hasPermission, hasAnyPermission]);

  // Server-side validation (async)
  const validateWithServer = useCallback(async () => {
    setIsChecking(true);
    try {
      if (Array.isArray(permission)) {
        const results = await checkPermissions(permission);
        setServerResult(results);
        return Object.values(results).some(Boolean);
      } else {
        const result = await checkPermission(permission);
        setServerResult(result);
        return result;
      }
    } catch (error) {
      console.error('Server permission validation failed:', error);
      return clientResult;
    } finally {
      setIsChecking(false);
    }
  }, [permission, checkPermission, checkPermissions, clientResult]);

  return {
    hasPermission: clientResult,
    isLoading: isLoading || isChecking,
    serverResult,
    validateWithServer,
  };
}

/**
 * Permission gate component for conditional rendering
 */
interface PermissionGateProps {
  permissions: string | string[];
  mode?: 'any' | 'all';
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionGate({ 
  permissions, 
  mode = 'any', 
  fallback = null, 
  children 
}: PermissionGateProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isLoading } = usePermissions();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const permissionList = Array.isArray(permissions) ? permissions : [permissions];
  
  let hasRequiredPermissions = false;
  if (mode === 'all') {
    hasRequiredPermissions = hasAllPermissions(permissionList);
  } else {
    hasRequiredPermissions = hasAnyPermission(permissionList);
  }

  return hasRequiredPermissions ? <>{children}</> : <>{fallback}</>;
}

/**
 * Hook for permission-based navigation
 */
export function usePermissionNavigation() {
  const { hasPermission } = usePermissions();

  const canAccess = useCallback((route: string, requiredPermission?: string): boolean => {
    if (!requiredPermission) return true;
    return hasPermission(requiredPermission);
  }, [hasPermission]);

  const getAccessibleRoutes = useCallback((routes: Array<{ path: string; permission?: string }>) => {
    return routes.filter(route => canAccess(route.path, route.permission));
  }, [canAccess]);

  return {
    canAccess,
    getAccessibleRoutes,
  };
}
