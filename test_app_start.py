"""
Test that the FastAPI app can start without errors.
"""

try:
    from main import app
    print("✅ FastAPI app imported successfully!")
    print(f"   App title: {app.title}")
    print(f"   App version: {app.version}")
    
    # Test that settings are loaded
    from app.core.config import settings
    print(f"   Project name: {settings.PROJECT_NAME}")
    print(f"   CORS origins: {settings.BACKEND_CORS_ORIGINS}")
    print(f"   Database URL: {settings.DATABASE_URL[:50]}...")
    
    print("\n🎉 Application is ready to start!")
    print("   Run: uv run uvicorn main:app --reload")
    
except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
