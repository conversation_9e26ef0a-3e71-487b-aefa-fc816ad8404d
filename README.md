# Rewards Platform

A comprehensive SaaS platform for customer engagement through milestone-based rewards, quests, badges, and gamification.

## Project Structure

```
rewards-platform/
├── frontend/
│   └── admin/                 # Next.js Admin Dashboard
│       ├── app/              # Next.js 13+ App Router
│       ├── components/       # React Components
│       ├── lib/             # Utilities and helpers
│       └── package.json
├── backend/
│   └── api/                  # FastAPI Backend
│       ├── app/             # Application code
│       ├── scripts/         # Database scripts
│       ├── requirements.txt
│       └── main.py
├── docker-compose.yml       # Development environment
└── README.md
```

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Development Setup

1. **Clone the repository**
   \`\`\`bash
   git clone <repository-url>
   cd rewards-platform
   \`\`\`

2. **Start with Docker Compose**
   \`\`\`bash
   docker-compose up -d
   \`\`\`

3. **Access the applications**
   - Admin Dashboard: http://localhost:3000
   - API Documentation: http://localhost:8000/docs
   - API Redoc: http://localhost:8000/redoc

### Local Development

#### Frontend (Next.js)
\`\`\`bash
cd frontend/admin
npm install
npm run dev
\`\`\`

#### Backend (FastAPI)
\`\`\`bash
cd backend/api
pip install -r requirements.txt
uvicorn main:app --reload
\`\`\`

## Features

### Admin Dashboard (Next.js)
- 🎯 Campaign Management
- 🎮 Quest Builder
- 🏆 Badge System
- 🎁 Rewards Catalog
- 👥 User Management
- 📊 Analytics Dashboard
- 🔐 Role-Based Access Control
- 🎨 Permission Templates

### API Backend (FastAPI)
- 🚀 High-performance async API
- 🔒 JWT Authentication
- 🏢 Multi-tenant architecture
- 📝 Automatic API documentation
- 🔍 Advanced filtering and pagination
- 📈 Real-time analytics
- 🪝 Webhook system
- 🛡️ Comprehensive security

### Database (Supabase/PostgreSQL)
- 🗄️ Robust schema design
- 🔐 Row-level security
- 📊 Analytics views
- 🔄 Automated functions
- 📝 Audit logging
- 🚀 Performance optimization

## Technology Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **shadcn/ui** - UI components
- **Recharts** - Data visualization
- **Lucide React** - Icons

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - ORM with async support
- **Pydantic** - Data validation
- **JWT** - Authentication
- **Uvicorn** - ASGI server

### Database
- **PostgreSQL** - Primary database
- **Supabase** - Backend-as-a-Service (optional)

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Development environment

## API Documentation

The API is fully documented with OpenAPI/Swagger. Access the interactive documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
