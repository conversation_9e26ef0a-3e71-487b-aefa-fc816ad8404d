"""
Test database connection to diagnose alembic issues.
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from app.core.config import settings

async def test_connection():
    """Test database connection"""
    print(f"Testing connection to: {settings.DATABASE_URL}")
    
    try:
        # Create engine with a short timeout
        engine = create_async_engine(
            settings.DATABASE_URL,
            echo=True,
            pool_timeout=5,
            pool_recycle=300
        )
        
        # Test connection
        async with engine.connect() as conn:
            result = await conn.execute("SELECT 1 as test")
            row = result.fetchone()
            print(f"✅ Database connection successful! Test result: {row}")
            
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print(f"   Error type: {type(e)}")
        return False

if __name__ == "__main__":
    print("🔍 Testing database connection...")
    success = asyncio.run(test_connection())
    
    if success:
        print("\n✅ Database is accessible. Alembic should work.")
        print("   Try: uv run alembic revision --autogenerate -m 'Initial migration'")
    else:
        print("\n❌ Database connection failed.")
        print("   Please check:")
        print("   1. Database is running")
        print("   2. DATABASE_URL is correct")
        print("   3. Network connectivity")
        print("   4. Database credentials")
