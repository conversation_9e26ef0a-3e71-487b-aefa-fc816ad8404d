"""
Comprehensive test to verify the refactored FastAPI app structure.
"""

import pytest
from fastapi.testclient import TestClient

# Test that the app can be imported and created
def test_app_creation():
    """Test that the FastAPI app can be created without errors"""
    try:
        from main import app
        assert app is not None
        assert app.title == "Rewards Platform API"
        print("✅ FastAPI app created successfully")
    except Exception as e:
        print(f"❌ Error creating FastAPI app: {e}")
        raise


def test_app_routes():
    """Test that basic routes are registered"""
    try:
        from main import app
        client = TestClient(app)

        # Test root endpoint
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "Rewards Platform API" in data["message"]
        print("✅ Root endpoint working")

        # Test OpenAPI docs endpoint
        response = client.get("/api/v1/openapi.json")
        assert response.status_code == 200
        print("✅ OpenAPI docs endpoint working")

    except Exception as e:
        print(f"❌ Error testing routes: {e}")
        raise


def test_api_endpoints_structure():
    """Test that all API endpoints are properly registered"""
    try:
        from main import app
        client = TestClient(app)

        # Get OpenAPI schema to check registered endpoints
        response = client.get("/api/v1/openapi.json")
        assert response.status_code == 200
        openapi_schema = response.json()

        # Check that all expected endpoint groups are present
        paths = openapi_schema.get("paths", {})

        expected_endpoint_groups = [
            "/api/v1/auth/",
            "/api/v1/users/",
            "/api/v1/organizations/",
            "/api/v1/campaigns/",
            "/api/v1/quests/",
            "/api/v1/badges/",
            "/api/v1/rewards/",
            "/api/v1/analytics/"
        ]

        for endpoint_group in expected_endpoint_groups:
            # Check if any path starts with this endpoint group
            found = any(path.startswith(endpoint_group) for path in paths.keys())
            assert found, f"Endpoint group {endpoint_group} not found in API"
            print(f"✅ {endpoint_group} endpoints registered")

        print("✅ All API endpoint groups properly registered")

    except Exception as e:
        print(f"❌ Error testing API endpoints structure: {e}")
        raise


def test_models_import():
    """Test that all models can be imported"""
    try:
        from app.models import (
            User, Organization, Role, Category,
            Campaign, Quest, Badge, Reward
        )
        print("✅ All SQLAlchemy models imported successfully")
    except Exception as e:
        print(f"❌ Error importing models: {e}")
        raise


def test_schemas_import():
    """Test that all schemas can be imported"""
    try:
        from app.schemas import (
            User, UserCreate, UserUpdate,
            Organization, OrganizationCreate, OrganizationUpdate,
            Campaign, CampaignCreate, CampaignUpdate,
            Quest, QuestCreate, QuestUpdate,
            Badge, BadgeCreate, BadgeUpdate,
            Reward, RewardCreate, RewardUpdate,
            Token, TokenData
        )
        print("✅ All Pydantic schemas imported successfully")
    except Exception as e:
        print(f"❌ Error importing schemas: {e}")
        raise


def test_services_import():
    """Test that all services can be imported"""
    try:
        from app.services import (
            AuthService, UserService, OrganizationService,
            CampaignService, QuestService, BadgeService,
            RewardService, AnalyticsService
        )
        print("✅ All service classes imported successfully")
    except Exception as e:
        print(f"❌ Error importing services: {e}")
        raise


def test_crud_import():
    """Test that all CRUD classes can be imported"""
    try:
        from app.crud import (
            user_crud, organization_crud, campaign_crud,
            quest_crud, badge_crud, reward_crud
        )
        print("✅ All CRUD classes imported successfully")
    except Exception as e:
        print(f"❌ Error importing CRUD classes: {e}")
        raise


def test_core_modules():
    """Test that core modules can be imported"""
    try:
        from app.core.config import settings
        from app.core.database import get_db, Base
        from app.core.security import create_access_token, verify_password
        print("✅ All core modules imported successfully")
        print(f"✅ Project name: {settings.PROJECT_NAME}")
    except Exception as e:
        print(f"❌ Error importing core modules: {e}")
        raise


def test_unauthorized_endpoints():
    """Test that protected endpoints return 401 without authentication"""
    try:
        from main import app
        client = TestClient(app)

        protected_endpoints = [
            "/api/v1/users/me",
            "/api/v1/campaigns/",
            "/api/v1/quests/",
            "/api/v1/badges/",
            "/api/v1/rewards/",
            "/api/v1/analytics/overview"
        ]

        for endpoint in protected_endpoints:
            response = client.get(endpoint)
            # Should return 401 (Unauthorized) or 422 (Validation Error for missing auth)
            assert response.status_code in [401, 422], f"Endpoint {endpoint} should require authentication"

        print("✅ Protected endpoints properly require authentication")

    except Exception as e:
        print(f"❌ Error testing protected endpoints: {e}")
        raise


if __name__ == "__main__":
    print("🧪 Running comprehensive FastAPI structure tests...\n")

    test_app_creation()
    test_core_modules()
    test_models_import()
    test_schemas_import()
    test_services_import()
    test_crud_import()
    test_app_routes()
    test_api_endpoints_structure()
    test_unauthorized_endpoints()

    print("\n🎉 All tests passed! The refactored FastAPI structure is working correctly!")
    print("\n📋 Summary of what was tested:")
    print("   ✅ FastAPI app creation and configuration")
    print("   ✅ All SQLAlchemy models")
    print("   ✅ All Pydantic schemas")
    print("   ✅ All service classes")
    print("   ✅ All CRUD operations")
    print("   ✅ Core modules (config, database, security)")
    print("   ✅ API endpoint registration")
    print("   ✅ Authentication protection")
    print("\n🚀 Ready for production use!")
