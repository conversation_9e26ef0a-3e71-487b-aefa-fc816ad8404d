"""Fix badge icon null values

Revision ID: 6f7a8b9c0d1e
Revises: 5a8b9c2d3e4f
Create Date: 2025-06-15 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '6f7a8b9c0d1e'
down_revision = '5a8b9c2d3e4f'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update existing NULL icon values to the default 'Trophy'
    op.execute("UPDATE badges SET icon = 'Trophy' WHERE icon IS NULL")
    
    # Make the column NOT NULL since it should always have a value
    op.alter_column('badges', 'icon', nullable=False)


def downgrade() -> None:
    # Make the column nullable again
    op.alter_column('badges', 'icon', nullable=True)
