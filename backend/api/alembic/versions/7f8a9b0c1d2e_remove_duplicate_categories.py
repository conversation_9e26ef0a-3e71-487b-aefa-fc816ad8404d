"""Remove duplicate categories and migrate badges

Revision ID: 7f8a9b0c1d2e
Revises: 6f7a8b9c0d1e
Create Date: 2025-06-15 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '7f8a9b0c1d2e'
down_revision = '6f7a8b9c0d1e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create a temporary table to track which categories to keep
    op.execute("""
        CREATE TEMP TABLE categories_to_keep AS
        WITH ranked_categories AS (
            SELECT 
                id,
                name,
                organization_id,
                created_at,
                ROW_NUMBER() OVER (
                    PARTITION BY name, organization_id 
                    ORDER BY created_at ASC
                ) as rn
            FROM categories
        )
        SELECT id, name, organization_id
        FROM ranked_categories 
        WHERE rn = 1;
    """)
    
    # Create a temporary table to track categories to delete
    op.execute("""
        CREATE TEMP TABLE categories_to_delete AS
        SELECT c.id, c.name, c.organization_id
        FROM categories c
        WHERE c.id NOT IN (SELECT id FROM categories_to_keep);
    """)
    
    # Migrate badges from duplicate categories to the kept categories
    op.execute("""
        UPDATE badges
        SET category_id = (
            SELECT ctk.id
            FROM categories_to_keep ctk
            JOIN categories_to_delete ctd ON ctk.name = ctd.name AND ctk.organization_id = ctd.organization_id
            WHERE ctd.id = badges.category_id
        )
        WHERE category_id IN (SELECT id FROM categories_to_delete);
    """)

    # Migrate quests from duplicate categories to the kept categories
    op.execute("""
        UPDATE quests
        SET category_id = (
            SELECT ctk.id
            FROM categories_to_keep ctk
            JOIN categories_to_delete ctd ON ctk.name = ctd.name AND ctk.organization_id = ctd.organization_id
            WHERE ctd.id = quests.category_id
        )
        WHERE category_id IN (SELECT id FROM categories_to_delete);
    """)

    # Migrate rewards from duplicate categories to the kept categories
    op.execute("""
        UPDATE rewards
        SET category_id = (
            SELECT ctk.id
            FROM categories_to_keep ctk
            JOIN categories_to_delete ctd ON ctk.name = ctd.name AND ctk.organization_id = ctd.organization_id
            WHERE ctd.id = rewards.category_id
        )
        WHERE category_id IN (SELECT id FROM categories_to_delete);
    """)

    # Delete the duplicate categories
    op.execute("""
        DELETE FROM categories
        WHERE id IN (SELECT id FROM categories_to_delete);
    """)
    
    # Clean up temporary tables
    op.execute("DROP TABLE categories_to_keep;")
    op.execute("DROP TABLE categories_to_delete;")


def downgrade() -> None:
    # This migration cannot be easily reversed as we're removing duplicate data
    # The downgrade would require recreating the duplicate categories, but we don't
    # have enough information to restore the exact state
    pass
