"""Fix badge status enum case to match other enums

Revision ID: 5a8b9c2d3e4f
Revises: 4215e7c39150
Create Date: 2025-06-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5a8b9c2d3e4f'
down_revision = '4215e7c39150'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Step 1: Remove the default value first
    op.execute("ALTER TABLE badges ALTER COLUMN status DROP DEFAULT")

    # Step 2: Change the column to text temporarily
    op.execute("ALTER TABLE badges ALTER COLUMN status TYPE text")

    # Step 3: Update existing data to uppercase
    op.execute("UPDATE badges SET status = 'DRAFT' WHERE status = 'draft'")
    op.execute("UPDATE badges SET status = 'ACTIVE' WHERE status = 'active'")
    op.execute("UPDATE badges SET status = 'PAUSED' WHERE status = 'paused'")
    op.execute("UPDATE badges SET status = 'ARCHIVED' WHERE status = 'archived'")

    # Step 4: Drop the old enum
    op.execute("DROP TYPE IF EXISTS badgestatus")

    # Step 5: Create new enum with uppercase values
    badge_status_enum = postgresql.ENUM(
        'ACTIVE', 'DRAFT', 'PAUSED', 'ARCHIVED',
        name='badgestatus',
        create_type=True
    )
    badge_status_enum.create(op.get_bind())

    # Step 6: Change the column back to enum type
    op.execute("ALTER TABLE badges ALTER COLUMN status TYPE badgestatus USING status::badgestatus")

    # Step 7: Set default value
    op.execute("ALTER TABLE badges ALTER COLUMN status SET DEFAULT 'DRAFT'")


def downgrade() -> None:
    # Step 1: Remove the default value first
    op.execute("ALTER TABLE badges ALTER COLUMN status DROP DEFAULT")

    # Step 2: Change the column to text temporarily
    op.execute("ALTER TABLE badges ALTER COLUMN status TYPE text")

    # Step 3: Update existing data to lowercase
    op.execute("UPDATE badges SET status = 'draft' WHERE status = 'DRAFT'")
    op.execute("UPDATE badges SET status = 'active' WHERE status = 'ACTIVE'")
    op.execute("UPDATE badges SET status = 'paused' WHERE status = 'PAUSED'")
    op.execute("UPDATE badges SET status = 'archived' WHERE status = 'ARCHIVED'")

    # Step 4: Drop the new enum
    op.execute("DROP TYPE IF EXISTS badgestatus")

    # Step 5: Create old enum with lowercase values
    badge_status_enum = postgresql.ENUM(
        'active', 'draft', 'paused', 'archived',
        name='badgestatus',
        create_type=True
    )
    badge_status_enum.create(op.get_bind())

    # Step 6: Change the column back to enum type
    op.execute("ALTER TABLE badges ALTER COLUMN status TYPE badgestatus USING status::badgestatus")

    # Step 7: Set default value
    op.execute("ALTER TABLE badges ALTER COLUMN status SET DEFAULT 'draft'")
