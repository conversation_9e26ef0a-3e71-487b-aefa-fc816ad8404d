"""Add a industry column on the organizations table

Revision ID: e4ceef4f7705
Revises: 71d8981a70e2
Create Date: 2025-06-12 23:52:13.827599

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e4ceef4f7705'
down_revision = '71d8981a70e2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add industry column to organizations table
    op.add_column('organizations', sa.Column('industry', sa.String(length=100), nullable=True))


def downgrade() -> None:
    # Remove industry column from organizations table
    op.drop_column('organizations', 'industry')
