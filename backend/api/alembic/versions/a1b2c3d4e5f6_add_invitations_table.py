"""Add invitations table for user invitation system

Revision ID: a1b2c3d4e5f6
Revises: e4ceef4f7705
Create Date: 2025-06-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = '7f8a9b0c1d2e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create invitations table
    op.create_table('invitations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=sa.text('gen_random_uuid()')),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('first_name', sa.String(), nullable=False),
        sa.Column('last_name', sa.String(), nullable=False),
        sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('role_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('token', sa.String(), nullable=False),
        sa.Column('invited_by_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('is_accepted', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_expired', sa.Boolean(), nullable=False, default=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('accepted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('invitation_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=sa.text('NOW()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=sa.text('NOW()')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['invited_by_id'], ['users.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('token')
    )
    
    # Create indexes for better performance
    op.create_index('idx_invitations_email', 'invitations', ['email'])
    op.create_index('idx_invitations_token', 'invitations', ['token'])
    op.create_index('idx_invitations_organization_id', 'invitations', ['organization_id'])
    op.create_index('idx_invitations_invited_by_id', 'invitations', ['invited_by_id'])
    op.create_index('idx_invitations_status', 'invitations', ['is_accepted', 'is_expired'])
    op.create_index('idx_invitations_expires_at', 'invitations', ['expires_at'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_invitations_expires_at', table_name='invitations')
    op.drop_index('idx_invitations_status', table_name='invitations')
    op.drop_index('idx_invitations_invited_by_id', table_name='invitations')
    op.drop_index('idx_invitations_organization_id', table_name='invitations')
    op.drop_index('idx_invitations_token', table_name='invitations')
    op.drop_index('idx_invitations_email', table_name='invitations')
    
    # Drop table
    op.drop_table('invitations')
