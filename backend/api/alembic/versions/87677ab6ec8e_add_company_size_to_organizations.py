"""Add company_size to organizations

Revision ID: 87677ab6ec8e
Revises: a1b2c3d4e5f6
Create Date: 2025-07-05 19:23:18.734291

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '87677ab6ec8e'
down_revision = 'a1b2c3d4e5f6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('badges', 'icon',
               existing_type=sa.VARCHAR(length=100),
               nullable=True,
               existing_server_default=sa.text("'Trophy'::character varying"))
    op.alter_column('invitations', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=False)
    op.alter_column('invitations', 'accepted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('invitations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=False)
    op.alter_column('invitations', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=False)
    op.drop_index('idx_invitations_email', table_name='invitations')
    op.drop_index('idx_invitations_expires_at', table_name='invitations')
    op.drop_index('idx_invitations_invited_by_id', table_name='invitations')
    op.drop_index('idx_invitations_organization_id', table_name='invitations')
    op.drop_index('idx_invitations_status', table_name='invitations')
    op.drop_index('idx_invitations_token', table_name='invitations')
    op.drop_constraint('invitations_token_key', 'invitations', type_='unique')
    op.create_index(op.f('ix_invitations_email'), 'invitations', ['email'], unique=False)
    op.create_index(op.f('ix_invitations_token'), 'invitations', ['token'], unique=True)
    op.drop_constraint('invitations_invited_by_id_fkey', 'invitations', type_='foreignkey')
    op.drop_constraint('invitations_role_id_fkey', 'invitations', type_='foreignkey')
    op.drop_constraint('invitations_organization_id_fkey', 'invitations', type_='foreignkey')
    op.create_foreign_key(None, 'invitations', 'users', ['invited_by_id'], ['id'])
    op.create_foreign_key(None, 'invitations', 'organizations', ['organization_id'], ['id'])
    op.create_foreign_key(None, 'invitations', 'roles', ['role_id'], ['id'])
    op.add_column('organizations', sa.Column('company_size', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'company_size')
    op.drop_constraint(None, 'invitations', type_='foreignkey')
    op.drop_constraint(None, 'invitations', type_='foreignkey')
    op.drop_constraint(None, 'invitations', type_='foreignkey')
    op.create_foreign_key('invitations_organization_id_fkey', 'invitations', 'organizations', ['organization_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('invitations_role_id_fkey', 'invitations', 'roles', ['role_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('invitations_invited_by_id_fkey', 'invitations', 'users', ['invited_by_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_invitations_token'), table_name='invitations')
    op.drop_index(op.f('ix_invitations_email'), table_name='invitations')
    op.create_unique_constraint('invitations_token_key', 'invitations', ['token'])
    op.create_index('idx_invitations_token', 'invitations', ['token'], unique=False)
    op.create_index('idx_invitations_status', 'invitations', ['is_accepted', 'is_expired'], unique=False)
    op.create_index('idx_invitations_organization_id', 'invitations', ['organization_id'], unique=False)
    op.create_index('idx_invitations_invited_by_id', 'invitations', ['invited_by_id'], unique=False)
    op.create_index('idx_invitations_expires_at', 'invitations', ['expires_at'], unique=False)
    op.create_index('idx_invitations_email', 'invitations', ['email'], unique=False)
    op.alter_column('invitations', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False)
    op.alter_column('invitations', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False)
    op.alter_column('invitations', 'accepted_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('invitations', 'expires_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False)
    op.alter_column('badges', 'icon',
               existing_type=sa.VARCHAR(length=100),
               nullable=False,
               existing_server_default=sa.text("'Trophy'::character varying"))
    # ### end Alembic commands ###
