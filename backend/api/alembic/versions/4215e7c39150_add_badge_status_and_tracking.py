"""Add badge status and tracking fields

Revision ID: 4215e7c39150
Revises: b0531ba4316c
Create Date: 2025-06-14 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4215e7c39150'
down_revision = 'b0531ba4316c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add status enum type
    badge_status_enum = postgresql.ENUM(
        'active', 'draft', 'paused', 'archived',
        name='badgestatus',
        create_type=False
    )
    badge_status_enum.create(op.get_bind(), checkfirst=True)
    
    # Add new columns to badges table
    op.add_column('badges', sa.Column('status', badge_status_enum, nullable=False, server_default='draft'))
    op.add_column('badges', sa.Column('times_earned', sa.Integer(), nullable=False, server_default='0'))
    
    # Rename icon_url to icon and change type
    op.alter_column('badges', 'icon_url', new_column_name='icon', type_=sa.String(100), server_default='Trophy')
    
    # Change criteria from JSONB to Text
    op.alter_column('badges', 'criteria', type_=sa.Text())
    
    # Add indexes for better performance
    op.create_index('ix_badges_status', 'badges', ['status'])
    op.create_index('ix_badges_times_earned', 'badges', ['times_earned'])


def downgrade() -> None:
    # Remove indexes
    op.drop_index('ix_badges_times_earned', 'badges')
    op.drop_index('ix_badges_status', 'badges')
    
    # Revert criteria back to JSONB (this might cause data loss)
    op.alter_column('badges', 'criteria', type_=postgresql.JSONB())
    
    # Revert icon back to icon_url
    op.alter_column('badges', 'icon', new_column_name='icon_url', type_=sa.String(500))
    
    # Remove new columns
    op.drop_column('badges', 'times_earned')
    op.drop_column('badges', 'status')
    
    # Drop the enum type
    badge_status_enum = postgresql.ENUM(
        'active', 'draft', 'paused', 'archived',
        name='badgestatus'
    )
    badge_status_enum.drop(op.get_bind(), checkfirst=True)
