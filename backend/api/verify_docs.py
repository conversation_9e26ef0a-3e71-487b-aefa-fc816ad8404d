"""
Verify FastAPI docs configuration without making HTTP requests.
"""

def verify_fastapi_config():
    """Verify FastAPI application configuration"""
    print("🔍 Verifying FastAPI configuration...")
    
    try:
        from main import app
        from app.core.config import settings
        
        print("✅ FastAPI app imported successfully")
        print(f"   App title: {app.title}")
        print(f"   App version: {app.version}")
        print(f"   App description: {app.description}")
        
        # Check OpenAPI configuration
        print(f"\n📚 Documentation URLs:")
        print(f"   OpenAPI JSON: {app.openapi_url}")
        print(f"   Swagger UI: {app.docs_url}")
        print(f"   ReDoc: {app.redoc_url}")
        
        # Check if URLs are properly configured
        if app.docs_url == f"{settings.API_V1_STR}/docs":
            print("✅ Swagger UI URL correctly configured")
        else:
            print(f"❌ Swagger UI URL issue: expected {settings.API_V1_STR}/docs, got {app.docs_url}")
            
        if app.redoc_url == f"{settings.API_V1_STR}/redoc":
            print("✅ ReDoc URL correctly configured")
        else:
            print(f"❌ ReDoc URL issue: expected {settings.API_V1_STR}/redoc, got {app.redoc_url}")
            
        if app.openapi_url == f"{settings.API_V1_STR}/openapi.json":
            print("✅ OpenAPI JSON URL correctly configured")
        else:
            print(f"❌ OpenAPI JSON URL issue: expected {settings.API_V1_STR}/openapi.json, got {app.openapi_url}")
        
        # Check router configuration
        print(f"\n🛣️ Router configuration:")
        print(f"   API V1 prefix: {settings.API_V1_STR}")
        
        # Check if routes are registered
        routes = [route for route in app.routes]
        print(f"   Total routes: {len(routes)}")
        
        # Check for API router
        api_routes = [route for route in routes if hasattr(route, 'path') and route.path.startswith(settings.API_V1_STR)]
        print(f"   API routes: {len(api_routes)}")
        
        if api_routes:
            print("✅ API routes are registered")
            for route in api_routes[:5]:  # Show first 5 routes
                if hasattr(route, 'path') and hasattr(route, 'methods'):
                    print(f"      {list(route.methods)[0] if route.methods else 'GET'} {route.path}")
        else:
            print("❌ No API routes found")
        
        # Check CORS configuration
        print(f"\n🌐 CORS configuration:")
        print(f"   CORS origins: {settings.BACKEND_CORS_ORIGINS}")
        
        # Check middleware
        middleware_count = len(app.user_middleware)
        print(f"   Middleware count: {middleware_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_access_urls():
    """Print the correct URLs to access the documentation"""
    from app.core.config import settings
    
    print(f"\n🚀 Access your API documentation at:")
    print(f"   • Swagger UI: http://localhost:8000{settings.API_V1_STR}/docs")
    print(f"   • ReDoc: http://localhost:8000{settings.API_V1_STR}/redoc")
    print(f"   • OpenAPI JSON: http://localhost:8000{settings.API_V1_STR}/openapi.json")
    print(f"   • Root endpoint: http://localhost:8000/")

if __name__ == "__main__":
    print("🔧 FastAPI Documentation Configuration Verification")
    print("=" * 60)
    
    success = verify_fastapi_config()
    
    if success:
        print_access_urls()
        print("\n✅ Configuration looks good!")
        print("   Restart your FastAPI server and try the URLs above.")
    else:
        print("\n❌ Configuration issues found. Please check the errors above.")
    
    print("=" * 60)
