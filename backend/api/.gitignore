# =============================================================================
# Python .gitignore for FastAPI Backend
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# FastAPI / Web Framework Specific
# =============================================================================

# Static files
static/
media/

# Session files
sessions/

# =============================================================================
# Database Files
# =============================================================================

# SQLite databases
*.db
*.sqlite
*.sqlite3
rewards_platform.db

# PostgreSQL dumps
*.sql
*.dump

# Database migration temp files
alembic/versions/__pycache__/

# =============================================================================
# Development Tools
# =============================================================================

# VS Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OS Generated Files
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Logs and Runtime Files
# =============================================================================

# Application logs
logs/
*.log
*.out

# Runtime files
*.pid
*.seed
*.pid.lock

# =============================================================================
# Security and Secrets
# =============================================================================

# Environment files with secrets
.env.local
.env.production
.env.staging
.env.development

# SSL certificates
*.pem
*.key
*.crt
*.csr

# API keys and secrets
secrets/
.secrets
api_keys.txt

# =============================================================================
# Backup Files
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig

# =============================================================================
# Testing and Coverage
# =============================================================================

# Test databases
test_*.db
test_*.sqlite
test_*.sqlite3

# Test output
test-results/
test-reports/
.coverage.*

# =============================================================================
# Docker (if using Docker)
# =============================================================================

# Docker
.dockerignore
docker-compose.override.yml

# =============================================================================
# Temporary Files
# =============================================================================

# Temporary directories
tmp/
temp/
.tmp/

# Cache directories
.cache/
cache/

# =============================================================================
# Package Manager Files (uv, pip, etc.)
# =============================================================================

# uv
.uv/

# pip
pip-wheel-metadata/

# =============================================================================
# Project Specific
# =============================================================================

# Test files (keep test examples but ignore temporary test files)
test_*.py
!test_main.py
!test_config.py

# Local configuration overrides
local_settings.py
local_config.py

# Development database
dev.db
development.db

# Upload directories
uploads/
files/

# Generated documentation
docs/build/
docs/_build/
