"""
Test configuration loading to verify CORS settings work correctly.
"""

import os
import tempfile
from app.core.config import Settings


def test_cors_configuration():
    """Test that CORS configuration loads correctly from environment variables"""
    
    # Test with environment variable
    os.environ["ALLOWED_ORIGINS"] = "http://localhost:3000,https://example.com"
    os.environ["SECRET_KEY"] = "test-secret-key"
    os.environ["DATABASE_URL"] = "postgresql+asyncpg://user:pass@localhost/test"
    
    try:
        settings = Settings()
        
        # Check that CORS origins are loaded correctly
        assert len(settings.BACKEND_CORS_ORIGINS) == 2
        assert str(settings.BACKEND_CORS_ORIGINS[0]) == "http://localhost:3000/"
        assert str(settings.BACKEND_CORS_ORIGINS[1]) == "https://example.com/"
        
        print("✅ CORS configuration loaded successfully")
        print(f"   CORS Origins: {settings.BACKEND_CORS_ORIGINS}")
        print(f"   Secret Key: {settings.SECRET_KEY[:10]}...")
        print(f"   Database URL: {settings.DATABASE_URL}")
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        raise
    finally:
        # Clean up environment variables
        for key in ["ALLOWED_ORIGINS", "SECRET_KEY", "DATABASE_URL"]:
            if key in os.environ:
                del os.environ[key]


def test_env_file_loading():
    """Test loading configuration from .env file"""
    
    # Create a temporary .env file
    env_content = """
SECRET_KEY=test-secret-from-file
DATABASE_URL=postgresql+asyncpg://user:pass@localhost/testdb
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
PROJECT_NAME=Test Rewards API
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
        f.write(env_content.strip())
        env_file_path = f.name
    
    try:
        # Load settings with custom env file
        settings = Settings(_env_file=env_file_path)
        
        assert settings.SECRET_KEY == "test-secret-from-file"
        assert settings.PROJECT_NAME == "Test Rewards API"
        assert len(settings.BACKEND_CORS_ORIGINS) == 2
        
        print("✅ .env file configuration loaded successfully")
        print(f"   Project Name: {settings.PROJECT_NAME}")
        print(f"   CORS Origins: {settings.BACKEND_CORS_ORIGINS}")
        
    except Exception as e:
        print(f"❌ Error loading .env file: {e}")
        raise
    finally:
        # Clean up temporary file
        os.unlink(env_file_path)


def test_default_values():
    """Test that default values work when no environment variables are set"""
    
    # Ensure no relevant environment variables are set
    env_vars_to_clear = ["ALLOWED_ORIGINS", "SECRET_KEY", "DATABASE_URL", "PROJECT_NAME"]
    original_values = {}
    
    for var in env_vars_to_clear:
        if var in os.environ:
            original_values[var] = os.environ[var]
            del os.environ[var]
    
    try:
        # This should fail because DATABASE_URL is required
        try:
            settings = Settings()
            print("❌ Expected validation error for missing DATABASE_URL")
        except ValueError as e:
            if "DATABASE_URL environment variable is required" in str(e):
                print("✅ Correctly validates required DATABASE_URL")
            else:
                raise
        
        # Test with minimal required config
        os.environ["DATABASE_URL"] = "postgresql+asyncpg://user:pass@localhost/test"
        settings = Settings()
        
        assert settings.PROJECT_NAME == "Rewards Platform API"  # Default value
        assert settings.BACKEND_CORS_ORIGINS == []  # Default empty list
        assert settings.SECRET_KEY == "your-super-secret-key-here"  # Default value
        
        print("✅ Default values work correctly")
        print(f"   Default Project Name: {settings.PROJECT_NAME}")
        print(f"   Default CORS Origins: {settings.BACKEND_CORS_ORIGINS}")
        
    except Exception as e:
        print(f"❌ Error testing default values: {e}")
        raise
    finally:
        # Restore original environment variables
        for var, value in original_values.items():
            os.environ[var] = value
        if "DATABASE_URL" in os.environ:
            del os.environ["DATABASE_URL"]


if __name__ == "__main__":
    print("🧪 Testing configuration loading...\n")
    
    test_cors_configuration()
    print()
    test_env_file_loading()
    print()
    test_default_values()
    
    print("\n🎉 All configuration tests passed!")
    print("\n📋 Configuration is working correctly:")
    print("   ✅ CORS origins load from ALLOWED_ORIGINS environment variable")
    print("   ✅ .env file loading works")
    print("   ✅ Default values are applied correctly")
    print("   ✅ Required fields are validated")
    print("\n🚀 You can now start the FastAPI server!")
