# DEPRECATED: This file contains old Pydantic models
# New structure uses:
# - app/models/ for SQLAlchemy models
# - app/schemas/ for Pydantic schemas
#
# This file is kept for reference during migration
# TODO: Remove after migration is complete

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Base response models
class ErrorResponse(BaseModel):
    message: str
    details: Optional[Dict[str, Any]] = None

class SuccessResponse(BaseModel):
    message: str
    data: Optional[Dict[str, Any]] = None

# Enums
class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

class CampaignStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"
    ARCHIVED = "archived"

class QuestStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"

class QuestValidationType(str, Enum):
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    CODE = "code"
    UPLOAD = "upload"

class QuestFrequency(str, Enum):
    ONE_TIME = "one_time"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    UNLIMITED = "unlimited"

class RewardStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    OUT_OF_STOCK = "out_of_stock"

class BadgeTier(str, Enum):
    BRONZE = "bronze"
    SILVER = "silver"
    GOLD = "gold"
    PLATINUM = "platinum"
    DIAMOND = "diamond"

# Authentication models
class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

class TokenData(BaseModel):
    user_id: Optional[str] = None
    organization_id: Optional[str] = None

# User models
class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    role_id: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role_id: Optional[str] = None
    status: Optional[UserStatus] = None

class User(UserBase):
    id: str
    organization_id: str
    status: UserStatus
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Organization models
class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None
    website: Optional[str] = None

class OrganizationCreate(OrganizationBase):
    pass

class OrganizationUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None

class Organization(OrganizationBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Campaign models
class CampaignBase(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    target_audience: Optional[List[str]] = None
    banner_url: Optional[str] = None

class CampaignCreate(CampaignBase):
    pass

class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    status: Optional[CampaignStatus] = None
    target_audience: Optional[List[str]] = None
    banner_url: Optional[str] = None

class Campaign(CampaignBase):
    id: str
    organization_id: str
    status: CampaignStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Quest models
class QuestBase(BaseModel):
    title: str
    description: Optional[str] = None
    category_id: Optional[str] = None
    points_reward: int = Field(ge=0)
    frequency: QuestFrequency = QuestFrequency.ONE_TIME
    validation_type: QuestValidationType = QuestValidationType.AUTOMATIC
    validation_criteria: Optional[Dict[str, Any]] = None

class QuestCreate(QuestBase):
    campaign_id: str

class QuestUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[str] = None
    points_reward: Optional[int] = Field(None, ge=0)
    frequency: Optional[QuestFrequency] = None
    validation_type: Optional[QuestValidationType] = None
    validation_criteria: Optional[Dict[str, Any]] = None
    status: Optional[QuestStatus] = None

class Quest(QuestBase):
    id: str
    campaign_id: str
    organization_id: str
    status: QuestStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Badge models
class BadgeBase(BaseModel):
    name: str
    description: Optional[str] = None
    icon_url: Optional[str] = None
    tier: BadgeTier = BadgeTier.BRONZE
    criteria: Dict[str, Any]
    points_reward: int = Field(ge=0)

class BadgeCreate(BadgeBase):
    category_id: Optional[str] = None

class BadgeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    icon_url: Optional[str] = None
    tier: Optional[BadgeTier] = None
    criteria: Optional[Dict[str, Any]] = None
    points_reward: Optional[int] = Field(None, ge=0)
    category_id: Optional[str] = None

class Badge(BadgeBase):
    id: str
    organization_id: str
    category_id: Optional[str] = None
    created_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Reward models
class RewardBase(BaseModel):
    name: str
    description: Optional[str] = None
    points_cost: int = Field(ge=0)
    stock_quantity: Optional[int] = Field(None, ge=0)
    image_url: Optional[str] = None

class RewardCreate(RewardBase):
    category_id: Optional[str] = None

class RewardUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    points_cost: Optional[int] = Field(None, ge=0)
    stock_quantity: Optional[int] = Field(None, ge=0)
    image_url: Optional[str] = None
    status: Optional[RewardStatus] = None
    category_id: Optional[str] = None

class Reward(RewardBase):
    id: str
    organization_id: str
    category_id: Optional[str] = None
    status: RewardStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Analytics models
class AnalyticsOverview(BaseModel):
    total_users: int
    active_campaigns: int
    total_quests_completed: int
    total_points_awarded: int
    total_rewards_redeemed: int

class CampaignAnalytics(BaseModel):
    campaign_id: str
    campaign_name: str
    participants: int
    quests_completed: int
    points_awarded: int
    completion_rate: float

class QuestAnalytics(BaseModel):
    quest_id: str
    quest_title: str
    completions: int
    success_rate: float
    average_completion_time: Optional[float] = None

# Pagination models
class PaginationParams(BaseModel):
    page: int = Field(1, ge=1)
    limit: int = Field(10, ge=1, le=100)

class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    limit: int
    pages: int
