"""
Fine-grained permission system for RBAC.
"""

from typing import Dict, List, Set
from enum import Enum


class PermissionAction(str, Enum):
    """Permission actions"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"  # Full CRUD access
    EXECUTE = "execute"  # For special actions


class PermissionScope(str, Enum):
    """Permission scopes"""
    OWN = "own"      # Only resources created by the user
    TEAM = "team"    # Resources within user's team/department
    ALL = "all"      # All resources in organization
    SYSTEM = "system"  # System-wide access


class PermissionResource(str, Enum):
    """System resources"""
    CAMPAIGNS = "campaigns"
    QUESTS = "quests"
    BADGES = "badges"
    REWARDS = "rewards"
    CATEGORIES = "categories"
    USERS = "users"
    ROLES = "roles"
    ORGANIZATIONS = "organizations"
    ANALYTICS = "analytics"
    SETTINGS = "settings"
    INVITATIONS = "invitations"
    AUDIT = "audit"


# Fine-grained permission definitions
FINE_GRAINED_PERMISSIONS = {
    # Campaign permissions
    "campaigns:create": {
        "name": "Create Campaigns",
        "description": "Create new marketing campaigns",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "campaigns:read:own": {
        "name": "View Own Campaigns",
        "description": "View campaigns created by the user",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.OWN
    },
    "campaigns:read:all": {
        "name": "View All Campaigns",
        "description": "View all campaigns in the organization",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "campaigns:update:own": {
        "name": "Edit Own Campaigns",
        "description": "Edit campaigns created by the user",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.OWN
    },
    "campaigns:update:all": {
        "name": "Edit All Campaigns",
        "description": "Edit any campaign in the organization",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "campaigns:delete:own": {
        "name": "Delete Own Campaigns",
        "description": "Delete campaigns created by the user",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.OWN
    },
    "campaigns:delete:all": {
        "name": "Delete All Campaigns",
        "description": "Delete any campaign in the organization",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "campaigns:manage": {
        "name": "Manage Campaigns",
        "description": "Full campaign management access",
        "resource": PermissionResource.CAMPAIGNS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Quest permissions
    "quests:create": {
        "name": "Create Quests",
        "description": "Create new customer quests",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "quests:read:own": {
        "name": "View Own Quests",
        "description": "View quests created by the user",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.OWN
    },
    "quests:read:all": {
        "name": "View All Quests",
        "description": "View all quests in the organization",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "quests:update:own": {
        "name": "Edit Own Quests",
        "description": "Edit quests created by the user",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.OWN
    },
    "quests:update:all": {
        "name": "Edit All Quests",
        "description": "Edit any quest in the organization",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "quests:delete:own": {
        "name": "Delete Own Quests",
        "description": "Delete quests created by the user",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.OWN
    },
    "quests:delete:all": {
        "name": "Delete All Quests",
        "description": "Delete any quest in the organization",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "quests:manage": {
        "name": "Manage Quests",
        "description": "Full quest management access",
        "resource": PermissionResource.QUESTS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Badge permissions
    "badges:create": {
        "name": "Create Badges",
        "description": "Create new achievement badges",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "badges:read:own": {
        "name": "View Own Badges",
        "description": "View badges created by the user",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.READ,
        "scope": PermissionScope.OWN
    },
    "badges:read:all": {
        "name": "View All Badges",
        "description": "View all badges in the organization",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "badges:update:own": {
        "name": "Edit Own Badges",
        "description": "Edit badges created by the user",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.OWN
    },
    "badges:update:all": {
        "name": "Edit All Badges",
        "description": "Edit any badge in the organization",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "badges:delete:own": {
        "name": "Delete Own Badges",
        "description": "Delete badges created by the user",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.OWN
    },
    "badges:delete:all": {
        "name": "Delete All Badges",
        "description": "Delete any badge in the organization",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "badges:manage": {
        "name": "Manage Badges",
        "description": "Full badge management access",
        "resource": PermissionResource.BADGES,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Reward permissions
    "rewards:create": {
        "name": "Create Rewards",
        "description": "Create new customer rewards",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "rewards:read:own": {
        "name": "View Own Rewards",
        "description": "View rewards created by the user",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.OWN
    },
    "rewards:read:all": {
        "name": "View All Rewards",
        "description": "View all rewards in the organization",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "rewards:update:own": {
        "name": "Edit Own Rewards",
        "description": "Edit rewards created by the user",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.OWN
    },
    "rewards:update:all": {
        "name": "Edit All Rewards",
        "description": "Edit any reward in the organization",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "rewards:delete:own": {
        "name": "Delete Own Rewards",
        "description": "Delete rewards created by the user",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.OWN
    },
    "rewards:delete:all": {
        "name": "Delete All Rewards",
        "description": "Delete any reward in the organization",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "rewards:manage": {
        "name": "Manage Rewards",
        "description": "Full reward management access",
        "resource": PermissionResource.REWARDS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Category permissions
    "categories:create": {
        "name": "Create Categories",
        "description": "Create new content categories",
        "resource": PermissionResource.CATEGORIES,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "categories:read": {
        "name": "View Categories",
        "description": "View all categories in the organization",
        "resource": PermissionResource.CATEGORIES,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "categories:update": {
        "name": "Edit Categories",
        "description": "Edit categories in the organization",
        "resource": PermissionResource.CATEGORIES,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "categories:delete": {
        "name": "Delete Categories",
        "description": "Delete categories in the organization",
        "resource": PermissionResource.CATEGORIES,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "categories:manage": {
        "name": "Manage Categories",
        "description": "Full category management access",
        "resource": PermissionResource.CATEGORIES,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # User permissions
    "users:create": {
        "name": "Create Users",
        "description": "Create new users and send invitations",
        "resource": PermissionResource.USERS,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "users:read": {
        "name": "View Users",
        "description": "View all users in the organization",
        "resource": PermissionResource.USERS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "users:update": {
        "name": "Edit Users",
        "description": "Edit user profiles and settings",
        "resource": PermissionResource.USERS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "users:delete": {
        "name": "Delete Users",
        "description": "Delete users from the organization",
        "resource": PermissionResource.USERS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "users:manage": {
        "name": "Manage Users",
        "description": "Full user management access",
        "resource": PermissionResource.USERS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Role permissions
    "roles:create": {
        "name": "Create Roles",
        "description": "Create new roles and permission sets",
        "resource": PermissionResource.ROLES,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "roles:read": {
        "name": "View Roles",
        "description": "View all roles in the organization",
        "resource": PermissionResource.ROLES,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "roles:update": {
        "name": "Edit Roles",
        "description": "Edit role permissions and settings",
        "resource": PermissionResource.ROLES,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "roles:delete": {
        "name": "Delete Roles",
        "description": "Delete non-system roles",
        "resource": PermissionResource.ROLES,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "roles:manage": {
        "name": "Manage Roles",
        "description": "Full role management access",
        "resource": PermissionResource.ROLES,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Organization permissions
    "organizations:read": {
        "name": "View Organization",
        "description": "View organization details and settings",
        "resource": PermissionResource.ORGANIZATIONS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "organizations:update": {
        "name": "Edit Organization",
        "description": "Edit organization settings and configuration",
        "resource": PermissionResource.ORGANIZATIONS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "organizations:manage": {
        "name": "Manage Organization",
        "description": "Full organization management access",
        "resource": PermissionResource.ORGANIZATIONS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Analytics permissions
    "analytics:read": {
        "name": "View Analytics",
        "description": "View analytics and reports",
        "resource": PermissionResource.ANALYTICS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "analytics:manage": {
        "name": "Manage Analytics",
        "description": "Full analytics management access",
        "resource": PermissionResource.ANALYTICS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Settings permissions
    "settings:read": {
        "name": "View Settings",
        "description": "View system and organization settings",
        "resource": PermissionResource.SETTINGS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "settings:update": {
        "name": "Edit Settings",
        "description": "Edit system and organization settings",
        "resource": PermissionResource.SETTINGS,
        "action": PermissionAction.UPDATE,
        "scope": PermissionScope.ALL
    },
    "settings:manage": {
        "name": "Manage Settings",
        "description": "Full settings management access",
        "resource": PermissionResource.SETTINGS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Invitation permissions
    "invitations:create": {
        "name": "Send Invitations",
        "description": "Send user invitations",
        "resource": PermissionResource.INVITATIONS,
        "action": PermissionAction.CREATE,
        "scope": PermissionScope.ALL
    },
    "invitations:read": {
        "name": "View Invitations",
        "description": "View pending and sent invitations",
        "resource": PermissionResource.INVITATIONS,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "invitations:delete": {
        "name": "Cancel Invitations",
        "description": "Cancel pending invitations",
        "resource": PermissionResource.INVITATIONS,
        "action": PermissionAction.DELETE,
        "scope": PermissionScope.ALL
    },
    "invitations:manage": {
        "name": "Manage Invitations",
        "description": "Full invitation management access",
        "resource": PermissionResource.INVITATIONS,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },

    # Audit permissions
    "audit:read": {
        "name": "View Audit Logs",
        "description": "View system audit logs and activity",
        "resource": PermissionResource.AUDIT,
        "action": PermissionAction.READ,
        "scope": PermissionScope.ALL
    },
    "audit:manage": {
        "name": "Manage Audit",
        "description": "Full audit management access",
        "resource": PermissionResource.AUDIT,
        "action": PermissionAction.MANAGE,
        "scope": PermissionScope.ALL
    },
}


def get_all_permissions() -> Dict[str, Dict]:
    """Get all available permissions"""
    return FINE_GRAINED_PERMISSIONS


def get_permissions_by_resource(resource: PermissionResource) -> Dict[str, Dict]:
    """Get permissions for a specific resource"""
    return {
        perm_id: perm_data 
        for perm_id, perm_data in FINE_GRAINED_PERMISSIONS.items()
        if perm_data["resource"] == resource
    }


def get_permissions_by_action(action: PermissionAction) -> Dict[str, Dict]:
    """Get permissions for a specific action"""
    return {
        perm_id: perm_data 
        for perm_id, perm_data in FINE_GRAINED_PERMISSIONS.items()
        if perm_data["action"] == action
    }


def has_permission(user_permissions: List[str], required_permission: str) -> bool:
    """Check if user has a specific permission"""
    if required_permission in user_permissions:
        return True
    
    # Check for manage permission (implies all other permissions for the resource)
    if ":" in required_permission:
        resource = required_permission.split(":")[0]
        manage_permission = f"{resource}:manage"
        if manage_permission in user_permissions:
            return True
    
    return False


def validate_permission_exists(permission: str) -> bool:
    """Validate that a permission exists in the system"""
    return permission in FINE_GRAINED_PERMISSIONS


def get_resource_permissions(resource: str) -> List[str]:
    """Get all permissions for a resource"""
    return [
        perm_id for perm_id, perm_data in FINE_GRAINED_PERMISSIONS.items()
        if perm_data["resource"] == resource
    ]
