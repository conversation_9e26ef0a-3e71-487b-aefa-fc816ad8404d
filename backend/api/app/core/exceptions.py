"""
Custom exceptions with error codes for consistent error handling.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import Optional, Any, Dict
from .error_codes import <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorCodeInfo


class AppException(HTTPException):
    """
    Base application exception that includes error codes.
    
    This exception extends FastAPI's HTTPException to include structured
    error codes that the frontend can use for consistent error handling.
    """
    
    def __init__(
        self,
        error_code: ErrorCode,
        detail: Optional[str] = None,
        headers: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize the exception with an error code.
        
        Args:
            error_code: The standardized error code
            detail: Optional custom error message (defaults to error code's default message)
            headers: Optional HTTP headers
            **kwargs: Additional data to include in the error response
        """
        self.error_code = error_code
        self.additional_data = kwargs
        
        # Get error info from the error code
        error_info = ErrorCodeInfo.get_info(error_code)
        status_code = error_info["status_code"]
        
        # Use custom detail or default message
        if detail is None:
            detail = error_info["message"]
        
        super().__init__(
            status_code=status_code,
            detail=detail,
            headers=headers
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the exception to a dictionary for JSON responses."""
        result = {
            "error_code": self.error_code.value,
            "detail": self.detail,
            "status_code": self.status_code,
        }
        
        # Add any additional data
        if self.additional_data:
            result.update(self.additional_data)
        
        return result


# Convenience exception classes for common error types
class AuthenticationException(AppException):
    """Exception for authentication-related errors."""
    pass


class AuthorizationException(AppException):
    """Exception for authorization-related errors."""
    pass


class ValidationException(AppException):
    """Exception for validation errors."""
    pass


class ResourceNotFoundException(AppException):
    """Exception for resource not found errors."""
    pass


class ConflictException(AppException):
    """Exception for conflict errors (e.g., duplicate resources)."""
    pass


class InvitationException(AppException):
    """Exception for invitation-related errors."""
    pass


class UserException(AppException):
    """Exception for user-related errors."""
    pass


class RoleException(AppException):
    """Exception for role-related errors."""
    pass


class OrganizationException(AppException):
    """Exception for organization-related errors."""
    pass
