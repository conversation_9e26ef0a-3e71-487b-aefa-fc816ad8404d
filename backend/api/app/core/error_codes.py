"""
Global error codes module for consistent error handling across the application.

This module defines standardized error codes that can be used throughout the backend
to provide consistent error responses that the frontend can handle appropriately.
"""

from enum import Enum
from typing import Dict, Any


class ErrorCode(str, Enum):
    """
    Standardized error codes for the application.
    
    Format: CATEGORY_SPECIFIC_ERROR
    Categories: AUTH, USER, INVITATION, ROLE, ORGANIZATION, etc.
    """
    
    # Authentication errors
    AUTH_INVALID_CREDENTIALS = "AUTH_INVALID_CREDENTIALS"
    AUTH_TOKEN_EXPIRED = "AUTH_TOKEN_EXPIRED"
    AUTH_TOKEN_INVALID = "AUTH_TOKEN_INVALID"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH_INSUFFICIENT_PERMISSIONS"
    
    # User errors
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS"
    USER_INACTIVE = "USER_INACTIVE"
    USER_EMAIL_ALREADY_EXISTS = "USER_EMAIL_ALREADY_EXISTS"
    
    # Invitation errors
    INVITATION_ALREADY_EXISTS = "INVITATION_ALREADY_EXISTS"
    INVITATION_NOT_FOUND = "INVITATION_NOT_FOUND"
    INVITATION_EXPIRED = "INVITATION_EXPIRED"
    INVITATION_ALREADY_ACCEPTED = "INVITATION_ALREADY_ACCEPTED"
    INVITATION_INVALID_TOKEN = "INVITATION_INVALID_TOKEN"
    
    # Role errors
    ROLE_NOT_FOUND = "ROLE_NOT_FOUND"
    ROLE_ALREADY_EXISTS = "ROLE_ALREADY_EXISTS"
    ROLE_IN_USE = "ROLE_IN_USE"
    
    # Organization errors
    ORGANIZATION_NOT_FOUND = "ORGANIZATION_NOT_FOUND"
    ORGANIZATION_ALREADY_EXISTS = "ORGANIZATION_ALREADY_EXISTS"
    
    # General errors
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    PERMISSION_DENIED = "PERMISSION_DENIED"


class ErrorCodeInfo:
    """Information about error codes including default messages and HTTP status codes."""
    
    ERROR_INFO: Dict[ErrorCode, Dict[str, Any]] = {
        # Authentication errors
        ErrorCode.AUTH_INVALID_CREDENTIALS: {
            "message": "Invalid email or password",
            "status_code": 401,
        },
        ErrorCode.AUTH_TOKEN_EXPIRED: {
            "message": "Authentication token has expired",
            "status_code": 401,
        },
        ErrorCode.AUTH_TOKEN_INVALID: {
            "message": "Invalid authentication token",
            "status_code": 401,
        },
        ErrorCode.AUTH_INSUFFICIENT_PERMISSIONS: {
            "message": "Insufficient permissions to perform this action",
            "status_code": 403,
        },
        
        # User errors
        ErrorCode.USER_NOT_FOUND: {
            "message": "User not found",
            "status_code": 404,
        },
        ErrorCode.USER_ALREADY_EXISTS: {
            "message": "User already exists",
            "status_code": 409,
        },
        ErrorCode.USER_INACTIVE: {
            "message": "User account is inactive",
            "status_code": 403,
        },
        ErrorCode.USER_EMAIL_ALREADY_EXISTS: {
            "message": "User with this email already exists",
            "status_code": 409,
        },
        
        # Invitation errors
        ErrorCode.INVITATION_ALREADY_EXISTS: {
            "message": "Pending invitation already exists for this email",
            "status_code": 409,
        },
        ErrorCode.INVITATION_NOT_FOUND: {
            "message": "Invitation not found",
            "status_code": 404,
        },
        ErrorCode.INVITATION_EXPIRED: {
            "message": "Invitation has expired",
            "status_code": 410,
        },
        ErrorCode.INVITATION_ALREADY_ACCEPTED: {
            "message": "Invitation has already been accepted",
            "status_code": 409,
        },
        ErrorCode.INVITATION_INVALID_TOKEN: {
            "message": "Invalid invitation token",
            "status_code": 400,
        },
        
        # Role errors
        ErrorCode.ROLE_NOT_FOUND: {
            "message": "Role not found",
            "status_code": 404,
        },
        ErrorCode.ROLE_ALREADY_EXISTS: {
            "message": "Role already exists",
            "status_code": 409,
        },
        ErrorCode.ROLE_IN_USE: {
            "message": "Role is currently in use and cannot be deleted",
            "status_code": 409,
        },
        
        # Organization errors
        ErrorCode.ORGANIZATION_NOT_FOUND: {
            "message": "Organization not found",
            "status_code": 404,
        },
        ErrorCode.ORGANIZATION_ALREADY_EXISTS: {
            "message": "Organization already exists",
            "status_code": 409,
        },
        
        # General errors
        ErrorCode.VALIDATION_ERROR: {
            "message": "Validation error",
            "status_code": 422,
        },
        ErrorCode.INTERNAL_SERVER_ERROR: {
            "message": "Internal server error",
            "status_code": 500,
        },
        ErrorCode.RESOURCE_NOT_FOUND: {
            "message": "Resource not found",
            "status_code": 404,
        },
        ErrorCode.PERMISSION_DENIED: {
            "message": "Permission denied",
            "status_code": 403,
        },
    }
    
    @classmethod
    def get_info(cls, error_code: ErrorCode) -> Dict[str, Any]:
        """Get error information for a given error code."""
        return cls.ERROR_INFO.get(error_code, {
            "message": "Unknown error",
            "status_code": 500,
        })
    
    @classmethod
    def get_message(cls, error_code: ErrorCode) -> str:
        """Get the default message for an error code."""
        return cls.get_info(error_code).get("message", "Unknown error")
    
    @classmethod
    def get_status_code(cls, error_code: ErrorCode) -> int:
        """Get the HTTP status code for an error code."""
        return cls.get_info(error_code).get("status_code", 500)
