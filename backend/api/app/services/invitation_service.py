"""
Invitation service for business logic.
"""

from typing import List, Dict, Any, Optional
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.invitation import invitation_crud
from app.crud.user import user_crud
from app.crud.organization import organization_crud
from app.crud.role import role_crud
from app.schemas.invitation import InvitationCreate, InvitationAccept, InvitationStatus
from app.schemas.user import UserCreate
from app.services.email_service import EmailService
from app.core.security import get_password_hash


class InvitationService:
    """Invitation service for business logic"""

    @staticmethod
    async def create_invitation(
        db: AsyncSession,
        invitation_data: InvitationCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Dict[str, Any]:
        """Create and send a user invitation"""
        
        # Check if user already exists
        existing_user = await user_crud.get_by_email(db, email=invitation_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Check if there's already a pending invitation
        existing_invitation = await invitation_crud.get_by_email_and_organization(
            db, email=invitation_data.email, organization_id=current_user_org_id
        )
        if existing_invitation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Pending invitation already exists for this email"
            )
        
        # Verify organization exists
        organization = await organization_crud.get(db, id=current_user_org_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Verify role exists if provided
        role = None
        if invitation_data.role_id:
            role = await role_crud.get(db, id=invitation_data.role_id)
            if not role or str(role.organization_id) != current_user_org_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found or not in current organization"
                )
        
        # Get inviter information
        inviter = await user_crud.get(db, id=current_user_id)
        if not inviter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inviter not found"
            )
        
        # Create invitation
        invitation = await invitation_crud.create_invitation(
            db=db,
            invitation_data=invitation_data,
            organization_id=current_user_org_id,
            invited_by_id=current_user_id
        )
        
        # Send invitation email
        inviter_name = f"{inviter.first_name} {inviter.last_name}"
        role_name = role.name if role else None
        
        email_sent = EmailService.send_invitation_email(
            to_email=invitation.email,
            to_name=f"{invitation.first_name} {invitation.last_name}",
            inviter_name=inviter_name,
            organization_name=organization.name,
            invitation_token=invitation.token,
            role_name=role_name
        )
        
        return {
            "invitation_id": str(invitation.id),
            "email": invitation.email,
            "token": invitation.token,
            "expires_at": invitation.expires_at,
            "email_sent": email_sent,
            "organization_name": organization.name,
            "role_name": role_name
        }
    
    @staticmethod
    async def get_invitation_status(
        db: AsyncSession,
        token: str
    ) -> InvitationStatus:
        """Get invitation status by token"""
        
        invitation = await invitation_crud.get_by_token(db, token=token)
        if not invitation:
            return InvitationStatus(
                valid=False,
                expired=True,
                accepted=False
            )
        
        return InvitationStatus(
            valid=invitation.is_valid,
            expired=invitation.is_expired or invitation.expires_at < invitation.expires_at,
            accepted=invitation.is_accepted,
            email=invitation.email,
            first_name=invitation.first_name,
            last_name=invitation.last_name,
            organization_name=invitation.organization.name if invitation.organization else None,
            role_name=invitation.role.name if invitation.role else None,
            expires_at=invitation.expires_at
        )
    
    @staticmethod
    async def accept_invitation(
        db: AsyncSession,
        acceptance_data: InvitationAccept
    ) -> Dict[str, Any]:
        """Accept an invitation and create user account"""
        
        # Get invitation
        invitation = await invitation_crud.get_by_token(db, token=acceptance_data.token)
        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invitation not found"
            )
        
        # Check if invitation is valid
        if not invitation.is_valid:
            if invitation.is_accepted:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has already been accepted"
                )
            elif invitation.is_expired:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has expired"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation is no longer valid"
                )
        
        # Check if user already exists
        existing_user = await user_crud.get_by_email(db, email=invitation.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create user account
        user_create = UserCreate(
            email=invitation.email,
            first_name=invitation.first_name,
            last_name=invitation.last_name,
            password=acceptance_data.password,
            organization_id=str(invitation.organization_id),
            role_id=str(invitation.role_id) if invitation.role_id else None
        )
        
        user = await user_crud.create(db, obj_in=user_create)
        
        # Accept invitation
        await invitation_crud.accept_invitation(db, invitation=invitation)
        
        # Send welcome email
        EmailService.send_welcome_email(
            to_email=user.email,
            to_name=f"{user.first_name} {user.last_name}",
            organization_name=invitation.organization.name if invitation.organization else "Organization"
        )
        
        return {
            "user_id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "organization_id": str(user.organization_id),
            "role_id": str(user.role_id) if user.role_id else None,
            "message": "Account created successfully"
        }
    
    @staticmethod
    async def get_organization_invitations(
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100,
        pending_only: bool = False
    ) -> List[Dict[str, Any]]:
        """Get invitations for an organization"""
        
        if pending_only:
            invitations = await invitation_crud.get_pending_invitations(
                db, organization_id=organization_id, skip=skip, limit=limit
            )
        else:
            invitations = await invitation_crud.get_organization_invitations(
                db, organization_id=organization_id, skip=skip, limit=limit
            )
        
        result = []
        for invitation in invitations:
            result.append({
                "id": str(invitation.id),
                "email": invitation.email,
                "first_name": invitation.first_name,
                "last_name": invitation.last_name,
                "role_name": invitation.role.name if invitation.role else None,
                "invited_by": f"{invitation.invited_by.first_name} {invitation.invited_by.last_name}",
                "is_accepted": invitation.is_accepted,
                "is_expired": invitation.is_expired,
                "expires_at": invitation.expires_at,
                "created_at": invitation.created_at,
                "accepted_at": invitation.accepted_at
            })
        
        return result
    
    @staticmethod
    async def cancel_invitation(
        db: AsyncSession,
        invitation_id: str,
        current_user_org_id: str
    ) -> bool:
        """Cancel an invitation"""
        
        invitation = await invitation_crud.get(db, id=invitation_id)
        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invitation not found"
            )
        
        # Verify invitation belongs to current organization
        if str(invitation.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot cancel invitation from another organization"
            )
        
        # Cannot cancel accepted invitations
        if invitation.is_accepted:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel accepted invitation"
            )
        
        return await invitation_crud.delete_invitation(db, invitation_id=invitation_id)
