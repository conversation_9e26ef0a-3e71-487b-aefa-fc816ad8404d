"""
Permission service with business logic.
"""

from typing import List, Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTTPException, status

from app.models.user import User
from app.models.role import Role
from app.schemas.permission import PermissionTemplate
from app.core.permissions import (
    get_all_permissions,
    get_permissions_by_resource,
    has_permission,
    validate_permission_exists,
    FINE_GRAINED_PERMISSIONS
)
from app.crud.user import user_crud
from app.crud.role import role_crud


class PermissionService:
    """Permission service for business logic"""

    @staticmethod
    async def get_user_permissions(
        db: AsyncSession,
        user_id: str,
        organization_id: str
    ) -> List[str]:
        """Get effective permissions for a user"""
        user = await user_crud.get(db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if str(user.organization_id) != organization_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User not in current organization"
            )
        
        # Get user's role permissions
        if user.role_id:
            role = await role_crud.get(db, id=user.role_id)
            if role:
                return role.permissions
        
        return []

    @staticmethod
    async def check_user_permission(
        db: AsyncSession,
        user_id: str,
        permission: str,
        organization_id: str
    ) -> bool:
        """Check if a user has a specific permission"""
        user_permissions = await PermissionService.get_user_permissions(
            db, user_id, organization_id
        )
        return has_permission(user_permissions, permission)

    @staticmethod
    async def get_permission_templates(
        db: AsyncSession,
        organization_id: str
    ) -> List[PermissionTemplate]:
        """Get available permission templates"""
        # System templates
        templates = [
            PermissionTemplate(
                id="admin",
                name="Administrator",
                description="Full access to all features and settings",
                permissions=list(FINE_GRAINED_PERMISSIONS.keys()),
                level=100,
                color="#dc2626",
                is_system=True,
                category="System"
            ),
            PermissionTemplate(
                id="manager",
                name="Manager",
                description="Manage content and view analytics",
                permissions=[
                    "campaigns:manage", "quests:manage", "badges:manage", "rewards:manage",
                    "categories:manage", "analytics:read", "users:read"
                ],
                level=80,
                color="#7c3aed",
                is_system=True,
                category="Management"
            ),
            PermissionTemplate(
                id="editor",
                name="Content Editor",
                description="Create and edit content",
                permissions=[
                    "campaigns:create", "campaigns:read:all", "campaigns:update:all",
                    "quests:create", "quests:read:all", "quests:update:all",
                    "badges:create", "badges:read:all", "badges:update:all",
                    "rewards:create", "rewards:read:all", "rewards:update:all",
                    "categories:read", "categories:create", "categories:update"
                ],
                level=60,
                color="#059669",
                is_system=True,
                category="Content"
            ),
            PermissionTemplate(
                id="creator",
                name="Content Creator",
                description="Create own content and view others",
                permissions=[
                    "campaigns:create", "campaigns:read:all", "campaigns:update:own",
                    "quests:create", "quests:read:all", "quests:update:own",
                    "badges:create", "badges:read:all", "badges:update:own",
                    "rewards:create", "rewards:read:all", "rewards:update:own",
                    "categories:read"
                ],
                level=40,
                color="#0891b2",
                is_system=True,
                category="Content"
            ),
            PermissionTemplate(
                id="analyst",
                name="Data Analyst",
                description="View analytics and reports",
                permissions=[
                    "campaigns:read:all", "quests:read:all", "badges:read:all",
                    "rewards:read:all", "analytics:read", "users:read"
                ],
                level=30,
                color="#ea580c",
                is_system=True,
                category="Analytics"
            ),
            PermissionTemplate(
                id="viewer",
                name="Viewer",
                description="Read-only access to content",
                permissions=[
                    "campaigns:read:all", "quests:read:all", "badges:read:all",
                    "rewards:read:all", "categories:read"
                ],
                level=20,
                color="#6b7280",
                is_system=True,
                category="Basic"
            ),
            PermissionTemplate(
                id="limited",
                name="Limited User",
                description="View only own content",
                permissions=[
                    "campaigns:read:own", "quests:read:own", "badges:read:own",
                    "rewards:read:own", "categories:read"
                ],
                level=10,
                color="#9ca3af",
                is_system=True,
                category="Basic"
            )
        ]

        # TODO: Add custom templates from database
        # custom_templates = await get_custom_templates(db, organization_id)
        # templates.extend(custom_templates)

        return templates

    @staticmethod
    async def validate_permissions(permissions: List[str]) -> Dict[str, bool]:
        """Validate a list of permissions"""
        results = {}
        for permission in permissions:
            results[permission] = validate_permission_exists(permission)
        return results

    @staticmethod
    async def get_permission_conflicts(permissions: List[str]) -> List[Dict[str, Any]]:
        """Detect potential permission conflicts"""
        conflicts = []
        
        # Check for redundant permissions (e.g., having both manage and specific permissions)
        for permission in permissions:
            if ":" in permission:
                resource = permission.split(":")[0]
                manage_permission = f"{resource}:manage"
                
                if manage_permission in permissions and permission != manage_permission:
                    conflicts.append({
                        "type": "redundant",
                        "permission": permission,
                        "implied_by": manage_permission,
                        "severity": "low",
                        "description": f"Permission '{permission}' is redundant because '{manage_permission}' is also granted"
                    })
        
        return conflicts

    @staticmethod
    async def get_permission_recommendations(
        current_permissions: List[str],
        role_level: int
    ) -> List[Dict[str, Any]]:
        """Get permission recommendations based on role level and current permissions"""
        recommendations = []
        
        # Recommend manage permissions for high-level roles
        if role_level >= 80:
            manage_permissions = [
                perm for perm in FINE_GRAINED_PERMISSIONS.keys()
                if perm.endswith(":manage")
            ]
            
            missing_manage = [
                perm for perm in manage_permissions
                if perm not in current_permissions
            ]
            
            if missing_manage:
                recommendations.append({
                    "type": "add_manage_permissions",
                    "permissions": missing_manage,
                    "reason": "High-level roles typically have manage permissions",
                    "priority": "medium"
                })
        
        # Recommend read permissions as baseline
        if role_level >= 20:
            read_permissions = [
                perm for perm in FINE_GRAINED_PERMISSIONS.keys()
                if ":read" in perm
            ]
            
            missing_read = [
                perm for perm in read_permissions
                if perm not in current_permissions and not any(
                    manage_perm in current_permissions
                    for manage_perm in [perm.split(":")[0] + ":manage"]
                )
            ]
            
            if missing_read:
                recommendations.append({
                    "type": "add_read_permissions",
                    "permissions": missing_read[:5],  # Limit to top 5
                    "reason": "Basic read access is recommended for most roles",
                    "priority": "low"
                })
        
        return recommendations

    @staticmethod
    async def get_permission_usage_stats(
        db: AsyncSession,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get permission usage statistics"""
        # Get all roles in organization
        roles = await role_crud.get_by_organization(db, organization_id)
        
        permission_stats = {}
        total_permissions = len(FINE_GRAINED_PERMISSIONS)
        
        # Count permission usage across roles
        for role in roles:
            for permission in role.permissions:
                if permission not in permission_stats:
                    permission_stats[permission] = {
                        "count": 0,
                        "roles": []
                    }
                permission_stats[permission]["count"] += 1
                permission_stats[permission]["roles"].append(role.name)
        
        # Calculate statistics
        used_permissions = len(permission_stats)
        unused_permissions = total_permissions - used_permissions
        
        most_used = sorted(
            permission_stats.items(),
            key=lambda x: x[1]["count"],
            reverse=True
        )[:10]
        
        return {
            "total_permissions": total_permissions,
            "used_permissions": used_permissions,
            "unused_permissions": unused_permissions,
            "usage_percentage": (used_permissions / total_permissions) * 100,
            "most_used_permissions": [
                {
                    "permission": perm,
                    "count": data["count"],
                    "roles": data["roles"]
                }
                for perm, data in most_used
            ],
            "permission_distribution": permission_stats
        }

    @staticmethod
    def get_permission_hierarchy() -> Dict[str, List[str]]:
        """Get permission hierarchy showing implications"""
        hierarchy = {}
        
        # Build hierarchy based on manage permissions
        for permission_id, permission_data in FINE_GRAINED_PERMISSIONS.items():
            resource = permission_data["resource"]
            manage_key = f"{resource}:manage"
            
            if permission_id.endswith(":manage"):
                # Manage permission implies all other permissions for the resource
                implied_permissions = [
                    perm_id for perm_id, perm_data in FINE_GRAINED_PERMISSIONS.items()
                    if perm_data["resource"] == resource and perm_id != permission_id
                ]
                hierarchy[permission_id] = implied_permissions
        
        return hierarchy
