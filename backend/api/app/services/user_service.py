"""
User service with business logic.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.user import user_crud
from app.crud.organization import organization_crud
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash


class UserService:
    """User service for business logic"""
    
    @staticmethod
    async def create_user(
        db: AsyncSession,
        user_create: UserCreate,
        current_user_org_id: str
    ) -> User:
        """Create a new user with business logic validation"""
        
        # Ensure user is being created in the same organization as current user
        if user_create.organization_id and user_create.organization_id != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create user for another organization"
            )
        
        # Set organization_id if not provided
        if not user_create.organization_id:
            user_create.organization_id = current_user_org_id
        
        # Check if email already exists
        existing_user = await user_crud.get_by_email(db, email=user_create.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Verify organization exists
        organization = await organization_crud.get(db, id=user_create.organization_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Create user
        return await user_crud.create(db, obj_in=user_create)
    
    @staticmethod
    async def get_users_by_organization(
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """Get all users in an organization"""
        return await user_crud.get_by_organization(
            db, 
            organization_id=organization_id,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    async def update_user(
        db: AsyncSession,
        user_id: str,
        user_update: UserUpdate,
        current_user_org_id: str
    ) -> User:
        """Update user with business logic validation"""
        
        # Get existing user
        user = await user_crud.get(db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Ensure user belongs to current user's organization
        if str(user.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot update user from another organization"
            )
        
        # Check email uniqueness if email is being updated
        if user_update.email and user_update.email != user.email:
            existing_user = await user_crud.get_by_email(db, email=user_update.email)
            if existing_user and str(existing_user.id) != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
        
        # Update user
        return await user_crud.update(db, db_obj=user, obj_in=user_update)
    
    @staticmethod
    async def delete_user(
        db: AsyncSession,
        user_id: str,
        current_user_org_id: str
    ) -> User:
        """Delete user with business logic validation"""
        
        # Get existing user
        user = await user_crud.get(db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Ensure user belongs to current user's organization
        if str(user.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete user from another organization"
            )
        
        # Prevent deletion of superusers (business rule)
        if user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete superuser"
            )
        
        # Delete user
        return await user_crud.remove(db, id=user_id)

    @staticmethod
    async def assign_role(
        db: AsyncSession,
        user_id: str,
        role_id: str,
        current_user_org_id: str
    ) -> User:
        """Assign role to user with validation"""
        from app.crud.role import role_crud

        # Get existing user
        user = await user_crud.get(db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Ensure user belongs to current user's organization
        if str(user.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot assign role to user from another organization"
            )

        # Validate role exists and belongs to same organization
        role = await role_crud.get(db, id=role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )

        if str(role.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot assign role from another organization"
            )

        # Assign role
        return await user_crud.assign_role(
            db=db,
            user_id=user_id,
            role_id=role_id,
            organization_id=current_user_org_id
        )

    @staticmethod
    async def get_user_permissions(
        db: AsyncSession,
        user_id: str,
        current_user_org_id: str
    ) -> List[str]:
        """Get effective permissions for a user"""
        from app.services.permission_service import PermissionService

        return await PermissionService.get_user_permissions(
            db=db,
            user_id=user_id,
            organization_id=current_user_org_id
        )
