"""
Role service with business logic.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from fastapi import HTTPException, status

from app.models.role import Role
from app.models.user import User
from app.schemas.role import RoleCreate, RoleUpdate
from app.crud.role import role_crud
from app.crud.user import user_crud
from app.services.permission_service import PermissionService
from app.core.permissions import validate_permission_exists


class RoleService:
    """Role service for business logic"""

    @staticmethod
    async def get_roles_by_organization(
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100,
        include_system: bool = True
    ) -> List[Role]:
        """Get roles by organization with optional filtering"""
        roles = await role_crud.get_by_organization(
            db=db,
            organization_id=organization_id,
            skip=skip,
            limit=limit
        )
        
        if not include_system:
            roles = [role for role in roles if not role.is_system]
        
        return roles

    @staticmethod
    async def get_role_by_id(
        db: AsyncSession,
        role_id: str,
        organization_id: str
    ) -> Role:
        """Get role by ID with organization validation"""
        role = await role_crud.get(db, id=role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        if str(role.organization_id) != organization_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Role not in current organization"
            )
        
        return role

    @staticmethod
    async def create_role(
        db: AsyncSession,
        role_create: RoleCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Role:
        """Create a new role with validation"""
        # Validate permissions exist
        invalid_permissions = []
        for permission in role_create.permissions:
            if not validate_permission_exists(permission):
                invalid_permissions.append(permission)
        
        if invalid_permissions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid permissions: {', '.join(invalid_permissions)}"
            )
        
        # Check if role name already exists in organization
        existing_role = await role_crud.get_by_name_and_organization(
            db=db,
            name=role_create.name,
            organization_id=current_user_org_id
        )
        
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role name already exists in organization"
            )
        
        # Set organization_id from current user
        role_create.organization_id = current_user_org_id
        
        return await role_crud.create(db, obj_in=role_create)

    @staticmethod
    async def update_role(
        db: AsyncSession,
        role_id: str,
        role_update: RoleUpdate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Role:
        """Update a role with validation"""
        role = await RoleService.get_role_by_id(
            db=db,
            role_id=role_id,
            organization_id=current_user_org_id
        )
        
        # Prevent modification of system roles
        if role.is_system and not role_update.permissions:
            # Allow permission updates for system roles but not other fields
            if any([role_update.name, role_update.description, role_update.level, 
                   role_update.color, role_update.is_system]):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Cannot modify system role properties"
                )
        
        # Validate permissions if provided
        if role_update.permissions:
            invalid_permissions = []
            for permission in role_update.permissions:
                if not validate_permission_exists(permission):
                    invalid_permissions.append(permission)
            
            if invalid_permissions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid permissions: {', '.join(invalid_permissions)}"
                )
        
        # Check if new name conflicts (if name is being updated)
        if role_update.name and role_update.name != role.name:
            existing_role = await role_crud.get_by_name_and_organization(
                db=db,
                name=role_update.name,
                organization_id=current_user_org_id
            )
            
            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role name already exists in organization"
                )
        
        return await role_crud.update(db, db_obj=role, obj_in=role_update)

    @staticmethod
    async def delete_role(
        db: AsyncSession,
        role_id: str,
        current_user_id: str,
        current_user_org_id: str
    ) -> None:
        """Delete a role with validation"""
        role = await RoleService.get_role_by_id(
            db=db,
            role_id=role_id,
            organization_id=current_user_org_id
        )
        
        # Prevent deletion of system roles
        if role.is_system:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete system roles"
            )
        
        # Check if role is assigned to any users
        users_with_role = await user_crud.get_by_role(db, role_id=role_id)
        if users_with_role:
            user_emails = [user.email for user in users_with_role]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete role assigned to users: {', '.join(user_emails)}"
            )
        
        await role_crud.remove(db, id=role_id)

    @staticmethod
    async def clone_role(
        db: AsyncSession,
        role_id: str,
        new_name: str,
        new_description: Optional[str],
        current_user_id: str,
        current_user_org_id: str
    ) -> Role:
        """Clone an existing role"""
        source_role = await RoleService.get_role_by_id(
            db=db,
            role_id=role_id,
            organization_id=current_user_org_id
        )
        
        # Check if new name already exists
        existing_role = await role_crud.get_by_name_and_organization(
            db=db,
            name=new_name,
            organization_id=current_user_org_id
        )
        
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role name already exists in organization"
            )
        
        # Create new role with cloned properties
        role_create = RoleCreate(
            name=new_name,
            description=new_description or f"Copy of {source_role.name}",
            level=source_role.level,
            color=source_role.color,
            permissions=source_role.permissions.copy(),
            is_system=False,  # Cloned roles are never system roles
            organization_id=current_user_org_id
        )
        
        return await role_crud.create(db, obj_in=role_create)

    @staticmethod
    async def create_role_from_template(
        db: AsyncSession,
        template_id: str,
        role_name: str,
        role_description: Optional[str],
        current_user_id: str,
        current_user_org_id: str
    ) -> Role:
        """Create a role from a permission template"""
        # Get available templates
        templates = await PermissionService.get_permission_templates(
            db=db,
            organization_id=current_user_org_id
        )
        
        # Find the requested template
        template = None
        for t in templates:
            if t.id == template_id:
                template = t
                break
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template '{template_id}' not found"
            )
        
        # Check if role name already exists
        existing_role = await role_crud.get_by_name_and_organization(
            db=db,
            name=role_name,
            organization_id=current_user_org_id
        )
        
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role name already exists in organization"
            )
        
        # Create role from template
        role_create = RoleCreate(
            name=role_name,
            description=role_description or template.description,
            level=template.level,
            color=template.color,
            permissions=template.permissions.copy(),
            is_system=False,
            organization_id=current_user_org_id
        )
        
        return await role_crud.create(db, obj_in=role_create)

    @staticmethod
    async def get_role_users(
        db: AsyncSession,
        role_id: str,
        organization_id: str
    ) -> List[Dict[str, Any]]:
        """Get users assigned to a specific role"""
        role = await RoleService.get_role_by_id(
            db=db,
            role_id=role_id,
            organization_id=organization_id
        )
        
        users = await user_crud.get_by_role(db, role_id=role_id)
        
        return [
            {
                "id": str(user.id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "is_active": user.is_active,
                "last_login": user.last_login,
                "created_at": user.created_at
            }
            for user in users
        ]

    @staticmethod
    async def get_role_statistics(
        db: AsyncSession,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get role usage statistics"""
        # Get all roles in organization
        roles = await role_crud.get_by_organization(db, organization_id)
        
        # Get user counts per role
        role_stats = []
        total_users = 0
        
        for role in roles:
            users = await user_crud.get_by_role(db, role_id=str(role.id))
            user_count = len(users)
            total_users += user_count
            
            role_stats.append({
                "role_id": str(role.id),
                "role_name": role.name,
                "user_count": user_count,
                "permission_count": len(role.permissions),
                "is_system": role.is_system,
                "level": role.level
            })
        
        # Sort by user count
        role_stats.sort(key=lambda x: x["user_count"], reverse=True)
        
        return {
            "total_roles": len(roles),
            "system_roles": len([r for r in roles if r.is_system]),
            "custom_roles": len([r for r in roles if not r.is_system]),
            "total_users": total_users,
            "roles_with_users": len([r for r in role_stats if r["user_count"] > 0]),
            "unused_roles": len([r for r in role_stats if r["user_count"] == 0]),
            "role_distribution": role_stats
        }
