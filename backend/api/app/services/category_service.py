"""
Category service with business logic.
"""

from typing import List
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.category import category_crud
from app.models.category import Category
from app.schemas.category import CategoryCreate, CategoryUpdate


class CategoryService:
    """Category service for business logic"""
    
    @staticmethod
    async def create_category(
        db: AsyncSession,
        category_create: CategoryCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Category:
        """Create a new category with business logic validation"""
        
        # Ensure category belongs to current user's organization
        if category_create.organization_id != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create category for another organization"
            )
        
        # Check if category name already exists in organization
        existing_category = await category_crud.get_by_name_and_organization(
            db=db,
            name=category_create.name,
            organization_id=current_user_org_id
        )
        
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category with this name already exists in your organization"
            )
        
        # Create category
        return await category_crud.create(db, obj_in=category_create)
    
    @staticmethod
    async def get_categories_by_organization(
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Category]:
        """Get categories for an organization"""
        
        return await category_crud.get_by_organization(
            db=db,
            organization_id=organization_id,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    async def get_category_by_id(
        db: AsyncSession,
        category_id: str,
        current_user_org_id: str
    ) -> Category:
        """Get category by ID with organization access control"""
        
        category = await category_crud.get(db, id=category_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        
        # Ensure category belongs to current user's organization
        if str(category.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access category from another organization"
            )
        
        return category
    
    @staticmethod
    async def update_category(
        db: AsyncSession,
        category_id: str,
        category_update: CategoryUpdate,
        current_user_org_id: str
    ) -> Category:
        """Update category with business logic validation"""
        
        # Get existing category
        category = await CategoryService.get_category_by_id(
            db, category_id, current_user_org_id
        )
        
        # Check if new name conflicts with existing category
        if category_update.name and category_update.name != category.name:
            existing_category = await category_crud.get_by_name_and_organization(
                db=db,
                name=category_update.name,
                organization_id=current_user_org_id
            )
            
            if existing_category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Category with this name already exists in your organization"
                )
        
        # Update category
        return await category_crud.update(db, db_obj=category, obj_in=category_update)
    
    @staticmethod
    async def delete_category(
        db: AsyncSession,
        category_id: str,
        current_user_org_id: str
    ) -> Category:
        """Delete category with business logic validation"""
        
        # Get existing category
        category = await CategoryService.get_category_by_id(
            db, category_id, current_user_org_id
        )
        
        # TODO: Add business rule to check if category is in use by quests/badges/rewards
        # For now, we'll allow deletion
        
        # Delete category
        return await category_crud.remove(db, id=category_id)
