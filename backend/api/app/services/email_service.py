"""
Email service for sending invitations and notifications.
"""

import logging
from typing import Optional

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Email service for sending emails"""

    @staticmethod
    def send_email(
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        to_name: Optional[str] = None
    ) -> bool:
        """Send an email - Mock implementation for development"""
        try:
            # For development, we'll log the email instead of actually sending it
            logger.info(f"📧 EMAIL WOULD BE SENT:")
            logger.info(f"To: {to_name} <{to_email}>" if to_name else f"To: {to_email}")
            logger.info(f"Subject: {subject}")
            logger.info(f"Content: {text_content[:100] if text_content else html_content[:100]}...")

            # In production, you would implement actual SMTP sending here
            # For now, we'll return True to simulate successful sending
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    @staticmethod
    def send_invitation_email(
        to_email: str,
        to_name: str,
        inviter_name: str,
        organization_name: str,
        invitation_token: str,
        role_name: Optional[str] = None
    ) -> bool:
        """Send user invitation email"""

        # Create invitation URL
        invitation_url = f"{settings.FRONTEND_URL}/accept-invitation?token={invitation_token}"

        # Email subject
        subject = f"You're invited to join {organization_name}"

        # Simple HTML email content
        role_section = f"<p><strong>Your assigned role:</strong> {role_name}</p>" if role_name else ""

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invitation to {organization_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #f0f0f0; margin-bottom: 30px; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #2563eb; }}
        .invitation-box {{ background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 24px; margin: 20px 0; text-align: center; }}
        .btn {{ display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 16px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #e5e7eb; margin-top: 30px; color: #6b7280; font-size: 14px; }}
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">{organization_name}</div>
        <p>Admin Console Invitation</p>
    </div>

    <div class="content">
        <h2>Hello {to_name}!</h2>

        <p>{inviter_name} has invited you to join <strong>{organization_name}</strong> on our Admin Console platform.</p>

        {role_section}

        <div class="invitation-box">
            <h3>You're invited to join our team!</h3>
            <p>Click the button below to accept your invitation and set up your account.</p>
            <a href="{invitation_url}" class="btn">Accept Invitation</a>
        </div>

        <p><strong>Important:</strong> This invitation will expire in 72 hours.</p>

        <p>If you're unable to click the button above, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background: #f3f4f6; padding: 8px; border-radius: 4px; font-family: monospace;">{invitation_url}</p>

        <p>If you have any questions, please contact {inviter_name} or your system administrator.</p>
    </div>

    <div class="footer">
        <p>This invitation was sent by {inviter_name} from {organization_name}.</p>
        <p>If you weren't expecting this invitation, you can safely ignore this email.</p>
    </div>
</body>
</html>
        """

        # Simple text email content (fallback)
        role_text = f"Your assigned role: {role_name}\n\n" if role_name else ""

        text_content = f"""Hello {to_name}!

{inviter_name} has invited you to join {organization_name} on our Admin Console platform.

{role_text}To accept your invitation and set up your account, please visit:
{invitation_url}

Important: This invitation will expire in 72 hours.

If you have any questions, please contact {inviter_name} or your system administrator.

---
This invitation was sent by {inviter_name} from {organization_name}.
If you weren't expecting this invitation, you can safely ignore this email.
        """
        
        # Send email
        return EmailService.send_email(
            to_email=to_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
            to_name=to_name
        )
    
    @staticmethod
    def send_welcome_email(
        to_email: str,
        to_name: str,
        organization_name: str
    ) -> bool:
        """Send welcome email to new user"""
        
        subject = f"Welcome to {organization_name}!"
        
        html_content = f"""
        <h2>Welcome {to_name}!</h2>
        <p>Your account has been successfully created for {organization_name}.</p>
        <p>You can now log in to the Admin Console at: <a href="{settings.FRONTEND_URL}">{settings.FRONTEND_URL}</a></p>
        <p>If you have any questions, please contact your system administrator.</p>
        """
        
        text_content = f"""
        Welcome {to_name}!
        
        Your account has been successfully created for {organization_name}.
        You can now log in to the Admin Console at: {settings.FRONTEND_URL}
        
        If you have any questions, please contact your system administrator.
        """
        
        return EmailService.send_email(
            to_email=to_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
            to_name=to_name
        )
