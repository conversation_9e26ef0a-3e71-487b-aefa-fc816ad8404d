"""
Organization service with business logic.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.organization import organization_crud
from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate, OrganizationUpdate


class OrganizationService:
    """Organization service for business logic"""
    
    @staticmethod
    async def create_organization(
        db: AsyncSession,
        org_create: OrganizationCreate
    ) -> Organization:
        """Create a new organization with business logic validation"""
        
        # Check if slug already exists
        existing_org = await organization_crud.get_by_slug(db, slug=org_create.slug)
        if existing_org:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization slug already exists"
            )
        
        # Check if name already exists
        existing_org = await organization_crud.get_by_name(db, name=org_create.name)
        if existing_org:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization name already exists"
            )
        
        # Create organization
        return await organization_crud.create(db, obj_in=org_create)
    
    @staticmethod
    async def get_organization_by_id(
        db: AsyncSession,
        org_id: str,
        current_user_org_id: str,
        is_admin: bool = False
    ) -> Organization:
        """Get organization by ID with access control"""
        
        organization = await organization_crud.get(db, id=org_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Regular users can only view their own organization
        if not is_admin and str(organization.id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this organization"
            )
        
        return organization
    
    @staticmethod
    async def update_organization(
        db: AsyncSession,
        org_id: str,
        org_update: OrganizationUpdate,
        current_user_org_id: str,
        is_admin: bool = False
    ) -> Organization:
        """Update organization with business logic validation"""
        
        # Get existing organization
        organization = await organization_crud.get(db, id=org_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Regular users can only update their own organization
        if not is_admin and str(organization.id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this organization"
            )
        
        # Check name uniqueness if name is being updated
        if org_update.name and org_update.name != organization.name:
            existing_org = await organization_crud.get_by_name(db, name=org_update.name)
            if existing_org and str(existing_org.id) != org_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Organization name already exists"
                )
        
        # Update organization
        return await organization_crud.update(db, db_obj=organization, obj_in=org_update)
    
    @staticmethod
    async def list_organizations(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100
    ) -> List[Organization]:
        """List all organizations (admin only)"""
        return await organization_crud.get_multi(db, skip=skip, limit=limit)
    
    @staticmethod
    async def delete_organization(
        db: AsyncSession,
        org_id: str
    ) -> Organization:
        """Delete organization (admin only)"""
        
        organization = await organization_crud.get(db, id=org_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Additional business logic could be added here
        # e.g., check if organization has active campaigns, users, etc.
        
        return await organization_crud.remove(db, id=org_id)
