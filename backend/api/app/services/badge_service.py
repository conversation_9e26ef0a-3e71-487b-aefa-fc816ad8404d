"""
Badge service with business logic.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.badge import badge_crud
from app.models.badge import Badge, BadgeTier, BadgeStatus
from app.schemas.badge import BadgeC<PERSON>, BadgeUpdate


class BadgeService:
    """Badge service for business logic"""
    
    @staticmethod
    async def create_badge(
        db: AsyncSession,
        badge_create: BadgeCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Badge:
        """Create a new badge with business logic validation"""
        
        # Validate points reward
        if badge_create.points_reward < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points reward cannot be negative"
            )
        
        # Validate criteria is not empty
        if not badge_create.criteria or not badge_create.criteria.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Badge must have valid criteria"
            )
        
        # Add organization and creator information
        badge_data = badge_create.dict()
        badge_data["organization_id"] = current_user_org_id
        badge_data["created_by"] = current_user_id
        
        # Create badge
        return await badge_crud.create(db, obj_in=badge_data)
    
    @staticmethod
    async def get_badges_by_organization(
        db: AsyncSession,
        organization_id: str,
        tier_filter: Optional[BadgeTier] = None,
        status_filter: Optional[BadgeStatus] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Badge]:
        """Get badges for an organization with optional filtering"""

        return await badge_crud.get_by_organization_with_filters(
            db=db,
            organization_id=organization_id,
            tier_filter=tier_filter,
            status_filter=status_filter,
            search=search,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    async def get_badge_by_id(
        db: AsyncSession,
        badge_id: str,
        current_user_org_id: str
    ) -> Badge:
        """Get badge by ID with organization access control"""
        
        badge = await badge_crud.get(db, id=badge_id)
        if not badge:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Badge not found"
            )
        
        # Ensure badge belongs to current user's organization
        if str(badge.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access badge from another organization"
            )
        
        return badge
    
    @staticmethod
    async def update_badge(
        db: AsyncSession,
        badge_id: str,
        badge_update: BadgeUpdate,
        current_user_org_id: str
    ) -> Badge:
        """Update badge with business logic validation"""
        
        # Get existing badge with access control
        badge = await BadgeService.get_badge_by_id(
            db, badge_id, current_user_org_id
        )
        
        # Validate points reward if being updated
        if badge_update.points_reward is not None and badge_update.points_reward < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points reward cannot be negative"
            )
        
        # Validate criteria if being updated
        if badge_update.criteria is not None:
            if not badge_update.criteria or not badge_update.criteria.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Badge must have valid criteria"
                )
        
        # Update badge
        return await badge_crud.update(db, db_obj=badge, obj_in=badge_update)
    
    @staticmethod
    async def delete_badge(
        db: AsyncSession,
        badge_id: str,
        current_user_org_id: str
    ) -> Badge:
        """Delete badge with business logic validation"""
        
        # Get existing badge with access control
        badge = await BadgeService.get_badge_by_id(
            db, badge_id, current_user_org_id
        )
        
        # Additional business logic could be added here
        # e.g., check if badge is currently awarded to users
        
        # Delete badge
        return await badge_crud.remove(db, id=badge_id)
    

    
    @staticmethod
    async def get_badges_by_tier(
        db: AsyncSession,
        organization_id: str,
        tier: BadgeTier,
        skip: int = 0,
        limit: int = 100
    ) -> List[Badge]:
        """Get badges by tier within organization"""
        
        return await badge_crud.get_by_tier(
            db=db,
            organization_id=organization_id,
            tier=tier,
            skip=skip,
            limit=limit
        )
