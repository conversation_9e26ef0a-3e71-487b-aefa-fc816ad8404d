"""
Analytics service with business logic.
"""

from typing import Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.crud.user import user_crud
from app.crud.campaign import campaign_crud
from app.crud.quest import quest_crud
from app.crud.badge import badge_crud
from app.crud.reward import reward_crud
from app.models.campaign import Campaign, CampaignStatus
from app.models.quest import Quest, QuestStatus
from app.models.badge import Badge
from app.models.reward import Reward, RewardStatus
from app.models.user import User


class AnalyticsService:
    """Analytics service for business intelligence"""
    
    @staticmethod
    async def get_organization_overview(
        db: AsyncSession,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get high-level analytics overview for organization"""
        
        # Count total users
        total_users = len(await user_crud.get_by_organization(
            db, organization_id=organization_id, skip=0, limit=10000
        ))
        
        # Count active campaigns
        active_campaigns_result = await db.execute(
            select(func.count(Campaign.id))
            .where(and_(
                Campaign.organization_id == organization_id,
                Campaign.status == CampaignStatus.ACTIVE
            ))
        )
        active_campaigns = active_campaigns_result.scalar() or 0
        
        # Count total campaigns
        total_campaigns_result = await db.execute(
            select(func.count(Campaign.id))
            .where(Campaign.organization_id == organization_id)
        )
        total_campaigns = total_campaigns_result.scalar() or 0
        
        # Count active quests
        active_quests_result = await db.execute(
            select(func.count(Quest.id))
            .join(Campaign)
            .where(and_(
                Campaign.organization_id == organization_id,
                Quest.status == QuestStatus.ACTIVE
            ))
        )
        active_quests = active_quests_result.scalar() or 0
        
        # Count total badges
        total_badges_result = await db.execute(
            select(func.count(Badge.id))
            .where(Badge.organization_id == organization_id)
        )
        total_badges = total_badges_result.scalar() or 0
        
        # Count active rewards
        active_rewards_result = await db.execute(
            select(func.count(Reward.id))
            .where(and_(
                Reward.organization_id == organization_id,
                Reward.status == RewardStatus.ACTIVE
            ))
        )
        active_rewards = active_rewards_result.scalar() or 0
        
        return {
            "total_users": total_users,
            "total_campaigns": total_campaigns,
            "active_campaigns": active_campaigns,
            "active_quests": active_quests,
            "total_badges": total_badges,
            "active_rewards": active_rewards,
            "campaign_completion_rate": (active_campaigns / total_campaigns * 100) if total_campaigns > 0 else 0
        }
    
    @staticmethod
    async def get_campaign_analytics(
        db: AsyncSession,
        campaign_id: str,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get detailed analytics for a specific campaign"""
        
        # Get campaign details
        campaign = await campaign_crud.get(db, id=campaign_id)
        if not campaign or str(campaign.organization_id) != organization_id:
            return {}
        
        # Count quests in campaign
        total_quests_result = await db.execute(
            select(func.count(Quest.id))
            .where(Quest.campaign_id == campaign_id)
        )
        total_quests = total_quests_result.scalar() or 0
        
        # Count active quests
        active_quests_result = await db.execute(
            select(func.count(Quest.id))
            .where(and_(
                Quest.campaign_id == campaign_id,
                Quest.status == QuestStatus.ACTIVE
            ))
        )
        active_quests = active_quests_result.scalar() or 0
        
        # Calculate total points available
        total_points_result = await db.execute(
            select(func.sum(Quest.points_reward))
            .where(Quest.campaign_id == campaign_id)
        )
        total_points = total_points_result.scalar() or 0
        
        return {
            "campaign_id": campaign_id,
            "campaign_name": campaign.name,
            "campaign_status": campaign.status,
            "total_quests": total_quests,
            "active_quests": active_quests,
            "total_points_available": total_points,
            "start_date": campaign.start_date,
            "end_date": campaign.end_date,
            "quest_completion_rate": (active_quests / total_quests * 100) if total_quests > 0 else 0
        }
    
    @staticmethod
    async def get_quest_analytics(
        db: AsyncSession,
        organization_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get analytics for top quests by points"""
        
        # Get top quests by points reward
        result = await db.execute(
            select(Quest.id, Quest.title, Quest.points_reward, Quest.status, Campaign.name.label('campaign_name'))
            .join(Campaign)
            .where(Campaign.organization_id == organization_id)
            .order_by(Quest.points_reward.desc())
            .limit(limit)
        )
        
        quests = []
        for row in result:
            quests.append({
                "quest_id": str(row.id),
                "quest_title": row.title,
                "points_reward": row.points_reward,
                "status": row.status,
                "campaign_name": row.campaign_name,
                # Additional metrics would come from user activity tracking
                "completions": 0,  # Placeholder - would need user_quest_completions table
                "success_rate": 0.0  # Placeholder
            })
        
        return quests
    
    @staticmethod
    async def get_badge_analytics(
        db: AsyncSession,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get badge distribution analytics"""
        
        # Count badges by tier
        badge_tiers_result = await db.execute(
            select(Badge.tier, func.count(Badge.id))
            .where(Badge.organization_id == organization_id)
            .group_by(Badge.tier)
        )
        
        badge_distribution = {}
        total_badges = 0
        
        for tier, count in badge_tiers_result:
            badge_distribution[tier] = count
            total_badges += count
        
        # Get highest point badges
        top_badges_result = await db.execute(
            select(Badge.id, Badge.name, Badge.points_reward, Badge.tier)
            .where(Badge.organization_id == organization_id)
            .order_by(Badge.points_reward.desc())
            .limit(5)
        )
        
        top_badges = []
        for row in top_badges_result:
            top_badges.append({
                "badge_id": str(row.id),
                "name": row.name,
                "points_reward": row.points_reward,
                "tier": row.tier
            })
        
        return {
            "total_badges": total_badges,
            "badge_distribution": badge_distribution,
            "top_badges": top_badges
        }
    
    @staticmethod
    async def get_reward_analytics(
        db: AsyncSession,
        organization_id: str
    ) -> Dict[str, Any]:
        """Get reward analytics"""
        
        # Count rewards by status
        reward_status_result = await db.execute(
            select(Reward.status, func.count(Reward.id))
            .where(Reward.organization_id == organization_id)
            .group_by(Reward.status)
        )
        
        status_distribution = {}
        total_rewards = 0
        
        for status, count in reward_status_result:
            status_distribution[status] = count
            total_rewards += count
        
        # Get average points cost
        avg_cost_result = await db.execute(
            select(func.avg(Reward.points_cost))
            .where(and_(
                Reward.organization_id == organization_id,
                Reward.status == RewardStatus.ACTIVE
            ))
        )
        avg_cost = avg_cost_result.scalar() or 0
        
        # Get most expensive rewards
        expensive_rewards_result = await db.execute(
            select(Reward.id, Reward.name, Reward.points_cost, Reward.stock_quantity)
            .where(Reward.organization_id == organization_id)
            .order_by(Reward.points_cost.desc())
            .limit(5)
        )
        
        expensive_rewards = []
        for row in expensive_rewards_result:
            expensive_rewards.append({
                "reward_id": str(row.id),
                "name": row.name,
                "points_cost": row.points_cost,
                "stock_quantity": row.stock_quantity
            })
        
        return {
            "total_rewards": total_rewards,
            "status_distribution": status_distribution,
            "average_points_cost": round(avg_cost, 2),
            "most_expensive_rewards": expensive_rewards
        }
