"""
Reward service with business logic.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.reward import reward_crud
from app.models.reward import Reward, RewardStatus
from app.schemas.reward import RewardCreate, RewardUpdate


class RewardService:
    """Reward service for business logic"""
    
    @staticmethod
    async def create_reward(
        db: AsyncSession,
        reward_create: RewardCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Reward:
        """Create a new reward with business logic validation"""
        
        # Validate points cost
        if reward_create.points_cost < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points cost cannot be negative"
            )
        
        # Validate stock quantity if provided
        if reward_create.stock_quantity is not None and reward_create.stock_quantity < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Stock quantity cannot be negative"
            )
        
        # Add organization and creator information
        reward_data = reward_create.dict()
        reward_data["organization_id"] = current_user_org_id
        reward_data["created_by"] = current_user_id
        
        # Create reward
        return await reward_crud.create(db, obj_in=reward_data)
    
    @staticmethod
    async def get_rewards_by_organization(
        db: AsyncSession,
        organization_id: str,
        status_filter: Optional[RewardStatus] = None,
        min_points: Optional[int] = None,
        max_points: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reward]:
        """Get rewards for an organization with optional filtering"""
        
        # If points range is specified, use specialized query
        if min_points is not None and max_points is not None:
            return await reward_crud.get_by_points_range(
                db=db,
                organization_id=organization_id,
                min_points=min_points,
                max_points=max_points,
                skip=skip,
                limit=limit
            )
        
        # If only active rewards requested
        if status_filter == RewardStatus.ACTIVE:
            return await reward_crud.get_active_rewards(
                db=db,
                organization_id=organization_id,
                skip=skip,
                limit=limit
            )
        
        # Get all rewards with optional status filter
        filters = {}
        if status_filter:
            filters["status"] = status_filter
        
        return await reward_crud.get_multi(
            db=db,
            skip=skip,
            limit=limit,
            filters={**filters, "organization_id": organization_id}
        )
    
    @staticmethod
    async def get_reward_by_id(
        db: AsyncSession,
        reward_id: str,
        current_user_org_id: str
    ) -> Reward:
        """Get reward by ID with organization access control"""
        
        reward = await reward_crud.get(db, id=reward_id)
        if not reward:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reward not found"
            )
        
        # Ensure reward belongs to current user's organization
        if str(reward.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access reward from another organization"
            )
        
        return reward
    
    @staticmethod
    async def update_reward(
        db: AsyncSession,
        reward_id: str,
        reward_update: RewardUpdate,
        current_user_org_id: str
    ) -> Reward:
        """Update reward with business logic validation"""
        
        # Get existing reward with access control
        reward = await RewardService.get_reward_by_id(
            db, reward_id, current_user_org_id
        )
        
        # Validate points cost if being updated
        if reward_update.points_cost is not None and reward_update.points_cost < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points cost cannot be negative"
            )
        
        # Validate stock quantity if being updated
        if reward_update.stock_quantity is not None and reward_update.stock_quantity < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Stock quantity cannot be negative"
            )
        
        # Business rule: If stock quantity is 0, automatically set status to OUT_OF_STOCK
        if reward_update.stock_quantity == 0:
            reward_update.status = RewardStatus.OUT_OF_STOCK
        elif (reward_update.stock_quantity and reward_update.stock_quantity > 0 and 
              reward.status == RewardStatus.OUT_OF_STOCK):
            # If adding stock to out-of-stock item, reactivate it
            reward_update.status = RewardStatus.ACTIVE
        
        # Update reward
        return await reward_crud.update(db, db_obj=reward, obj_in=reward_update)
    
    @staticmethod
    async def delete_reward(
        db: AsyncSession,
        reward_id: str,
        current_user_org_id: str
    ) -> Reward:
        """Delete reward with business logic validation"""
        
        # Get existing reward with access control
        reward = await RewardService.get_reward_by_id(
            db, reward_id, current_user_org_id
        )
        
        # Additional business logic could be added here
        # e.g., check if reward has pending redemptions
        
        # Delete reward
        return await reward_crud.remove(db, id=reward_id)
    
    @staticmethod
    async def deactivate_reward(
        db: AsyncSession,
        reward_id: str,
        current_user_org_id: str
    ) -> Reward:
        """Deactivate a reward"""
        
        reward = await RewardService.get_reward_by_id(
            db, reward_id, current_user_org_id
        )
        
        # Update status to inactive
        reward_update = RewardUpdate(status=RewardStatus.INACTIVE)
        return await reward_crud.update(db, db_obj=reward, obj_in=reward_update)
    
    @staticmethod
    async def activate_reward(
        db: AsyncSession,
        reward_id: str,
        current_user_org_id: str
    ) -> Reward:
        """Activate a reward with validation"""
        
        reward = await RewardService.get_reward_by_id(
            db, reward_id, current_user_org_id
        )
        
        # Validate reward can be activated
        if reward.points_cost <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reward must have positive points cost to be activated"
            )
        
        # Check stock if applicable
        if reward.stock_quantity is not None and reward.stock_quantity <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot activate reward with no stock"
            )
        
        # Update status to active
        reward_update = RewardUpdate(status=RewardStatus.ACTIVE)
        return await reward_crud.update(db, db_obj=reward, obj_in=reward_update)
    
    @staticmethod
    async def get_available_rewards(
        db: AsyncSession,
        organization_id: str,
        user_points: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reward]:
        """Get rewards that user can afford with their points"""
        
        return await reward_crud.get_by_points_range(
            db=db,
            organization_id=organization_id,
            min_points=0,
            max_points=user_points,
            skip=skip,
            limit=limit
        )
