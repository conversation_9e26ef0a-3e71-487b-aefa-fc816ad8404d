"""
Authentication service with business logic.
"""

from datetime import timed<PERSON><PERSON>
from typing import Op<PERSON>
from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import create_access_token
from app.crud.user import user_crud
from app.crud.organization import organization_crud
from app.models.user import User
from app.schemas.auth import Token, UserRegister
from app.schemas.user import UserCreate
from typing import Dict, Any
from app.schemas.organization import OrganizationCreate


class AuthService:
    """Authentication service"""
    
    @staticmethod
    async def authenticate_user(
        db: AsyncSession, 
        email: str, 
        password: str
    ) -> Optional[User]:
        """Authenticate user with email and password"""
        user = await user_crud.authenticate(db, email=email, password=password)
        if not user:
            return None
        if not await user_crud.is_active(user):
            return None
        return user
    
    @staticmethod
    async def create_access_token_for_user(user: User) -> Token:
        """Create access token for authenticated user"""
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        token_data = {
            "sub": str(user.id),
            "organization_id": str(user.organization_id),
            "email": user.email,
            "is_superuser": user.is_superuser
        }
        
        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert to seconds
        )
    
    @staticmethod
    async def login(db: AsyncSession, email: str, password: str) -> Token:
        """Login user and return access token"""
        user = await AuthService.authenticate_user(db, email, password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return await AuthService.create_access_token_for_user(user)

    @staticmethod
    async def register(db: AsyncSession, user_data: UserRegister) -> Dict[str, Any]:
        """Register a new user and create organization if needed"""
        # Check if email already exists
        existing_user = await user_crud.get_by_email(db, email=user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create organization if provided
        organization_id = None
        if user_data.organization_name:
            # Generate a slug from the organization name
            import re
            slug = re.sub(r'[^a-zA-Z0-9\-]', '-', user_data.organization_name.lower())
            slug = re.sub(r'-+', '-', slug).strip('-')

            # Ensure slug is unique by checking if it exists
            existing_org = await organization_crud.get_by_slug(db, slug=slug)
            if existing_org:
                # Append a number to make it unique
                counter = 1
                while existing_org:
                    new_slug = f"{slug}-{counter}"
                    existing_org = await organization_crud.get_by_slug(db, slug=new_slug)
                    counter += 1
                slug = new_slug

            org_create = OrganizationCreate(
                name=user_data.organization_name,
                slug=slug,
                description=f"Organization for {user_data.organization_name}",
                industry=user_data.industry,
                company_size=user_data.company_size
            )
            organization = await organization_crud.create(db, obj_in=org_create)
            organization_id = str(organization.id)

        # Create user
        user_create = UserCreate(
            email=user_data.email,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            organization_id=organization_id,
            is_active=True
        )

        user = await user_crud.create(db, obj_in=user_create)

        # Convert UUID fields to strings for proper serialization
        user_dict = {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "organization_id": str(user.organization_id) if user.organization_id else None,
            "role_id": str(user.role_id) if user.role_id else None,
            "is_superuser": user.is_superuser,
            "last_login": user.last_login,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
        }

        return user_dict
