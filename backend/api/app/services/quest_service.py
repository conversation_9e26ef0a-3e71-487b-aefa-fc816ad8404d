"""
Quest service with business logic.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.quest import quest_crud
from app.crud.campaign import campaign_crud
from app.models.quest import Quest, QuestStatus
from app.schemas.quest import Quest<PERSON><PERSON>, QuestUpdate


class QuestService:
    """Quest service for business logic"""
    
    @staticmethod
    async def create_quest(
        db: AsyncSession,
        quest_create: QuestCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Quest:
        """Create a new quest with business logic validation"""
        
        # Verify campaign exists and belongs to user's organization
        campaign = await campaign_crud.get(db, id=quest_create.campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        if str(campaign.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create quest for campaign in another organization"
            )
        
        # Validate points reward
        if quest_create.points_reward < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points reward cannot be negative"
            )
        
        # Add creator information
        quest_data = quest_create.dict()
        quest_data["created_by"] = current_user_id
        
        # Create quest
        return await quest_crud.create(db, obj_in=quest_data)
    
    @staticmethod
    async def get_quests_by_campaign(
        db: AsyncSession,
        campaign_id: str,
        current_user_org_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Quest]:
        """Get quests for a campaign with organization access control"""
        
        # Verify campaign belongs to user's organization
        campaign = await campaign_crud.get(db, id=campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        if str(campaign.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access quests from another organization"
            )
        
        return await quest_crud.get_by_campaign(
            db=db,
            campaign_id=campaign_id,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    async def get_quest_by_id(
        db: AsyncSession,
        quest_id: str,
        current_user_org_id: str
    ) -> Quest:
        """Get quest by ID with organization access control"""
        
        quest = await quest_crud.get(db, id=quest_id)
        if not quest:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quest not found"
            )
        
        # Verify quest belongs to user's organization (through campaign)
        campaign = await campaign_crud.get(db, id=quest.campaign_id)
        if not campaign or str(campaign.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access quest from another organization"
            )
        
        return quest
    
    @staticmethod
    async def update_quest(
        db: AsyncSession,
        quest_id: str,
        quest_update: QuestUpdate,
        current_user_org_id: str
    ) -> Quest:
        """Update quest with business logic validation"""
        
        # Get existing quest with access control
        quest = await QuestService.get_quest_by_id(
            db, quest_id, current_user_org_id
        )
        
        # Validate points reward if being updated
        if quest_update.points_reward is not None and quest_update.points_reward < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Points reward cannot be negative"
            )
        
        # Business rule: Cannot change status to ACTIVE if validation criteria is missing
        if (quest_update.status == QuestStatus.ACTIVE and 
            not (quest_update.validation_criteria or quest.validation_criteria)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot activate quest without validation criteria"
            )
        
        # Update quest
        return await quest_crud.update(db, db_obj=quest, obj_in=quest_update)
    
    @staticmethod
    async def delete_quest(
        db: AsyncSession,
        quest_id: str,
        current_user_org_id: str
    ) -> Quest:
        """Delete quest with business logic validation"""
        
        # Get existing quest with access control
        quest = await QuestService.get_quest_by_id(
            db, quest_id, current_user_org_id
        )
        
        # Business rule: Cannot delete active quests
        if quest.status == QuestStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete active quest. Please pause or archive it first."
            )
        
        # Delete quest
        return await quest_crud.remove(db, id=quest_id)
    
    @staticmethod
    async def activate_quest(
        db: AsyncSession,
        quest_id: str,
        current_user_org_id: str
    ) -> Quest:
        """Activate a quest with business logic validation"""
        
        quest = await QuestService.get_quest_by_id(
            db, quest_id, current_user_org_id
        )
        
        # Validate quest can be activated
        if not quest.validation_criteria:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Quest must have validation criteria to be activated"
            )
        
        if quest.points_reward <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Quest must have positive points reward to be activated"
            )
        
        # Update status
        quest_update = QuestUpdate(status=QuestStatus.ACTIVE)
        return await quest_crud.update(db, db_obj=quest, obj_in=quest_update)
    
    @staticmethod
    async def get_quests_by_organization(
        db: AsyncSession,
        organization_id: str,
        status_filter: Optional[QuestStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Quest]:
        """Get all quests for an organization with optional filtering"""
        
        filters = {}
        if status_filter:
            filters["status"] = status_filter
        
        return await quest_crud.get_by_organization(
            db=db,
            organization_id=organization_id,
            skip=skip,
            limit=limit
        )
