"""
Campaign service with business logic.
"""

from typing import List, Optional
from datetime import datetime
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.campaign import campaign_crud
from app.models.campaign import Campaign, CampaignStatus
from app.schemas.campaign import Campaign<PERSON><PERSON>, CampaignUpdate


class CampaignService:
    """Campaign service for business logic"""
    
    @staticmethod
    async def create_campaign(
        db: AsyncSession,
        campaign_create: CampaignCreate,
        current_user_id: str,
        current_user_org_id: str
    ) -> Campaign:
        """Create a new campaign with business logic validation"""
        
        # Ensure campaign belongs to current user's organization
        if campaign_create.organization_id != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create campaign for another organization"
            )
        
        # Validate date logic
        if (campaign_create.start_date and campaign_create.end_date and 
            campaign_create.start_date >= campaign_create.end_date):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End date must be after start date"
            )
        
        # Add creator information to the campaign data
        # Manually extract fields to preserve datetime objects
        print(f"DEBUG: start_date type: {type(campaign_create.start_date)}, value: {campaign_create.start_date}")
        print(f"DEBUG: end_date type: {type(campaign_create.end_date)}, value: {campaign_create.end_date}")

        campaign_data = {
            "name": campaign_create.name,
            "description": campaign_create.description,
            "banner_url": campaign_create.banner_url,
            "start_date": campaign_create.start_date,  # This should be a datetime object
            "end_date": campaign_create.end_date,      # This should be a datetime object
            "target_audience": campaign_create.target_audience,
            "organization_id": campaign_create.organization_id,
            "created_by": current_user_id
        }

        # Create campaign
        return await campaign_crud.create(db, obj_in=campaign_data)
    
    @staticmethod
    async def get_campaigns_by_organization(
        db: AsyncSession,
        organization_id: str,
        status_filter: Optional[CampaignStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Campaign]:
        """Get campaigns for an organization with optional filtering"""
        
        filters = {"organization_id": organization_id}
        if status_filter:
            filters["status"] = status_filter
        
        return await campaign_crud.get_multi(
            db, 
            skip=skip, 
            limit=limit,
            filters=filters
        )
    
    @staticmethod
    async def get_campaign_by_id(
        db: AsyncSession,
        campaign_id: str,
        current_user_org_id: str
    ) -> Campaign:
        """Get campaign by ID with organization access control"""
        
        campaign = await campaign_crud.get(db, id=campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Ensure campaign belongs to current user's organization
        if str(campaign.organization_id) != current_user_org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access campaign from another organization"
            )
        
        return campaign
    
    @staticmethod
    async def update_campaign(
        db: AsyncSession,
        campaign_id: str,
        campaign_update: CampaignUpdate,
        current_user_org_id: str
    ) -> Campaign:
        """Update campaign with business logic validation"""
        
        # Get existing campaign
        campaign = await CampaignService.get_campaign_by_id(
            db, campaign_id, current_user_org_id
        )
        
        # Validate date logic if dates are being updated
        start_date = campaign_update.start_date or campaign.start_date
        end_date = campaign_update.end_date or campaign.end_date
        
        if start_date and end_date and start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End date must be after start date"
            )
        
        # Business rule: Cannot change status to ACTIVE if no start_date
        if (campaign_update.status == CampaignStatus.ACTIVE and 
            not start_date):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot activate campaign without start date"
            )
        
        # Update campaign
        return await campaign_crud.update(db, db_obj=campaign, obj_in=campaign_update)
    
    @staticmethod
    async def delete_campaign(
        db: AsyncSession,
        campaign_id: str,
        current_user_org_id: str
    ) -> Campaign:
        """Delete campaign with business logic validation"""
        
        # Get existing campaign
        campaign = await CampaignService.get_campaign_by_id(
            db, campaign_id, current_user_org_id
        )
        
        # Business rule: Cannot delete active campaigns
        if campaign.status == CampaignStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete active campaign. Please pause or end it first."
            )
        
        # Delete campaign
        return await campaign_crud.remove(db, id=campaign_id)
    
    @staticmethod
    async def activate_campaign(
        db: AsyncSession,
        campaign_id: str,
        current_user_org_id: str
    ) -> Campaign:
        """Activate a campaign with business logic validation"""
        
        campaign = await CampaignService.get_campaign_by_id(
            db, campaign_id, current_user_org_id
        )
        
        # Validate campaign can be activated
        if not campaign.start_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign must have a start date to be activated"
            )
        
        if campaign.end_date and campaign.end_date <= datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot activate campaign with past end date"
            )
        
        # Update status
        campaign_update = CampaignUpdate(status=CampaignStatus.ACTIVE)
        return await campaign_crud.update(db, db_obj=campaign, obj_in=campaign_update)
