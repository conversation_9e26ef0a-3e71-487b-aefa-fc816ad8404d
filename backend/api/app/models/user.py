"""
User SQLAlchemy model.
"""

from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class User(BaseModel):
    """User model"""
    __tablename__ = "users"
    
    # Basic info
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Status and permissions
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime(timezone=True))
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Role relationship
    role_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("roles.id"),
        nullable=True,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="users")
    role = relationship("Role", back_populates="users")
    created_campaigns = relationship("Campaign", back_populates="creator")
    created_quests = relationship("Quest", back_populates="creator")
    created_badges = relationship("Badge", back_populates="creator")
    created_rewards = relationship("Reward", back_populates="creator")
