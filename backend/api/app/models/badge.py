"""
Badge SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, ENUM as PostgreSQLEnum
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class BadgeTier(str, enum.Enum):
    """Badge tier enumeration"""
    BRONZE = "bronze"
    SILVER = "silver"
    GOLD = "gold"
    PLATINUM = "platinum"
    DIAMOND = "diamond"


class BadgeStatus(str, enum.Enum):
    """Badge status enumeration"""
    ACTIVE = "ACTIVE"
    DRAFT = "DRAFT"
    PAUSED = "PAUSED"
    ARCHIVED = "ARCHIVED"


class Badge(BaseModel):
    """Badge model"""
    __tablename__ = "badges"

    # Basic info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    icon = Column(String(100), default="Trophy")  # Icon name instead of URL

    # Badge properties
    tier = Column(
        SQLEnum(BadgeTier),
        default=BadgeTier.BRONZE,
        nullable=False,
        index=True
    )
    points_reward = Column(Integer, nullable=False, default=0)
    criteria = Column(Text, nullable=False)  # Store criteria as text for UI compatibility

    # Status
    status = Column(
        PostgreSQLEnum(BadgeStatus, name='badgestatus', create_type=False),
        default=BadgeStatus.DRAFT,
        nullable=False,
        index=True
    )

    # Tracking
    times_earned = Column(Integer, nullable=False, default=0, index=True)
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Category relationship (optional)
    category_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("categories.id"),
        nullable=True,
        index=True
    )
    
    # Creator relationship
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="badges")
    creator = relationship("User", back_populates="created_badges")
    category = relationship("Category", back_populates="badges")
