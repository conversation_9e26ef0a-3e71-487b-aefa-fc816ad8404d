"""
Campaign SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class CampaignStatus(str, enum.Enum):
    """Campaign status enumeration"""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"
    ARCHIVED = "archived"


class Campaign(BaseModel):
    """Campaign model"""
    __tablename__ = "campaigns"
    
    # Basic info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    banner_url = Column(String(500))
    
    # Dates
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    
    # Status
    status = Column(
        SQLEnum(CampaignStatus), 
        default=CampaignStatus.DRAFT, 
        nullable=False,
        index=True
    )
    
    # Targeting
    target_audience = Column(JSONB)  # Store audience criteria as JSON
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Creator relationship
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="campaigns")
    creator = relationship("User", back_populates="created_campaigns")
    quests = relationship("Quest", back_populates="campaign", cascade="all, delete-orphan")
