"""
User invitation model.
"""

import uuid
from datetime import datetime, timedelta
from sqlalchemy import Column, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database import Base
from app.core.config import settings


class Invitation(Base):
    """User invitation model"""
    __tablename__ = "invitations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, nullable=False, index=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    
    # Organization and role
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=True)
    
    # Invitation details
    token = Column(String, unique=True, nullable=False, index=True)
    invited_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Status and timing
    is_accepted = Column(Boolean, default=False, nullable=False)
    is_expired = Column(Boolean, default=False, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    accepted_at = Column(DateTime, nullable=True)
    
    # Metadata
    invitation_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    organization = relationship("Organization", back_populates="invitations")
    role = relationship("Role")
    invited_by = relationship("User", foreign_keys=[invited_by_id])
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.expires_at:
            self.expires_at = datetime.utcnow() + timedelta(hours=settings.INVITATION_TOKEN_EXPIRE_HOURS)
        if not self.token:
            self.token = self.generate_token()
    
    @staticmethod
    def generate_token() -> str:
        """Generate a unique invitation token"""
        import secrets
        return secrets.token_urlsafe(32)
    
    @property
    def is_valid(self) -> bool:
        """Check if invitation is still valid"""
        return not self.is_accepted and not self.is_expired and datetime.utcnow() < self.expires_at
    
    def expire(self):
        """Mark invitation as expired"""
        self.is_expired = True
        self.updated_at = datetime.utcnow()
    
    def accept(self):
        """Mark invitation as accepted"""
        self.is_accepted = True
        self.accepted_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
