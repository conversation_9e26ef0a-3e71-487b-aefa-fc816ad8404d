"""
Category SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Category(BaseModel):
    """Category model for organizing quests, badges, and rewards"""
    __tablename__ = "categories"
    
    # Basic info
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    color = Column(String(7), default="#6b7280")  # Hex color for UI
    icon = Column(String(50))  # Icon name or emoji
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="categories")
    quests = relationship("Quest", back_populates="category")
    badges = relationship("Badge", back_populates="category")
    rewards = relationship("Reward", back_populates="category")
