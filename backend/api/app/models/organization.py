"""
Organization SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Organization(BaseModel):
    """Organization model"""
    __tablename__ = "organizations"
    
    name = Column(String(255), nullable=False, index=True)
    slug = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    website = Column(String(255))
    industry = Column(String(100))  # Industry category
    company_size = Column(String(50))  # Company size category
    primary_color = Column(String(7), default="#3b82f6")  # Hex color
    subscription_plan = Column(String(50), default="free")
    
    # Relationships
    users = relationship("User", back_populates="organization")
    roles = relationship("Role", back_populates="organization")
    invitations = relationship("Invitation", back_populates="organization")
    categories = relationship("Category", back_populates="organization")
    campaigns = relationship("Campaign", back_populates="organization")
    badges = relationship("Badge", back_populates="organization")
    rewards = relationship("Reward", back_populates="organization")
