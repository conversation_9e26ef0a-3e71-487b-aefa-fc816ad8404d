"""
Role SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, Integer, <PERSON>olean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Role(BaseModel):
    """Role model for RBAC"""
    __tablename__ = "roles"
    
    # Basic info
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    level = Column(Integer, nullable=False, default=1)  # Higher level = more permissions
    color = Column(String(7), default="#6b7280")  # Hex color for UI
    
    # Permissions
    permissions = Column(JSONB, nullable=False, default=list)  # List of permission strings
    
    # System role flag
    is_system = Column(Boolean, default=False, nullable=False)  # Cannot be deleted if True
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="roles")
    users = relationship("User", back_populates="role")
