"""
Quest SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class QuestStatus(str, enum.Enum):
    """Quest status enumeration"""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"


class QuestValidationType(str, enum.Enum):
    """Quest validation type enumeration"""
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    CODE = "code"
    UPLOAD = "upload"


class QuestFrequency(str, enum.Enum):
    """Quest frequency enumeration"""
    ONE_TIME = "one_time"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    UNLIMITED = "unlimited"


class Quest(BaseModel):
    """Quest model"""
    __tablename__ = "quests"
    
    # Basic info
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    
    # Rewards and validation
    points_reward = Column(Integer, nullable=False, default=0)
    frequency = Column(
        SQLEnum(QuestFrequency), 
        default=QuestFrequency.ONE_TIME, 
        nullable=False
    )
    validation_type = Column(
        SQLEnum(QuestValidationType), 
        default=QuestValidationType.AUTOMATIC, 
        nullable=False
    )
    validation_criteria = Column(JSONB)  # Store validation rules as JSON
    
    # Status
    status = Column(
        SQLEnum(QuestStatus), 
        default=QuestStatus.DRAFT, 
        nullable=False,
        index=True
    )
    
    # Campaign relationship
    campaign_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("campaigns.id"), 
        nullable=False,
        index=True
    )
    
    # Category relationship (optional)
    category_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("categories.id"),
        nullable=True,
        index=True
    )
    
    # Creator relationship
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    campaign = relationship("Campaign", back_populates="quests")
    creator = relationship("User", back_populates="created_quests")
    category = relationship("Category", back_populates="quests")
