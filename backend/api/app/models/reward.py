"""
Reward SQLAlchemy model.
"""

from sqlalchemy import Column, String, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class RewardStatus(str, enum.Enum):
    """Reward status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    OUT_OF_STOCK = "out_of_stock"


class Reward(BaseModel):
    """Reward model"""
    __tablename__ = "rewards"
    
    # Basic info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    image_url = Column(String(500))
    
    # Reward properties
    points_cost = Column(Integer, nullable=False, default=0)
    stock_quantity = Column(Integer)  # NULL means unlimited
    
    # Status
    status = Column(
        SQLEnum(RewardStatus), 
        default=RewardStatus.ACTIVE, 
        nullable=False,
        index=True
    )
    
    # Organization relationship
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("organizations.id"), 
        nullable=False,
        index=True
    )
    
    # Category relationship (optional)
    category_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("categories.id"),
        nullable=True,
        index=True
    )
    
    # Creator relationship
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False,
        index=True
    )
    
    # Relationships
    organization = relationship("Organization", back_populates="rewards")
    creator = relationship("User", back_populates="created_rewards")
    category = relationship("Category", back_populates="rewards")
