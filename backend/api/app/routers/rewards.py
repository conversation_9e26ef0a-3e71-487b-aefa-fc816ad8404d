from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional
from uuid import UUID

from ..database import get_db
from ..models import (
    Reward, RewardCreate, RewardUpdate, RewardStatus,
    PaginationParams, PaginatedResponse, ErrorResponse
)
from ..auth import get_current_user, get_current_organization
from ..dependencies import require_permissions

router = APIRouter()

@router.get("/", response_model=PaginatedResponse)
async def get_rewards(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    status_filter: Optional[RewardStatus] = None,
    category_id: Optional[str] = None,
    search: Optional[str] = None,
    min_points: Optional[int] = None,
    max_points: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization)
):
    """Get paginated list of rewards for the organization"""
    try:
        # Build query
        query = select("*").select_from("rewards").where("organization_id = :org_id")
        params = {"org_id": organization_id}
        
        # Apply filters
        if status_filter:
            query = query.where("status = :status")
            params["status"] = status_filter
            
        if category_id:
            query = query.where("category_id = :category_id")
            params["category_id"] = category_id
            
        if search:
            query = query.where("name ILIKE :search OR description ILIKE :search")
            params["search"] = f"%{search}%"
            
        if min_points is not None:
            query = query.where("points_cost >= :min_points")
            params["min_points"] = min_points
            
        if max_points is not None:
            query = query.where("points_cost <= :max_points")
            params["max_points"] = max_points
        
        # Get total count
        count_query = select(func.count()).select_from("rewards").where("organization_id = :org_id")
        if status_filter:
            count_query = count_query.where("status = :status")
        if category_id:
            count_query = count_query.where("category_id = :category_id")
        if search:
            count_query = count_query.where("name ILIKE :search OR description ILIKE :search")
        if min_points is not None:
            count_query = count_query.where("points_cost >= :min_points")
        if max_points is not None:
            count_query = count_query.where("points_cost <= :max_points")
            
        total_result = await db.execute(count_query, params)
        total = total_result.scalar()
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.order_by("created_at DESC").offset(offset).limit(limit)
        
        result = await db.execute(query, params)
        rewards = result.fetchall()
        
        return PaginatedResponse(
            items=[dict(reward) for reward in rewards],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch rewards: {str(e)}"
        )

@router.post("/", response_model=Reward, status_code=status.HTTP_201_CREATED)
async def create_reward(
    reward_data: RewardCreate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["rewards:create"]))
):
    """Create a new reward"""
    try:
        query = """
        INSERT INTO rewards (organization_id, name, description, points_cost, stock_quantity, image_url, category_id, created_by)
        VALUES (:org_id, :name, :description, :points_cost, :stock_quantity, :image_url, :category_id, :created_by)
        RETURNING *
        """
        
        params = {
            "org_id": organization_id,
            "name": reward_data.name,
            "description": reward_data.description,
            "points_cost": reward_data.points_cost,
            "stock_quantity": reward_data.stock_quantity,
            "image_url": reward_data.image_url,
            "category_id": reward_data.category_id,
            "created_by": current_user["id"]
        }
        
        result = await db.execute(query, params)
        reward = result.fetchone()
        await db.commit()
        
        return Reward(**dict(reward))
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create reward: {str(e)}"
        )

@router.get("/{reward_id}", response_model=Reward)
async def get_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization)
):
    """Get a specific reward by ID"""
    try:
        query = "SELECT * FROM rewards WHERE id = :reward_id AND organization_id = :org_id"
        result = await db.execute(query, {"reward_id": reward_id, "org_id": organization_id})
        reward = result.fetchone()
        
        if not reward:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reward not found"
            )
            
        return Reward(**dict(reward))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch reward: {str(e)}"
        )

@router.put("/{reward_id}", response_model=Reward)
async def update_reward(
    reward_id: str,
    reward_data: RewardUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["rewards:update"]))
):
    """Update a reward"""
    try:
        # Check if reward exists
        check_query = "SELECT id FROM rewards WHERE id = :reward_id AND organization_id = :org_id"
        result = await db.execute(check_query, {"reward_id": reward_id, "org_id": organization_id})
        if not result.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reward not found"
            )
        
        # Build update query dynamically
        update_fields = []
        params = {"reward_id": reward_id, "org_id": organization_id}
        
        for field, value in reward_data.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = :{field}")
                params[field] = value
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields to update"
            )
        
        update_fields.append("updated_at = NOW()")
        
        query = f"""
        UPDATE rewards 
        SET {', '.join(update_fields)}
        WHERE id = :reward_id AND organization_id = :org_id
        RETURNING *
        """
        
        result = await db.execute(query, params)
        reward = result.fetchone()
        await db.commit()
        
        return Reward(**dict(reward))
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update reward: {str(e)}"
        )

@router.delete("/{reward_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["rewards:delete"]))
):
    """Delete a reward"""
    try:
        query = "DELETE FROM rewards WHERE id = :reward_id AND organization_id = :org_id"
        result = await db.execute(query, {"reward_id": reward_id, "org_id": organization_id})
        
        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reward not found"
            )
        
        await db.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete reward: {str(e)}"
        )

@router.post("/{reward_id}/redeem")
async def redeem_reward(
    reward_id: str,
    user_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["rewards:redeem"]))
):
    """Redeem a reward for a user"""
    try:
        # Check if reward exists and is available
        reward_query = """
        SELECT * FROM rewards 
        WHERE id = :reward_id AND organization_id = :org_id AND status = 'active'
        """
        reward_result = await db.execute(reward_query, {"reward_id": reward_id, "org_id": organization_id})
        reward = reward_result.fetchone()
        
        if not reward:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reward not found or not available"
            )
        
        # Check stock
        if reward.stock_quantity is not None and reward.stock_quantity <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reward is out of stock"
            )
        
        # Check user points
        user_query = "SELECT points_balance FROM users WHERE id = :user_id AND organization_id = :org_id"
        user_result = await db.execute(user_query, {"user_id": user_id, "org_id": organization_id})
        user = user_result.fetchone()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if user.points_balance < reward.points_cost:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient points"
            )
        
        # Process redemption
        async with db.begin():
            # Deduct points
            await db.execute(
                "UPDATE users SET points_balance = points_balance - :points WHERE id = :user_id",
                {"points": reward.points_cost, "user_id": user_id}
            )
            
            # Update stock
            if reward.stock_quantity is not None:
                await db.execute(
                    "UPDATE rewards SET stock_quantity = stock_quantity - 1 WHERE id = :reward_id",
                    {"reward_id": reward_id}
                )
            
            # Create redemption record
            redemption_query = """
            INSERT INTO reward_redemptions (user_id, reward_id, points_cost, status, redeemed_by)
            VALUES (:user_id, :reward_id, :points_cost, 'pending', :redeemed_by)
            RETURNING *
            """
            redemption_result = await db.execute(redemption_query, {
                "user_id": user_id,
                "reward_id": reward_id,
                "points_cost": reward.points_cost,
                "redeemed_by": current_user["id"]
            })
            redemption = redemption_result.fetchone()
        
        return {"message": "Reward redeemed successfully", "redemption_id": redemption.id}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to redeem reward: {str(e)}"
        )
