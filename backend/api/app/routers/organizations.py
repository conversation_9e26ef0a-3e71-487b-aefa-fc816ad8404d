from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
import uuid

from ..database import get_db, get_current_active_user, check_permission, verify_admin
from ..models import Organization, OrganizationCreate, OrganizationUpdate
from supabase import Client

router = APIRouter()

@router.get("/organizations", response_model=List[Organization])
async def list_organizations(
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(verify_admin)
):
    """
    List all organizations (admin only)
    """
    response = db.table("organizations").select("*").execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.post("/organizations", response_model=Organization, status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization: OrganizationCreate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(verify_admin)
):
    """
    Create a new organization (admin only)
    """
    response = db.table("organizations").insert(organization.dict()).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=400, detail="Failed to create organization")
    
    return response.data[0]

@router.get("/organizations/{organization_id}", response_model=Organization)
async def get_organization(
    organization_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """
    Get a specific organization
    """
    # Regular users can only view their own organization
    if current_user["role"] != "admin" and str(current_user["organization_id"]) != str(organization_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this organization"
        )
    
    response = db.table("organizations").select("*").eq("id", str(organization_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    return response.data[0]

@router.patch("/organizations/{organization_id}", response_model=Organization)
async def update_organization(
    organization_id: uuid.UUID,
    organization_update: OrganizationUpdate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """
    Update an organization
    """
    # Regular users can only update their own organization if they have the permission
    if current_user["role"] != "admin" and str(current_user["organization_id"]) != str(organization_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this organization"
        )
    
    # Check if organization exists
    check_response = db.table("organizations").select("id").eq("id", str(organization_id)).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    # Update the organization
    response = db.table("organizations").update(organization_update.dict(exclude_unset=True)).eq("id", str(organization_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data[0]

@router.delete("/organizations/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    organization_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(verify_admin)
):
    """
    Delete an organization (admin only)
    """
    # Check if organization exists
    check_response = db.table("organizations").select("id").eq("id", str(organization_id)).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    # Delete the organization
    response = db.table("organizations").delete().eq("id", str(organization_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return None
