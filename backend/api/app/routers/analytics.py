from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
import uuid
from datetime import date, datetime, timedelta

from ..database import get_db, check_permission
from ..models import (
    DateRangeParams, CampaignPerformanceMetrics, QuestPerformanceMetrics, RewardRedemptionMetrics
)
from supabase import Client

router = APIRouter()

@router.get("/analytics/campaigns", response_model=List[CampaignPerformanceMetrics])
async def get_campaign_performance(
    campaign_id: Optional[uuid.UUID] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:analytics"))
):
    """
    Get performance metrics for campaigns
    """
    organization_id = current_user["organization_id"]
    
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now().date()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Use a view or stored procedure in a real application
    # For this example, we'll use a raw SQL query
    query = f"""
    SELECT 
        c.id as campaign_id,
        c.name as campaign_name,
        COUNT(DISTINCT qc.customer_user_id) as total_participants,
        COUNT(qc.id) as total_completions,
        COALESCE(SUM(qc.points_awarded), 0) as total_points_awarded,
        CASE 
            WHEN COUNT(q.id) > 0 THEN 
                CAST(COUNT(qc.id) AS FLOAT) / (COUNT(DISTINCT qc.customer_user_id) * COUNT(DISTINCT q.id))
            ELSE 0 
        END as completion_rate
    FROM 
        campaigns c
    LEFT JOIN 
        quests q ON q.campaign_id = c.id
    LEFT JOIN 
        quest_completions qc ON qc.quest_id = q.id AND qc.status = 'approved'
    WHERE 
        c.organization_id = '{organization_id}'
        {f"AND c.id = '{campaign_id}'" if campaign_id else ""}
        AND (qc.completed_at IS NULL OR qc.completed_at BETWEEN '{start_date}' AND '{end_date}')
    GROUP BY 
        c.id, c.name
    ORDER BY 
        total_completions DESC
    """
    
    response = db.rpc("execute_sql", {"sql": query}).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.get("/analytics/quests", response_model=List[QuestPerformanceMetrics])
async def get_quest_performance(
    campaign_id: Optional[uuid.UUID] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:analytics"))
):
    """
    Get performance metrics for quests
    """
    organization_id = current_user["organization_id"]
    
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now().date()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Use a view or stored procedure in a real application
    query = f"""
    SELECT 
        q.id as quest_id,
        q.name as quest_name,
        c.id as campaign_id,
        c.name as campaign_name,
        COUNT(qc.id) as total_completions,
        COALESCE(SUM(qc.points_awarded), 0) as total_points_awarded,
        CASE 
            WHEN (SELECT COUNT(*) FROM customer_users cu WHERE cu.organization_id = '{organization_id}') > 0 THEN
                CAST(COUNT(qc.id) AS FLOAT) / (SELECT COUNT(*) FROM customer_users cu WHERE cu.organization_id = '{organization_id}')
            ELSE 0 
        END as completion_rate
    FROM 
        quests q
    JOIN 
        campaigns c ON q.campaign_id = c.id
    LEFT JOIN 
        quest_completions qc ON qc.quest_id = q.id AND qc.status = 'approved'
    WHERE 
        c.organization_id = '{organization_id}'
        {f"AND c.id = '{campaign_id}'" if campaign_id else ""}
        AND (qc.completed_at IS NULL OR qc.completed_at BETWEEN '{start_date}' AND '{end_date}')
    GROUP BY 
        q.id, q.name, c.id, c.name
    ORDER BY 
        total_completions DESC
    """
    
    response = db.rpc("execute_sql", {"sql": query}).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.get("/analytics/rewards", response_model=List[RewardRedemptionMetrics])
async def get_reward_redemption_metrics(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:analytics"))
):
    """
    Get redemption metrics for rewards
    """
    organization_id = current_user["organization_id"]
    
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now().date()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Use a view or stored procedure in a real application
    query = f"""
    SELECT 
        r.id as reward_id,
        r.name as reward_name,
        COUNT(rr.id) as total_redemptions,
        COALESCE(SUM(rr.points_spent), 0) as total_points_spent,
        CASE 
            WHEN COUNT(rr.id) > 0 THEN 
                CAST(SUM(rr.points_spent) AS FLOAT) / COUNT(rr.id)
            ELSE 0 
        END as average_points_per_redemption
    FROM 
        rewards r
    LEFT JOIN 
        reward_redemptions rr ON rr.reward_id = r.id AND rr.status = 'fulfilled'
    WHERE 
        r.organization_id = '{organization_id}'
        AND (rr.redeemed_at IS NULL OR rr.redeemed_at BETWEEN '{start_date}' AND '{end_date}')
    GROUP BY 
        r.id, r.name
    ORDER BY 
        total_redemptions DESC
    """
    
    response = db.rpc("execute_sql", {"sql": query}).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.get("/analytics/dashboard")
async def get_dashboard_metrics(
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:analytics"))
):
    """
    Get summary metrics for the dashboard
    """
    organization_id = current_user["organization_id"]
    
    # Get active campaigns count
    campaigns_response = db.table("campaigns").select("count", count="exact").eq("organization_id", organization_id).eq("status", "active").execute()
    
    # Get total customer users
    users_response = db.table("customer_users").select("count", count="exact").eq("organization_id", organization_id).execute()
    
    # Get total quests completed this month
    start_of_month = datetime.now().replace(day=1).date()
    quests_response = db.table("quest_completions").select(
        "count", count="exact"
    ).eq("status", "approved").gte("completed_at", start_of_month.isoformat()).execute()
    
    # Get total points awarded this month
    points_query = f"""
    SELECT COALESCE(SUM(points_awarded), 0) as total_points
    FROM quest_completions qc
    JOIN quests q ON qc.quest_id = q.id
    JOIN campaigns c ON q.campaign_id = c.id
    WHERE c.organization_id = '{organization_id}'
    AND qc.status = 'approved'
    AND qc.completed_at >= '{start_of_month}'
    """
    points_response = db.rpc("execute_sql", {"sql": points_query}).execute()
    
    # Get total rewards redeemed this month
    rewards_query = f"""
    SELECT COUNT(*) as total_redemptions
    FROM reward_redemptions rr
    JOIN rewards r ON rr.reward_id = r.id
    WHERE r.organization_id = '{organization_id}'
    AND rr.status = 'fulfilled'
    AND rr.redeemed_at >= '{start_of_month}'
    """
    rewards_response = db.rpc("execute_sql", {"sql": rewards_query}).execute()
    
    # Check for errors
    if any(r.error for r in [campaigns_response, users_response, quests_response, points_response, rewards_response]):
        raise HTTPException(status_code=400, detail="Error fetching dashboard metrics")
    
    # Compile metrics
    return {
        "active_campaigns": campaigns_response.count,
        "total_customers": users_response.count,
        "quests_completed_this_month": quests_response.count,
        "points_awarded_this_month": points_response.data[0]["total_points"] if points_response.data else 0,
        "rewards_redeemed_this_month": rewards_response.data[0]["total_redemptions"] if rewards_response.data else 0,
    }
