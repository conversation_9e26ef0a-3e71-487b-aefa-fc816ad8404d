from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional
from uuid import UUID

from ..database import get_db
from ..models import (
    Badge, BadgeCreate, BadgeUpdate, BadgeTier,
    PaginationParams, PaginatedResponse, ErrorResponse
)
from ..auth import get_current_user, get_current_organization
from ..dependencies import require_permissions

router = APIRouter()

@router.get("/", response_model=PaginatedResponse)
async def get_badges(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    tier: Optional[BadgeTier] = None,
    category_id: Optional[str] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization)
):
    """Get paginated list of badges for the organization"""
    try:
        # Build query
        query = select("*").select_from("badges").where("organization_id = :org_id")
        params = {"org_id": organization_id}
        
        # Apply filters
        if tier:
            query = query.where("tier = :tier")
            params["tier"] = tier
            
        if category_id:
            query = query.where("category_id = :category_id")
            params["category_id"] = category_id
            
        if search:
            query = query.where("name ILIKE :search OR description ILIKE :search")
            params["search"] = f"%{search}%"
        
        # Get total count
        count_query = select(func.count()).select_from("badges").where("organization_id = :org_id")
        if tier:
            count_query = count_query.where("tier = :tier")
        if category_id:
            count_query = count_query.where("category_id = :category_id")
        if search:
            count_query = count_query.where("name ILIKE :search OR description ILIKE :search")
            
        total_result = await db.execute(count_query, params)
        total = total_result.scalar()
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.order_by("created_at DESC").offset(offset).limit(limit)
        
        result = await db.execute(query, params)
        badges = result.fetchall()
        
        return PaginatedResponse(
            items=[dict(badge) for badge in badges],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch badges: {str(e)}"
        )

@router.post("/", response_model=Badge, status_code=status.HTTP_201_CREATED)
async def create_badge(
    badge_data: BadgeCreate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["badges:create"]))
):
    """Create a new badge"""
    try:
        query = """
        INSERT INTO badges (organization_id, name, description, icon_url, tier, criteria, points_reward, category_id, created_by)
        VALUES (:org_id, :name, :description, :icon_url, :tier, :criteria, :points_reward, :category_id, :created_by)
        RETURNING *
        """
        
        params = {
            "org_id": organization_id,
            "name": badge_data.name,
            "description": badge_data.description,
            "icon_url": badge_data.icon_url,
            "tier": badge_data.tier,
            "criteria": badge_data.criteria,
            "points_reward": badge_data.points_reward,
            "category_id": badge_data.category_id,
            "created_by": current_user["id"]
        }
        
        result = await db.execute(query, params)
        badge = result.fetchone()
        await db.commit()
        
        return Badge(**dict(badge))
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create badge: {str(e)}"
        )

@router.get("/{badge_id}", response_model=Badge)
async def get_badge(
    badge_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization)
):
    """Get a specific badge by ID"""
    try:
        query = "SELECT * FROM badges WHERE id = :badge_id AND organization_id = :org_id"
        result = await db.execute(query, {"badge_id": badge_id, "org_id": organization_id})
        badge = result.fetchone()
        
        if not badge:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Badge not found"
            )
            
        return Badge(**dict(badge))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch badge: {str(e)}"
        )

@router.put("/{badge_id}", response_model=Badge)
async def update_badge(
    badge_id: str,
    badge_data: BadgeUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["badges:update"]))
):
    """Update a badge"""
    try:
        # Check if badge exists
        check_query = "SELECT id FROM badges WHERE id = :badge_id AND organization_id = :org_id"
        result = await db.execute(check_query, {"badge_id": badge_id, "org_id": organization_id})
        if not result.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Badge not found"
            )
        
        # Build update query dynamically
        update_fields = []
        params = {"badge_id": badge_id, "org_id": organization_id}
        
        for field, value in badge_data.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = :{field}")
                params[field] = value
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields to update"
            )
        
        update_fields.append("updated_at = NOW()")
        
        query = f"""
        UPDATE badges 
        SET {', '.join(update_fields)}
        WHERE id = :badge_id AND organization_id = :org_id
        RETURNING *
        """
        
        result = await db.execute(query, params)
        badge = result.fetchone()
        await db.commit()
        
        return Badge(**dict(badge))
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update badge: {str(e)}"
        )

@router.delete("/{badge_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_badge(
    badge_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization),
    _: None = Depends(require_permissions(["badges:delete"]))
):
    """Delete a badge"""
    try:
        query = "DELETE FROM badges WHERE id = :badge_id AND organization_id = :org_id"
        result = await db.execute(query, {"badge_id": badge_id, "org_id": organization_id})
        
        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Badge not found"
            )
        
        await db.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete badge: {str(e)}"
        )

@router.get("/{badge_id}/recipients", response_model=PaginatedResponse)
async def get_badge_recipients(
    badge_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
    organization_id: str = Depends(get_current_organization)
):
    """Get users who have earned this badge"""
    try:
        # Check if badge exists
        badge_query = "SELECT id FROM badges WHERE id = :badge_id AND organization_id = :org_id"
        badge_result = await db.execute(badge_query, {"badge_id": badge_id, "org_id": organization_id})
        if not badge_result.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Badge not found"
            )
        
        # Get recipients with pagination
        query = """
        SELECT u.id, u.first_name, u.last_name, u.email, ub.earned_at
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        WHERE ub.badge_id = :badge_id AND u.organization_id = :org_id
        ORDER BY ub.earned_at DESC
        OFFSET :offset LIMIT :limit
        """
        
        count_query = """
        SELECT COUNT(*)
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        WHERE ub.badge_id = :badge_id AND u.organization_id = :org_id
        """
        
        offset = (page - 1) * limit
        params = {"badge_id": badge_id, "org_id": organization_id, "offset": offset, "limit": limit}
        
        # Get total count
        count_result = await db.execute(count_query, {"badge_id": badge_id, "org_id": organization_id})
        total = count_result.scalar()
        
        # Get recipients
        result = await db.execute(query, params)
        recipients = result.fetchall()
        
        return PaginatedResponse(
            items=[dict(recipient) for recipient in recipients],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch badge recipients: {str(e)}"
        )
