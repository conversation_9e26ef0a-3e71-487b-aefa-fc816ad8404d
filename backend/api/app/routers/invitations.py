from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
import uuid

from ..core.database import get_db
from ..schemas.invitation import InvitationCreate, Invitation
from ..services.invitation_service import InvitationService
from ..core.permissions import check_permission

router = APIRouter()

@router.post("/invitations", response_model=Invitation, status_code=status.HTTP_201_CREATED)
async def create_invitation(
    invitation: InvitationCreate,
    db: any = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("create:invitations"))
):
    """
    Create a new invitation.
    """
    invitation_service = InvitationService(db)
    new_invitation = await invitation_service.create_invitation(
        email=invitation.email,
        role=invitation.role,
        organization_id=current_user["organization_id"],
        invited_by=current_user["id"]
    )
    return new_invitation