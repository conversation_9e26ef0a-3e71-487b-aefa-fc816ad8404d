from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
import uuid
from datetime import date

from ..database import get_db, get_current_active_user, check_permission
from ..models import (
    Campaign, CampaignCreate, CampaignUpdate, 
    Quest, QuestCreate,
    ErrorResponse, SuccessResponse
)
from supabase import Client

router = APIRouter()

@router.get("/campaigns", response_model=List[Campaign])
async def list_campaigns(
    status: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:campaigns"))
):
    """
    List all campaigns for the current user's organization.
    Supports filtering by status, search term, and date range.
    """
    organization_id = current_user["organization_id"]
    
    # Start building the query
    query = db.table("campaigns").select("*").eq("organization_id", organization_id)
    
    # Apply filters
    if status:
        query = query.eq("status", status)
    
    if search:
        query = query.ilike("name", f"%{search}%")
    
    if start_date:
        query = query.gte("start_date", start_date.isoformat())
    
    if end_date:
        query = query.lte("end_date", end_date.isoformat())
    
    # Apply pagination
    query = query.range(offset, offset + limit - 1).order("created_at", desc=True)
    
    # Execute query
    response = query.execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.post("/campaigns", response_model=Campaign, status_code=status.HTTP_201_CREATED)
async def create_campaign(
    campaign: CampaignCreate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("create:campaigns"))
):
    """Create a new campaign"""
    # Ensure the campaign belongs to the user's organization
    if campaign.organization_id != current_user["organization_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot create campaign for another organization"
        )
    
    # Insert the campaign
    response = db.table("campaigns").insert(campaign.dict()).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=400, detail="Failed to create campaign")
    
    return response.data[0]

@router.get("/campaigns/{campaign_id}", response_model=Campaign)
async def get_campaign(
    campaign_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:campaigns"))
):
    """Get a specific campaign by ID"""
    organization_id = current_user["organization_id"]
    
    response = db.table("campaigns").select("*").eq("id", str(campaign_id)).eq("organization_id", organization_id).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=404, detail="Campaign not found")
    
    return response.data[0]

@router.patch("/campaigns/{campaign_id}", response_model=Campaign)
async def update_campaign(
    campaign_id: uuid.UUID,
    campaign_update: CampaignUpdate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("update:campaigns"))
):
    """Update a campaign"""
    organization_id = current_user["organization_id"]
    
    # Check if campaign exists and belongs to the user's organization
    check_response = db.table("campaigns").select("id").eq("id", str(campaign_id)).eq("organization_id", organization_id).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Campaign not found")
    
    # Update the campaign
    response = db.table("campaigns").update(campaign_update.dict(exclude_unset=True)).eq("id", str(campaign_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data[0]

@router.delete("/campaigns/{campaign_id}", response_model=SuccessResponse)
async def delete_campaign(
    campaign_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("delete:campaigns"))
):
    """Delete a campaign"""
    organization_id = current_user["organization_id"]
    
    # Check if campaign exists and belongs to the user's organization
    check_response = db.table("campaigns").select("id").eq("id", str(campaign_id)).eq("organization_id", organization_id).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Campaign not found")
    
    # Delete the campaign
    response = db.table("campaigns").delete().eq("id", str(campaign_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return SuccessResponse(message="Campaign deleted successfully")

@router.get("/campaigns/{campaign_id}/quests", response_model=List[Quest])
async def list_campaign_quests(
    campaign_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:quests"))
):
    """List all quests for a specific campaign"""
    organization_id = current_user["organization_id"]
    
    # First check if the campaign belongs to the user's organization
    campaign_check = db.table("campaigns").select("id").eq("id", str(campaign_id)).eq("organization_id", organization_id).execute()
    
    if campaign_check.error:
        raise HTTPException(status_code=400, detail=campaign_check.error.message)
    
    if not campaign_check.data or len(campaign_check.data) == 0:
        raise HTTPException(status_code=404, detail="Campaign not found")
    
    # Get quests for the campaign
    response = db.table("quests").select("*").eq("campaign_id", str(campaign_id)).order("created_at", desc=True).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.post("/campaigns/{campaign_id}/quests", response_model=Quest, status_code=status.HTTP_201_CREATED)
async def create_campaign_quest(
    campaign_id: uuid.UUID,
    quest: QuestCreate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("create:quests"))
):
    """Create a new quest for a campaign"""
    organization_id = current_user["organization_id"]
    
    # Check if campaign exists and belongs to the user's organization
    campaign_check = db.table("campaigns").select("id").eq("id", str(campaign_id)).eq("organization_id", organization_id).execute()
    
    if campaign_check.error:
        raise HTTPException(status_code=400, detail=campaign_check.error.message)
    
    if not campaign_check.data or len(campaign_check.data) == 0:
        raise HTTPException(status_code=404, detail="Campaign not found")
    
    # Ensure the quest is for the specified campaign
    if str(quest.campaign_id) != str(campaign_id):
        raise HTTPException(status_code=400, detail="Quest campaign_id does not match URL parameter")
    
    # Insert the quest
    response = db.table("quests").insert(quest.dict()).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=400, detail="Failed to create quest")
    
    return response.data[0]
