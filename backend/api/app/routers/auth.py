from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Any
import os
import jwt
from datetime import datetime, timedelta
import bcrypt

from ..database import get_db, get_admin_db
from ..models import To<PERSON>, User<PERSON><PERSON>, User
from supabase import Client

router = APIRouter()

# JWT configuration
JWT_SECRET = os.getenv("SUPABASE_JWT_SECRET")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

def create_access_token(data: Dict[str, Any], expires_delta: timedelta = None):
    """Create a new JWT token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return encoded_jwt

@router.post("/auth/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Client = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # Get user by email
    response = db.table("users").select("*").eq("email", form_data.username).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = response.data[0]
    
    # Check if user is active
    if not user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Verify password
    stored_password_hash = user.get("password_hash", "")
    if not bcrypt.checkpw(form_data.password.encode(), stored_password_hash.encode()):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user["id"])},
        expires_delta=access_token_expires
    )
    
    # Remove sensitive information from user object
    user.pop("password_hash", None)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user
    }

@router.post("/auth/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register_user(
    user: UserCreate,
    db: Client = Depends(get_admin_db)  # Use admin client for registration
):
    """
    Register a new user
    """
    # Check if email already exists
    check_response = db.table("users").select("id").eq("email", user.email).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if check_response.data and len(check_response.data) > 0:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Hash the password
    password_hash = bcrypt.hashpw(user.password.encode(), bcrypt.gensalt()).decode()
    
    # Prepare user data
    user_data = user.dict(exclude={"password"})
    user_data["password_hash"] = password_hash
    user_data["is_active"] = True
    
    # Insert the user
    response = db.table("users").insert(user_data).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=400, detail="Failed to create user")
    
    # Remove password_hash from response
    created_user = response.data[0]
    created_user.pop("password_hash", None)
    
    return created_user
