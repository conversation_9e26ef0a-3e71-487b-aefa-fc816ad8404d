from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
import uuid

from ..database import get_db, get_current_active_user, check_permission
from ..models import (
    Quest, QuestUpdate, QuestCompletion, QuestCompletionCreate, QuestCompletionUpdate,
    ErrorResponse, SuccessResponse
)
from supabase import Client

router = APIRouter()

@router.get("/quests", response_model=List[Quest])
async def list_quests(
    campaign_id: Optional[uuid.UUID] = None,
    category_id: Optional[uuid.UUID] = None,
    search: Optional[str] = None,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:quests"))
):
    """
    List all quests for the current user's organization.
    Supports filtering by campaign, category, and search term.
    """
    organization_id = current_user["organization_id"]
    
    # Start with a join query to get quests from the user's organization
    query = db.table("quests").select("quests.*").eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id")
    
    # Apply filters
    if campaign_id:
        query = query.eq("quests.campaign_id", str(campaign_id))
    
    if category_id:
        query = query.eq("quests.category_id", str(category_id))
    
    if search:
        query = query.ilike("quests.name", f"%{search}%")
    
    # Apply pagination
    query = query.range(offset, offset + limit - 1).order("quests.created_at", desc=True)
    
    # Execute query
    response = query.execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.get("/quests/{quest_id}", response_model=Quest)
async def get_quest(
    quest_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:quests"))
):
    """Get a specific quest by ID"""
    organization_id = current_user["organization_id"]
    
    # Join with campaigns to ensure the quest belongs to the user's organization
    response = db.table("quests").select("quests.*").eq("quests.id", str(quest_id)).eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id").execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    return response.data[0]

@router.patch("/quests/{quest_id}", response_model=Quest)
async def update_quest(
    quest_id: uuid.UUID,
    quest_update: QuestUpdate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("update:quests"))
):
    """Update a quest"""
    organization_id = current_user["organization_id"]
    
    # Check if quest exists and belongs to the user's organization
    check_response = db.table("quests").select("quests.id").eq("quests.id", str(quest_id)).eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id").execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    # Update the quest
    response = db.table("quests").update(quest_update.dict(exclude_unset=True)).eq("id", str(quest_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data[0]

@router.delete("/quests/{quest_id}", response_model=SuccessResponse)
async def delete_quest(
    quest_id: uuid.UUID,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("delete:quests"))
):
    """Delete a quest"""
    organization_id = current_user["organization_id"]
    
    # Check if quest exists and belongs to the user's organization
    check_response = db.table("quests").select("quests.id").eq("quests.id", str(quest_id)).eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id").execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if not check_response.data or len(check_response.data) == 0:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    # Delete the quest
    response = db.table("quests").delete().eq("id", str(quest_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return SuccessResponse(message="Quest deleted successfully")

@router.get("/quests/{quest_id}/completions", response_model=List[QuestCompletion])
async def list_quest_completions(
    quest_id: uuid.UUID,
    status: Optional[str] = None,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:quest_completions"))
):
    """List all completions for a specific quest"""
    organization_id = current_user["organization_id"]
    
    # First check if the quest belongs to the user's organization
    quest_check = db.table("quests").select("quests.id").eq("quests.id", str(quest_id)).eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id").execute()
    
    if quest_check.error:
        raise HTTPException(status_code=400, detail=quest_check.error.message)
    
    if not quest_check.data or len(quest_check.data) == 0:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    # Get completions for the quest
    query = db.table("quest_completions").select("*").eq("quest_id", str(quest_id))
    
    if status:
        query = query.eq("status", status)
    
    # Apply pagination
    query = query.range(offset, offset + limit - 1).order("completed_at", desc=True)
    
    # Execute query
    response = query.execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data

@router.post("/quests/{quest_id}/complete", response_model=QuestCompletion, status_code=status.HTTP_201_CREATED)
async def complete_quest(
    quest_id: uuid.UUID,
    completion: QuestCompletionCreate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("create:quest_completions"))
):
    """Record a quest completion"""
    organization_id = current_user["organization_id"]
    
    # Check if the quest belongs to the user's organization
    quest_check = db.table("quests").select("quests.id, quests.points, quests.validation_method").eq("quests.id", str(quest_id)).eq("campaigns.organization_id", organization_id).join("campaigns", "quests.campaign_id", "campaigns.id").execute()
    
    if quest_check.error:
        raise HTTPException(status_code=400, detail=quest_check.error.message)
    
    if not quest_check.data or len(quest_check.data) == 0:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    quest = quest_check.data[0]
    
    # Check if the customer user belongs to the user's organization
    customer_check = db.table("customer_users").select("id").eq("id", str(completion.customer_user_id)).eq("organization_id", organization_id).execute()
    
    if customer_check.error:
        raise HTTPException(status_code=400, detail=customer_check.error.message)
    
    if not customer_check.data or len(customer_check.data) == 0:
        raise HTTPException(status_code=404, detail="Customer user not found")
    
    # Determine initial status based on validation method
    initial_status = "pending" if quest["validation_method"] == "manual" else "approved"
    
    # Create completion record
    completion_data = completion.dict()
    completion_data["status"] = initial_status
    completion_data["points_awarded"] = quest["points"] if initial_status == "approved" else 0
    completion_data["completed_at"] = "now()"  # Use Postgres now() function
    
    # Insert the completion
    response = db.table("quest_completions").insert(completion_data).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    if not response.data or len(response.data) == 0:
        raise HTTPException(status_code=400, detail="Failed to record quest completion")
    
    # If approved, update customer points balance
    if initial_status == "approved":
        # This would typically be handled by a database trigger
        # But for demonstration, we'll update points here
        db.table("customer_users").update({"points_balance": db.raw(f"points_balance + {quest['points']}")}).eq("id", str(completion.customer_user_id)).execute()
    
    return response.data[0]

@router.patch("/quest-completions/{completion_id}", response_model=QuestCompletion)
async def update_quest_completion(
    completion_id: uuid.UUID,
    completion_update: QuestCompletionUpdate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("update:quest_completions"))
):
    """Update a quest completion (e.g., approve or reject)"""
    organization_id = current_user["organization_id"]
    
    # Get the completion with related quest and customer user
    completion_check = db.table("quest_completions").select(
        "quest_completions.*, quests.points, customer_users.id as customer_id, customer_users.organization_id"
    ).eq("quest_completions.id", str(completion_id)
    ).join("quests", "quest_completions.quest_id", "quests.id"
    ).join("customer_users", "quest_completions.customer_user_id", "customer_users.id"
    ).execute()
    
    if completion_check.error:
        raise HTTPException(status_code=400, detail=completion_check.error.message)
    
    if not completion_check.data or len(completion_check.data) == 0:
        raise HTTPException(status_code=404, detail="Quest completion not found")
    
    completion = completion_check.data[0]
    
    # Check if the customer belongs to the user's organization
    if completion["organization_id"] != organization_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this completion")
    
    # Handle status change
    update_data = completion_update.dict(exclude_unset=True)
    
    # If changing from pending to approved, award points
    if completion["status"] == "pending" and update_data.get("status") == "approved":
        update_data["points_awarded"] = completion["points"]
        
        # Update customer points balance
        db.table("customer_users").update(
            {"points_balance": db.raw(f"points_balance + {completion['points']}")}
        ).eq("id", completion["customer_id"]).execute()
    
    # Update the completion
    response = db.table("quest_completions").update(update_data).eq("id", str(completion_id)).execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    return response.data[0]
