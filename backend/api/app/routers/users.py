from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
import uuid
import bcrypt

from ..database import get_db, get_current_active_user, check_permission
from ..models import User, UserCreate, UserUpdate
from supabase import Client

router = APIRouter()

@router.get("/users", response_model=List[User])
async def list_users(
    search: Optional[str] = None,
    role: Optional[str] = None,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("read:users"))
):
    """
    List users in the current user's organization
    """
    organization_id = current_user["organization_id"]
    
    # Start building the query
    query = db.table("users").select("*").eq("organization_id", organization_id)
    
    # Apply filters
    if search:
        query = query.or_(f"email.ilike.%{search}%,first_name.ilike.%{search}%,last_name.ilike.%{search}%")
    
    if role:
        query = query.eq("role", role)
    
    # Apply pagination
    query = query.range(offset, offset + limit - 1).order("created_at", desc=True)
    
    # Execute query
    response = query.execute()
    
    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)
    
    # Remove password_hash from all users
    for user in response.data:
        user.pop("password_hash", None)
    
    return response.data

@router.post("/users", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate,
    db: Client = Depends(get_db),
    current_user: Dict[str, Any] = Depends(check_permission("create:users"))
):
    """
    Create a new user in the current user's organization
    """
    # Ensure the user belongs to the current user's organization
    if str(user.organization_id) != str(current_user["organization_id"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot create user for another organization"
        )
    
    # Check if email already exists
    check_response = db.table("users").select("id").eq("email", user.email).execute()
    
    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)
    
    if check_response.data and len(check_response.
