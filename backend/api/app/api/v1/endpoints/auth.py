"""
Authentication endpoints.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_user, get_current_user_organization_id
from app.schemas.auth import Token, UserRegister
from app.services.auth_service import AuthService
from app.crud.role import role_crud
from app.schemas.role import RoleCreate

router = APIRouter()


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    return await AuthService.login(
        db=db,
        email=form_data.username,
        password=form_data.password
    )


@router.post("/register", response_model=dict, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user
    """
    return await AuthService.register(db=db, user_data=user_data)


@router.post("/test-token", response_model=dict)
async def test_token(
    current_user: dict = Depends(get_current_user)
):
    """
    Test endpoint to verify token authentication
    """
    return {"message": "Token is valid", "user": current_user}


@router.post("/seed-default-role", response_model=dict)
async def seed_default_role(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Seed a default admin role for the user's organization
    """
    organization_id = current_user.get("organization_id")
    if not organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User has no organization"
        )

    # Check if admin role already exists
    existing_role = await role_crud.get_by_name_and_organization(
        db=db,
        name="Admin",
        organization_id=organization_id
    )

    if existing_role:
        return {"message": "Admin role already exists", "role_id": str(existing_role.id)}

    # Create admin role with all permissions
    admin_permissions = [
        "campaigns:read", "campaigns:create", "campaigns:update", "campaigns:delete",
        "quests:read", "quests:create", "quests:update", "quests:delete",
        "categories:read", "categories:create", "categories:update", "categories:delete",
        "badges:read", "badges:create", "badges:update", "badges:delete",
        "rewards:read", "rewards:create", "rewards:update", "rewards:delete",
        "users:read", "users:create", "users:update", "users:delete",
        "organizations:read", "organizations:update",
        "analytics:read"
    ]

    role_data = RoleCreate(
        name="Admin",
        description="Full administrative access to all features",
        level=100,
        color="#ef4444",
        permissions=admin_permissions,
        is_system=True,
        organization_id=organization_id
    )

    role = await role_crud.create(db=db, obj_in=role_data)

    # Update the current user to have this role
    from app.crud.user import user_crud
    user = await user_crud.get(db=db, id=current_user["id"])
    if user:
        await user_crud.update(db=db, db_obj=user, obj_in={"role_id": role.id})

    return {
        "message": "Admin role created and assigned to user",
        "role_id": str(role.id),
        "permissions": admin_permissions
    }
