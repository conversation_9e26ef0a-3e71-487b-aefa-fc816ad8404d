"""
Permission management endpoints.
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.core.permissions import (
    get_all_permissions,
    get_permissions_by_resource,
    get_permissions_by_action,
    PermissionResource,
    PermissionAction,
    FINE_GRAINED_PERMISSIONS
)
from app.schemas.permission import Permission, PermissionTemplate
from app.services.permission_service import PermissionService

router = APIRouter()


@router.get("/", response_model=List[Permission])
async def list_permissions(
    resource: PermissionResource = None,
    action: PermissionAction = None,
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """List all available permissions with optional filtering"""
    permissions_dict = get_all_permissions()
    
    # Apply filters if provided
    if resource:
        permissions_dict = get_permissions_by_resource(resource)
    
    if action:
        permissions_dict = get_permissions_by_action(action)
    
    # Convert to Permission schema format
    permissions = []
    for perm_id, perm_data in permissions_dict.items():
        permission = Permission(
            id=perm_id,
            name=perm_data["name"],
            description=perm_data["description"],
            resource=perm_data["resource"],
            action=perm_data["action"],
            scope=perm_data["scope"],
            category=perm_data["resource"].title()  # Use resource as category
        )
        permissions.append(permission)
    
    return permissions


@router.get("/resources", response_model=List[str])
async def list_permission_resources(
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """List all available permission resources"""
    return [resource.value for resource in PermissionResource]


@router.get("/actions", response_model=List[str])
async def list_permission_actions(
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """List all available permission actions"""
    return [action.value for action in PermissionAction]


@router.get("/templates", response_model=List[PermissionTemplate])
async def list_permission_templates(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """List available permission templates"""
    return await PermissionService.get_permission_templates(db, organization_id)


@router.get("/matrix", response_model=Dict[str, List[str]])
async def get_permission_matrix(
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permission matrix organized by resource"""
    matrix = {}
    
    for resource in PermissionResource:
        resource_permissions = get_permissions_by_resource(resource)
        matrix[resource.value] = list(resource_permissions.keys())
    
    return matrix


@router.get("/validate/{permission_id}", response_model=Dict[str, Any])
async def validate_permission(
    permission_id: str,
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Validate if a permission exists and get its details"""
    if permission_id not in FINE_GRAINED_PERMISSIONS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Permission '{permission_id}' not found"
        )
    
    permission_data = FINE_GRAINED_PERMISSIONS[permission_id]
    return {
        "valid": True,
        "permission": {
            "id": permission_id,
            **permission_data
        }
    }


@router.post("/check", response_model=Dict[str, bool])
async def check_permissions(
    permission_ids: List[str],
    current_user: dict = Depends(get_current_active_user)
):
    """Check if current user has specific permissions"""
    user_permissions = current_user.get("permissions", [])
    
    results = {}
    for permission_id in permission_ids:
        # Check direct permission
        has_direct = permission_id in user_permissions
        
        # Check manage permission (implies all other permissions for the resource)
        has_manage = False
        if ":" in permission_id:
            resource = permission_id.split(":")[0]
            manage_permission = f"{resource}:manage"
            has_manage = manage_permission in user_permissions
        
        results[permission_id] = has_direct or has_manage
    
    return results


@router.get("/user/{user_id}", response_model=List[str])
async def get_user_permissions(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:read"]))
):
    """Get effective permissions for a specific user"""
    return await PermissionService.get_user_permissions(
        db=db,
        user_id=user_id,
        organization_id=organization_id
    )


@router.get("/effective", response_model=List[str])
async def get_current_user_permissions(
    current_user: dict = Depends(get_current_active_user)
):
    """Get effective permissions for the current user"""
    return current_user.get("permissions", [])


@router.get("/hierarchy", response_model=Dict[str, List[str]])
async def get_permission_hierarchy(
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permission hierarchy showing which permissions imply others"""
    hierarchy = {}
    
    # Build hierarchy based on manage permissions
    for resource in PermissionResource:
        resource_permissions = get_permissions_by_resource(resource)
        manage_key = f"{resource.value}:manage"
        
        if manage_key in resource_permissions:
            # Manage permission implies all other permissions for the resource
            implied_permissions = [
                perm_id for perm_id in resource_permissions.keys()
                if perm_id != manage_key
            ]
            hierarchy[manage_key] = implied_permissions
    
    return hierarchy


@router.get("/categories", response_model=Dict[str, List[str]])
async def get_permissions_by_category(
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permissions organized by category/resource"""
    categories = {}
    
    for resource in PermissionResource:
        resource_permissions = get_permissions_by_resource(resource)
        categories[resource.value.title()] = list(resource_permissions.keys())
    
    return categories
