"""
Campaign management endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.campaign import Campaign, CampaignCreate, CampaignUpdate
from app.models.campaign import CampaignStatus
from app.services.campaign_service import CampaignService

router = APIRouter()


@router.get("/", response_model=List[Campaign])
async def list_campaigns(
    status_filter: Optional[CampaignStatus] = Query(None, description="Filter by campaign status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:read"]))
):
    """List campaigns for the current organization"""
    return await CampaignService.get_campaigns_by_organization(
        db=db,
        organization_id=organization_id,
        status_filter=status_filter,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=Campaign, status_code=status.HTTP_201_CREATED)
async def create_campaign(
    campaign_create: CampaignCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:create"]))
):
    """Create a new campaign"""
    return await CampaignService.create_campaign(
        db=db,
        campaign_create=campaign_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/audience-segments", response_model=List[str])
async def get_audience_segments(
    _: dict = Depends(require_permissions(["campaigns:read"]))
):
    """Get available audience segments for campaign targeting"""
    # For now, return predefined segments. In the future, this could be dynamic
    # based on user data, analytics, or custom segments defined by the organization
    return [
        "All Users",
        "New Users",
        "Active Users",
        "Premium Users",
        "Inactive Users",
        "High-Value Customers",
        "Mobile Users",
        "Web Users",
        "Returning Customers",
        "First-Time Buyers"
    ]


@router.get("/{campaign_id}", response_model=Campaign)
async def get_campaign(
    campaign_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:read"]))
):
    """Get a specific campaign by ID"""
    return await CampaignService.get_campaign_by_id(
        db=db,
        campaign_id=campaign_id,
        current_user_org_id=organization_id
    )


@router.put("/{campaign_id}", response_model=Campaign)
async def update_campaign(
    campaign_id: str,
    campaign_update: CampaignUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:update"]))
):
    """Update a campaign"""
    return await CampaignService.update_campaign(
        db=db,
        campaign_id=campaign_id,
        campaign_update=campaign_update,
        current_user_org_id=organization_id
    )


@router.delete("/{campaign_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_campaign(
    campaign_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:delete"]))
):
    """Delete a campaign"""
    await CampaignService.delete_campaign(
        db=db,
        campaign_id=campaign_id,
        current_user_org_id=organization_id
    )


@router.post("/{campaign_id}/activate", response_model=Campaign)
async def activate_campaign(
    campaign_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["campaigns:update"]))
):
    """Activate a campaign"""
    return await CampaignService.activate_campaign(
        db=db,
        campaign_id=campaign_id,
        current_user_org_id=organization_id
    )


@router.get("/test-no-auth", response_model=dict)
async def test_endpoint_no_auth():
    """Test endpoint without authentication"""
    return {"message": "Test endpoint working", "status": "ok"}


@router.get("/test-with-auth", response_model=dict)
async def test_endpoint_with_auth(
    current_user: dict = Depends(get_current_active_user)
):
    """Test endpoint with authentication but no permissions"""
    return {
        "message": "Test endpoint with auth working",
        "status": "ok",
        "user_id": current_user.get("id"),
        "permissions": current_user.get("permissions", [])
    }


@router.get("/test-simple", response_model=dict)
async def test_simple():
    """Test endpoint without any dependencies"""
    return {"message": "Simple test working", "status": "ok"}
