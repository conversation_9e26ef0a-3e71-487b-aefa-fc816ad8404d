"""
User invitation endpoints.
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.invitation import InvitationCreate, InvitationAccept, InvitationStatus
from app.services.invitation_service import InvitationService

router = APIRouter()


@router.post("/invite", status_code=status.HTTP_201_CREATED)
async def invite_user(
    invitation_data: InvitationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:create"]))
) -> Dict[str, Any]:
    """Send an invitation to a new user"""
    return await InvitationService.create_invitation(
        db=db,
        invitation_data=invitation_data,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/status/{token}")
async def get_invitation_status(
    token: str,
    db: AsyncSession = Depends(get_db)
) -> InvitationStatus:
    """Get invitation status by token (public endpoint)"""
    return await InvitationService.get_invitation_status(db=db, token=token)


@router.post("/accept", status_code=status.HTTP_201_CREATED)
async def accept_invitation(
    acceptance_data: InvitationAccept,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Accept an invitation and create user account (public endpoint)"""
    return await InvitationService.accept_invitation(db=db, acceptance_data=acceptance_data)


@router.get("/", response_model=List[Dict[str, Any]])
async def list_invitations(
    pending_only: bool = Query(False, description="Show only pending invitations"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:read"]))
):
    """List invitations for the current organization"""
    return await InvitationService.get_organization_invitations(
        db=db,
        organization_id=organization_id,
        skip=skip,
        limit=limit,
        pending_only=pending_only
    )


@router.delete("/{invitation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def cancel_invitation(
    invitation_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:delete"]))
):
    """Cancel a pending invitation"""
    await InvitationService.cancel_invitation(
        db=db,
        invitation_id=invitation_id,
        current_user_org_id=organization_id
    )


@router.post("/resend/{invitation_id}")
async def resend_invitation(
    invitation_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:create"]))
) -> Dict[str, Any]:
    """Resend an invitation email"""
    # This would involve creating a new invitation and canceling the old one
    # For now, return a simple response
    return {"message": "Invitation resent successfully"}


# Cleanup endpoint for expired invitations (admin only)
@router.post("/cleanup-expired")
async def cleanup_expired_invitations(
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_permissions(["users:delete"]))
) -> Dict[str, Any]:
    """Clean up expired invitations (admin only)"""
    from app.crud.invitation import invitation_crud
    
    count = await invitation_crud.expire_old_invitations(db)
    return {"message": f"Expired {count} old invitations"}
