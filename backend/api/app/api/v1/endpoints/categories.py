"""
Category management endpoints.
"""

from typing import List
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.category import Category, CategoryCreate, CategoryUpdate
from app.services.category_service import CategoryService

router = APIRouter()


@router.get("/", response_model=List[Category])
async def list_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:read"]))
):
    """List categories for the current organization"""
    return await CategoryService.get_categories_by_organization(
        db=db,
        organization_id=organization_id,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=Category, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_create: CategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:create"]))
):
    """Create a new category"""
    return await CategoryService.create_category(
        db=db,
        category_create=category_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{category_id}", response_model=Category)
async def get_category(
    category_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:read"]))
):
    """Get a specific category by ID"""
    return await CategoryService.get_category_by_id(
        db=db,
        category_id=category_id,
        current_user_org_id=organization_id
    )


@router.put("/{category_id}", response_model=Category)
async def update_category(
    category_id: str,
    category_update: CategoryUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:update"]))
):
    """Update a category"""
    return await CategoryService.update_category(
        db=db,
        category_id=category_id,
        category_update=category_update,
        current_user_org_id=organization_id
    )


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:delete"]))
):
    """Delete a category"""
    await CategoryService.delete_category(
        db=db,
        category_id=category_id,
        current_user_org_id=organization_id
    )


@router.post("/seed-defaults", response_model=List[Category], status_code=status.HTTP_201_CREATED)
async def seed_default_categories(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["categories:create"]))
):
    """Seed default categories for the organization"""
    default_categories = [
        {"name": "Engagement", "description": "User engagement activities", "color": "#3b82f6", "icon": "👥"},
        {"name": "Social", "description": "Social media related activities", "color": "#8b5cf6", "icon": "📱"},
        {"name": "Onboarding", "description": "New user onboarding tasks", "color": "#10b981", "icon": "🚀"},
        {"name": "Purchase", "description": "Purchase and transaction related", "color": "#f59e0b", "icon": "💰"},
        {"name": "Content", "description": "Content creation and sharing", "color": "#ef4444", "icon": "📝"},
        {"name": "Referral", "description": "Referral and invitation activities", "color": "#06b6d4", "icon": "🤝"},
        {"name": "Achievement", "description": "Milestone and achievement tasks", "color": "#84cc16", "icon": "🏆"},
    ]

    created_categories = []
    for cat_data in default_categories:
        # Check if category already exists
        existing = await CategoryService.get_categories_by_organization(db, organization_id)
        if not any(c.name == cat_data["name"] for c in existing):
            category_create = CategoryCreate(
                name=cat_data["name"],
                description=cat_data["description"],
                color=cat_data["color"],
                icon=cat_data["icon"],
                organization_id=organization_id
            )
            category = await CategoryService.create_category(
                db=db,
                category_create=category_create,
                current_user_id=current_user["id"],
                current_user_org_id=organization_id
            )
            created_categories.append(category)

    return created_categories
