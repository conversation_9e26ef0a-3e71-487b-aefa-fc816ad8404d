"""
Badge management endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.badge import Badge, BadgeCreate, BadgeUpdate
from app.models.badge import BadgeTier, BadgeStatus
from app.services.badge_service import BadgeService

router = APIRouter()


@router.get("/", response_model=List[Badge])
async def list_badges(
    tier_filter: Optional[BadgeTier] = Query(None, description="Filter by badge tier"),
    status_filter: Optional[BadgeStatus] = Query(None, description="Filter by badge status"),
    search: Optional[str] = Query(None, description="Search badges by name or description"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:read"]))
):
    """List badges for the current organization"""
    return await BadgeService.get_badges_by_organization(
        db=db,
        organization_id=organization_id,
        tier_filter=tier_filter,
        status_filter=status_filter,
        search=search,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=Badge, status_code=status.HTTP_201_CREATED)
async def create_badge(
    badge_create: BadgeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:create"]))
):
    """Create a new badge"""
    return await BadgeService.create_badge(
        db=db,
        badge_create=badge_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{badge_id}", response_model=Badge)
async def get_badge(
    badge_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:read"]))
):
    """Get a specific badge by ID"""
    return await BadgeService.get_badge_by_id(
        db=db,
        badge_id=badge_id,
        current_user_org_id=organization_id
    )


@router.put("/{badge_id}", response_model=Badge)
async def update_badge(
    badge_id: str,
    badge_update: BadgeUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:update"]))
):
    """Update a badge"""
    return await BadgeService.update_badge(
        db=db,
        badge_id=badge_id,
        badge_update=badge_update,
        current_user_org_id=organization_id
    )


@router.delete("/{badge_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_badge(
    badge_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:delete"]))
):
    """Delete a badge"""
    await BadgeService.delete_badge(
        db=db,
        badge_id=badge_id,
        current_user_org_id=organization_id
    )


@router.get("/tier/{tier}", response_model=List[Badge])
async def get_badges_by_tier(
    tier: BadgeTier,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["badges:read"]))
):
    """Get badges by tier"""
    return await BadgeService.get_badges_by_tier(
        db=db,
        organization_id=organization_id,
        tier=tier,
        skip=skip,
        limit=limit
    )
