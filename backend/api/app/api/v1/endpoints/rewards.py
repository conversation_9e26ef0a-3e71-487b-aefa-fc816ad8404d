"""
Reward management endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.reward import Reward, RewardCreate, RewardUpdate
from app.models.reward import RewardStatus
from app.services.reward_service import RewardService

router = APIRouter()


@router.get("/", response_model=List[Reward])
async def list_rewards(
    status_filter: Optional[RewardStatus] = Query(None, description="Filter by reward status"),
    min_points: Optional[int] = Query(None, ge=0, description="Minimum points cost"),
    max_points: Optional[int] = Query(None, ge=0, description="Maximum points cost"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:read"]))
):
    """List rewards for the current organization"""
    return await RewardService.get_rewards_by_organization(
        db=db,
        organization_id=organization_id,
        status_filter=status_filter,
        min_points=min_points,
        max_points=max_points,
        skip=skip,
        limit=limit
    )


@router.get("/available", response_model=List[Reward])
async def list_available_rewards(
    user_points: int = Query(..., ge=0, description="User's current points"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:read"]))
):
    """List rewards that user can afford with their points"""
    return await RewardService.get_available_rewards(
        db=db,
        organization_id=organization_id,
        user_points=user_points,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=Reward, status_code=status.HTTP_201_CREATED)
async def create_reward(
    reward_create: RewardCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:create"]))
):
    """Create a new reward"""
    return await RewardService.create_reward(
        db=db,
        reward_create=reward_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{reward_id}", response_model=Reward)
async def get_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:read"]))
):
    """Get a specific reward by ID"""
    return await RewardService.get_reward_by_id(
        db=db,
        reward_id=reward_id,
        current_user_org_id=organization_id
    )


@router.put("/{reward_id}", response_model=Reward)
async def update_reward(
    reward_id: str,
    reward_update: RewardUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:update"]))
):
    """Update a reward"""
    return await RewardService.update_reward(
        db=db,
        reward_id=reward_id,
        reward_update=reward_update,
        current_user_org_id=organization_id
    )


@router.delete("/{reward_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:delete"]))
):
    """Delete a reward"""
    await RewardService.delete_reward(
        db=db,
        reward_id=reward_id,
        current_user_org_id=organization_id
    )


@router.post("/{reward_id}/activate", response_model=Reward)
async def activate_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:update"]))
):
    """Activate a reward"""
    return await RewardService.activate_reward(
        db=db,
        reward_id=reward_id,
        current_user_org_id=organization_id
    )


@router.post("/{reward_id}/deactivate", response_model=Reward)
async def deactivate_reward(
    reward_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["rewards:update"]))
):
    """Deactivate a reward"""
    return await RewardService.deactivate_reward(
        db=db,
        reward_id=reward_id,
        current_user_org_id=organization_id
    )
