"""
Analytics endpoints.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_user_organization_id
from app.api.deps import require_permissions
from app.services.analytics_service import AnalyticsService

router = APIRouter()


@router.get("/overview", response_model=Dict[str, Any])
async def get_organization_overview(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get high-level analytics overview for the organization"""
    return await AnalyticsService.get_organization_overview(
        db=db,
        organization_id=organization_id
    )


@router.get("/campaigns/{campaign_id}", response_model=Dict[str, Any])
async def get_campaign_analytics(
    campaign_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get detailed analytics for a specific campaign"""
    return await AnalyticsService.get_campaign_analytics(
        db=db,
        campaign_id=campaign_id,
        organization_id=organization_id
    )


@router.get("/quests", response_model=List[Dict[str, Any]])
async def get_quest_analytics(
    limit: int = Query(10, ge=1, le=50, description="Number of top quests to return"),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get analytics for top quests by points"""
    return await AnalyticsService.get_quest_analytics(
        db=db,
        organization_id=organization_id,
        limit=limit
    )


@router.get("/badges", response_model=Dict[str, Any])
async def get_badge_analytics(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get badge distribution and analytics"""
    return await AnalyticsService.get_badge_analytics(
        db=db,
        organization_id=organization_id
    )


@router.get("/rewards", response_model=Dict[str, Any])
async def get_reward_analytics(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get reward analytics and statistics"""
    return await AnalyticsService.get_reward_analytics(
        db=db,
        organization_id=organization_id
    )


@router.get("/", response_model=Dict[str, Any])
async def get_comprehensive_analytics(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["analytics:read"]))
):
    """Get comprehensive analytics combining all data sources"""

    # Get all analytics data
    overview = await AnalyticsService.get_organization_overview(db, organization_id)
    quest_analytics = await AnalyticsService.get_quest_analytics(db, organization_id, limit=5)
    badge_analytics = await AnalyticsService.get_badge_analytics(db, organization_id)
    reward_analytics = await AnalyticsService.get_reward_analytics(db, organization_id)

    return {
        "overview": overview,
        "top_quests": quest_analytics,
        "badges": badge_analytics,
        "rewards": reward_analytics,
        "generated_at": "2024-01-01T00:00:00Z"  # Would use actual timestamp
    }
