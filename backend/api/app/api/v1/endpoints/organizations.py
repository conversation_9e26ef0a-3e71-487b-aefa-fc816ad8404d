"""
Organization management endpoints.
"""

from typing import List
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_admin
from app.schemas.organization import Organization, OrganizationCreate, OrganizationUpdate
from app.services.organization_service import OrganizationService

router = APIRouter()


@router.get("/", response_model=List[Organization])
async def list_organizations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin)  # Only admins can list all organizations
):
    """List all organizations (admin only)"""
    return await OrganizationService.list_organizations(
        db=db,
        skip=skip,
        limit=limit
    )


@router.get("/me", response_model=Organization)
async def get_my_organization(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
):
    """Get current user's organization"""
    return await OrganizationService.get_organization_by_id(
        db=db,
        org_id=organization_id,
        current_user_org_id=organization_id,
        is_admin=current_user.get("is_superuser", False)
    )


@router.post("/", response_model=Organization, status_code=status.HTTP_201_CREATED)
async def create_organization(
    org_create: OrganizationCreate,
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin)  # Only admins can create organizations
):
    """Create a new organization (admin only)"""
    return await OrganizationService.create_organization(
        db=db,
        org_create=org_create
    )


@router.get("/{org_id}", response_model=Organization)
async def get_organization(
    org_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
):
    """Get organization by ID"""
    return await OrganizationService.get_organization_by_id(
        db=db,
        org_id=org_id,
        current_user_org_id=organization_id,
        is_admin=current_user.get("is_superuser", False)
    )


@router.put("/{org_id}", response_model=Organization)
async def update_organization(
    org_id: str,
    org_update: OrganizationUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
):
    """Update organization"""
    return await OrganizationService.update_organization(
        db=db,
        org_id=org_id,
        org_update=org_update,
        current_user_org_id=organization_id,
        is_admin=current_user.get("is_superuser", False)
    )


@router.delete("/{org_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    org_id: str,
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin)  # Only admins can delete organizations
):
    """Delete organization (admin only)"""
    await OrganizationService.delete_organization(
        db=db,
        org_id=org_id
    )
