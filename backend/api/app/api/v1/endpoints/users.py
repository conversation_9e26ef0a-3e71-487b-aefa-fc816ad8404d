"""
User management endpoints.
"""

from typing import List
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.user import User, UserCreate, UserUpdate
from app.services.user_service import UserService

router = APIRouter()


@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: dict = Depends(get_current_active_user)
):
    """Get current user information"""
    return current_user


@router.get("/", response_model=List[User])
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:read"]))
):
    """List users in the current organization"""
    return await UserService.get_users_by_organization(
        db=db,
        organization_id=organization_id,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_create: UserCreate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:create"]))
):
    """Create a new user in the current organization"""
    return await UserService.create_user(
        db=db,
        user_create=user_create,
        current_user_org_id=organization_id
    )


@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:update"]))
):
    """Update a user"""
    return await UserService.update_user(
        db=db,
        user_id=user_id,
        user_update=user_update,
        current_user_org_id=organization_id
    )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:delete"]))
):
    """Delete a user"""
    await UserService.delete_user(
        db=db,
        user_id=user_id,
        current_user_org_id=organization_id
    )


@router.put("/{user_id}/role", response_model=User)
async def assign_user_role(
    user_id: str,
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:update"]))
):
    """Assign a role to a user"""
    return await UserService.assign_role(
        db=db,
        user_id=user_id,
        role_id=role_id,
        current_user_org_id=organization_id
    )


@router.get("/{user_id}/permissions", response_model=List[str])
async def get_user_permissions(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["users:read"]))
):
    """Get effective permissions for a user"""
    return await UserService.get_user_permissions(
        db=db,
        user_id=user_id,
        current_user_org_id=organization_id
    )
