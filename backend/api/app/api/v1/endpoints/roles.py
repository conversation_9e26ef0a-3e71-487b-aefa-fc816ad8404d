"""
Role management endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.role import Role, RoleCreate, RoleUpdate, RoleClone
from app.schemas.permission import PermissionTemplate
from app.services.role_service import RoleService
from app.services.permission_service import PermissionService

router = APIRouter()


@router.get("/", response_model=List[Role])
async def list_roles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    include_system: bool = Query(True, description="Include system roles"),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """List roles in the current organization"""
    return await RoleService.get_roles_by_organization(
        db=db,
        organization_id=organization_id,
        skip=skip,
        limit=limit,
        include_system=include_system
    )


@router.get("/{role_id}", response_model=Role)
async def get_role(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get a specific role by ID"""
    return await RoleService.get_role_by_id(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )


@router.post("/", response_model=Role, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_create: RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:create"]))
):
    """Create a new role"""
    return await RoleService.create_role(
        db=db,
        role_create=role_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.put("/{role_id}", response_model=Role)
async def update_role(
    role_id: str,
    role_update: RoleUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:update"]))
):
    """Update a role"""
    return await RoleService.update_role(
        db=db,
        role_id=role_id,
        role_update=role_update,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:delete"]))
):
    """Delete a role (non-system roles only)"""
    await RoleService.delete_role(
        db=db,
        role_id=role_id,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.post("/{role_id}/clone", response_model=Role, status_code=status.HTTP_201_CREATED)
async def clone_role(
    role_id: str,
    role_clone: RoleClone,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:create"]))
):
    """Clone an existing role with a new name"""
    return await RoleService.clone_role(
        db=db,
        role_id=role_id,
        new_name=role_clone.name,
        new_description=role_clone.description,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.post("/from-template", response_model=Role, status_code=status.HTTP_201_CREATED)
async def create_role_from_template(
    template_id: str,
    role_name: str,
    role_description: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:create"]))
):
    """Create a role from a permission template"""
    return await RoleService.create_role_from_template(
        db=db,
        template_id=template_id,
        role_name=role_name,
        role_description=role_description,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{role_id}/users", response_model=List[dict])
async def get_role_users(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read", "users:read"]))
):
    """Get users assigned to a specific role"""
    return await RoleService.get_role_users(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )


@router.get("/{role_id}/permissions", response_model=List[str])
async def get_role_permissions(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permissions for a specific role"""
    role = await RoleService.get_role_by_id(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )
    return role.permissions


@router.put("/{role_id}/permissions", response_model=Role)
async def update_role_permissions(
    role_id: str,
    permissions: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:update"]))
):
    """Update permissions for a specific role"""
    role_update = RoleUpdate(permissions=permissions)
    return await RoleService.update_role(
        db=db,
        role_id=role_id,
        role_update=role_update,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{role_id}/conflicts", response_model=List[dict])
async def get_role_permission_conflicts(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permission conflicts for a specific role"""
    role = await RoleService.get_role_by_id(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )
    return await PermissionService.get_permission_conflicts(role.permissions)


@router.get("/{role_id}/recommendations", response_model=List[dict])
async def get_role_permission_recommendations(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get permission recommendations for a specific role"""
    role = await RoleService.get_role_by_id(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )
    return await PermissionService.get_permission_recommendations(
        current_permissions=role.permissions,
        role_level=role.level
    )


@router.post("/{role_id}/validate", response_model=dict)
async def validate_role_permissions(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Validate all permissions in a role"""
    role = await RoleService.get_role_by_id(
        db=db,
        role_id=role_id,
        organization_id=organization_id
    )
    
    validation_results = await PermissionService.validate_permissions(role.permissions)
    conflicts = await PermissionService.get_permission_conflicts(role.permissions)
    
    return {
        "role_id": role_id,
        "role_name": role.name,
        "total_permissions": len(role.permissions),
        "valid_permissions": sum(validation_results.values()),
        "invalid_permissions": len(role.permissions) - sum(validation_results.values()),
        "conflicts": conflicts,
        "validation_details": validation_results
    }


@router.get("/templates/available", response_model=List[PermissionTemplate])
async def get_available_templates(
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["roles:read"]))
):
    """Get available role templates"""
    return await PermissionService.get_permission_templates(db, organization_id)
