"""
Seeding endpoints for generating sample data.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import Dict, Any
import uuid
from datetime import datetime, timedelta, timezone
import random

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.models.campaign import Campaign, CampaignStatus
from app.models.quest import Quest, QuestStatus, QuestValidationType, QuestFrequency
from app.models.badge import Badge, BadgeTier, BadgeStatus
from app.models.reward import Reward, RewardStatus
from app.models.category import Category
from app.models.user import User
from app.models.organization import Organization
from app.models.role import Role
from app.core.permissions import get_all_permissions
from app.core.security import get_password_hash

router = APIRouter()

# Sample data templates
CAMPAIGN_TEMPLATES = [
    {
        "name": "Holiday Engagement Campaign",
        "description": "Boost customer engagement during the holiday season with festive quests and rewards",
        "status": CampaignStatus.ACTIVE,
        "target_audience": {"segments": ["new_customers", "loyal_customers"], "age_range": [18, 65]}
    },
    {
        "name": "Summer Fitness Challenge",
        "description": "Encourage healthy habits with fitness-focused activities and wellness rewards",
        "status": CampaignStatus.ACTIVE,
        "target_audience": {"segments": ["fitness_enthusiasts"], "interests": ["health", "fitness"]}
    },
    {
        "name": "Back to School Promotion",
        "description": "Welcome students back with educational content and study-related rewards",
        "status": CampaignStatus.DRAFT,
        "target_audience": {"segments": ["students", "parents"], "age_range": [16, 45]}
    },
    {
        "name": "Product Launch Buzz",
        "description": "Generate excitement around our new product launch with exclusive previews",
        "status": CampaignStatus.ACTIVE,
        "target_audience": {"segments": ["early_adopters", "tech_enthusiasts"]}
    },
    {
        "name": "Customer Loyalty Program",
        "description": "Reward long-term customers with exclusive benefits and recognition",
        "status": CampaignStatus.ACTIVE,
        "target_audience": {"segments": ["loyal_customers"], "tenure": "6_months_plus"}
    }
]

QUEST_TEMPLATES = [
    {
        "title": "Sign up for our newsletter",
        "description": "Stay updated with our latest news and exclusive offers",
        "points_reward": 50,
        "frequency": QuestFrequency.ONE_TIME,
        "validation_type": QuestValidationType.AUTOMATIC,
        "validation_criteria": {"action": "newsletter_signup"}
    },
    {
        "title": "Complete your profile",
        "description": "Add your personal information to get personalized recommendations",
        "points_reward": 100,
        "frequency": QuestFrequency.ONE_TIME,
        "validation_type": QuestValidationType.AUTOMATIC,
        "validation_criteria": {"profile_completion": 80}
    },
    {
        "title": "Share on social media",
        "description": "Share our content on your favorite social media platform",
        "points_reward": 75,
        "frequency": QuestFrequency.DAILY,
        "validation_type": QuestValidationType.MANUAL,
        "validation_criteria": {"platforms": ["facebook", "twitter", "instagram"]}
    },
    {
        "title": "Write a product review",
        "description": "Help others by sharing your experience with our products",
        "points_reward": 150,
        "frequency": QuestFrequency.UNLIMITED,
        "validation_type": QuestValidationType.MANUAL,
        "validation_criteria": {"min_words": 50}
    },
    {
        "title": "Refer a friend",
        "description": "Invite friends to join our platform and earn rewards together",
        "points_reward": 200,
        "frequency": QuestFrequency.UNLIMITED,
        "validation_type": QuestValidationType.AUTOMATIC,
        "validation_criteria": {"referral_signup": True}
    },
    {
        "title": "Daily check-in",
        "description": "Visit our platform daily to maintain your streak",
        "points_reward": 25,
        "frequency": QuestFrequency.DAILY,
        "validation_type": QuestValidationType.AUTOMATIC,
        "validation_criteria": {"action": "daily_login"}
    },
    {
        "title": "Upload a photo",
        "description": "Share a photo related to our brand or products",
        "points_reward": 100,
        "frequency": QuestFrequency.WEEKLY,
        "validation_type": QuestValidationType.UPLOAD,
        "validation_criteria": {"file_types": ["jpg", "png"], "max_size": "5MB"}
    },
    {
        "title": "Use promo code",
        "description": "Enter the special promo code to unlock exclusive content",
        "points_reward": 125,
        "frequency": QuestFrequency.ONE_TIME,
        "validation_type": QuestValidationType.CODE,
        "validation_criteria": {"code": "WELCOME2024"}
    }
]

BADGE_TEMPLATES = [
    {
        "name": "First Steps",
        "description": "Complete your first quest and start your journey",
        "icon": "Trophy",
        "tier": BadgeTier.BRONZE,
        "criteria": "Complete 1 quest",
        "points_reward": 50,
        "status": BadgeStatus.ACTIVE,
    },
    {
        "name": "Social Butterfly",
        "description": "Share content on social media platforms",
        "icon": "Heart",
        "tier": BadgeTier.SILVER,
        "criteria": "Share 5 posts on social media",
        "points_reward": 100,
        "status": BadgeStatus.ACTIVE,
    },
    {
        "name": "Streak Master",
        "description": "Maintain a consistent daily login streak",
        "icon": "Zap",
        "tier": BadgeTier.GOLD,
        "criteria": "Login for 30 consecutive days",
        "points_reward": 300,
        "status": BadgeStatus.ACTIVE,
    },
    {
        "name": "Explorer",
        "description": "Discover and complete various types of quests",
        "icon": "Target",
        "tier": BadgeTier.SILVER,
        "criteria": "Complete quests from 5 different categories",
        "points_reward": 200,
        "status": BadgeStatus.ACTIVE,
    },
    {
        "name": "Champion",
        "description": "Reach the highest level of engagement",
        "icon": "Crown",
        "tier": BadgeTier.PLATINUM,
        "criteria": "Earn 10,000 total points",
        "points_reward": 500,
        "status": BadgeStatus.DRAFT,
    },
    {
        "name": "Guardian",
        "description": "Help and support other community members",
        "icon": "Shield",
        "tier": BadgeTier.GOLD,
        "criteria": "Refer 10 successful new users",
        "points_reward": 400,
        "status": BadgeStatus.PAUSED,
    },
    {
        "name": "Rising Star",
        "description": "Show exceptional performance in your first month",
        "icon": "Star",
        "tier": BadgeTier.SILVER,
        "criteria": "Complete 20 quests in first 30 days",
        "points_reward": 250,
        "status": BadgeStatus.ACTIVE,
    },
    {
        "name": "Diamond Elite",
        "description": "Achieve the ultimate level of mastery",
        "icon": "Award",
        "tier": BadgeTier.DIAMOND,
        "criteria": "Maintain top 1% performance for 6 months",
        "points_reward": 1000,
        "status": BadgeStatus.DRAFT,
    },
]

REWARD_TEMPLATES = [
    {
        "name": "10% Discount Coupon",
        "description": "Get 10% off your next purchase",
        "points_cost": 500,
        "stock_quantity": 100,
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "Free Shipping Voucher",
        "description": "Free shipping on your next order",
        "points_cost": 300,
        "stock_quantity": 50,
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "Exclusive T-Shirt",
        "description": "Limited edition branded t-shirt",
        "points_cost": 1000,
        "stock_quantity": 25,
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "Premium Membership (1 Month)",
        "description": "One month of premium features access",
        "points_cost": 1500,
        "stock_quantity": None,  # Unlimited
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "Gift Card ($25)",
        "description": "$25 gift card for our online store",
        "points_cost": 2000,
        "stock_quantity": 20,
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "VIP Event Access",
        "description": "Exclusive access to VIP customer events",
        "points_cost": 2500,
        "stock_quantity": 10,
        "status": RewardStatus.ACTIVE
    },
    {
        "name": "Custom Avatar",
        "description": "Personalized avatar for your profile",
        "points_cost": 750,
        "stock_quantity": None,  # Unlimited
        "status": RewardStatus.ACTIVE
    }
]

# User and Role templates
ROLE_TEMPLATES = [
    {
        "name": "Content Manager",
        "description": "Manages campaigns, quests, and content creation",
        "level": 70,
        "color": "#3b82f6",
        "permissions": [
            "dashboard:read",
            "campaigns:read", "campaigns:create", "campaigns:update",
            "quests:read", "quests:create", "quests:update",
            "badges:read", "badges:create", "badges:update",
            "rewards:read", "rewards:create", "rewards:update",
            "analytics:read"
        ]
    },
    {
        "name": "Marketing Specialist",
        "description": "Focuses on campaign management and analytics",
        "level": 60,
        "color": "#10b981",
        "permissions": [
            "dashboard:read",
            "campaigns:read", "campaigns:create", "campaigns:update",
            "quests:read", "quests:create",
            "analytics:read", "analytics:export"
        ]
    },
    {
        "name": "Customer Support",
        "description": "Handles user support and basic content viewing",
        "level": 40,
        "color": "#f59e0b",
        "permissions": [
            "dashboard:read",
            "campaigns:read",
            "quests:read",
            "badges:read",
            "rewards:read",
            "users:read"
        ]
    },
    {
        "name": "Data Analyst",
        "description": "Specialized in analytics and reporting",
        "level": 50,
        "color": "#8b5cf6",
        "permissions": [
            "dashboard:read",
            "analytics:read", "analytics:export", "analytics:advanced",
            "campaigns:read",
            "quests:read",
            "users:read"
        ]
    }
]

USER_TEMPLATES = [
    {
        "email": "<EMAIL>",
        "first_name": "Sarah",
        "last_name": "Johnson",
        "role_name": "Content Manager"
    },
    {
        "email": "<EMAIL>",
        "first_name": "Mike",
        "last_name": "Chen",
        "role_name": "Marketing Specialist"
    },
    {
        "email": "<EMAIL>",
        "first_name": "Emma",
        "last_name": "Davis",
        "role_name": "Customer Support"
    },
    {
        "email": "<EMAIL>",
        "first_name": "Alex",
        "last_name": "Rodriguez",
        "role_name": "Data Analyst"
    },
    {
        "email": "<EMAIL>",
        "first_name": "Lisa",
        "last_name": "Wang",
        "role_name": "Content Manager"
    },
    {
        "email": "<EMAIL>",
        "first_name": "James",
        "last_name": "Brown",
        "role_name": "Marketing Specialist"
    },
    {
        "email": "<EMAIL>",
        "first_name": "Maria",
        "last_name": "Garcia",
        "role_name": "Customer Support"
    },
    {
        "email": "<EMAIL>",
        "first_name": "David",
        "last_name": "Kim",
        "role_name": "Data Analyst"
    }
]


@router.post("/seed-sample-data")
async def seed_sample_data(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Seed the database with comprehensive sample data including campaigns, quests, badges, and rewards.
    """
    
    try:
        # Get categories for assignment
        categories_result = await db.execute(
            select(Category).filter(Category.organization_id == organization_id)
        )
        categories = categories_result.scalars().all()

        if not categories:
            raise HTTPException(
                status_code=400,
                detail="No categories found. Please seed categories first using /categories/seed-defaults"
            )

        created_data = {
            "campaigns": [],
            "quests": [],
            "badges": [],
            "rewards": []
        }

        # Create campaigns
        for template in CAMPAIGN_TEMPLATES:
            campaign = Campaign(
                id=uuid.uuid4(),
                name=template["name"],
                description=template["description"],
                status=template["status"],
                target_audience=template["target_audience"],
                start_date=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)),
                end_date=datetime.now(timezone.utc) + timedelta(days=random.randint(30, 90)),
                organization_id=organization_id,
                created_by=current_user["id"]
            )
            db.add(campaign)
            created_data["campaigns"].append({
                "id": str(campaign.id),
                "name": campaign.name,
                "status": campaign.status.value
            })

        # Flush to get campaign IDs
        await db.flush()
        
        # Create quests for each campaign
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns = campaigns_result.scalars().all()

        for campaign in campaigns:
            # Create 2-3 quests per campaign
            quest_count = random.randint(2, 3)
            selected_templates = random.sample(QUEST_TEMPLATES, min(quest_count, len(QUEST_TEMPLATES)))

            for template in selected_templates:
                quest = Quest(
                    id=uuid.uuid4(),
                    title=template["title"],
                    description=template["description"],
                    points_reward=template["points_reward"],
                    frequency=template["frequency"],
                    validation_type=template["validation_type"],
                    validation_criteria=template["validation_criteria"],
                    status=random.choice([QuestStatus.ACTIVE, QuestStatus.DRAFT]),
                    campaign_id=campaign.id,
                    category_id=random.choice(categories).id if categories else None,
                    created_by=current_user["id"]
                )
                db.add(quest)
                created_data["quests"].append({
                    "id": str(quest.id),
                    "title": quest.title,
                    "campaign": campaign.name,
                    "points": quest.points_reward
                })
        
        # Create badges
        for template in BADGE_TEMPLATES:
            badge = Badge(
                id=uuid.uuid4(),
                name=template["name"],
                description=template["description"],
                icon=template["icon"],
                tier=template["tier"],
                criteria=template["criteria"],
                points_reward=template["points_reward"],
                status=template["status"],
                times_earned=0,
                organization_id=organization_id,
                category_id=random.choice(categories).id if categories else None,
                created_by=current_user["id"]
            )
            db.add(badge)
            created_data["badges"].append({
                "id": str(badge.id),
                "name": badge.name,
                "tier": badge.tier.value,
                "status": badge.status.value,
                "points": badge.points_reward
            })

        # Create rewards
        for template in REWARD_TEMPLATES:
            reward = Reward(
                id=uuid.uuid4(),
                name=template["name"],
                description=template["description"],
                points_cost=template["points_cost"],
                stock_quantity=template["stock_quantity"],
                status=template["status"],
                organization_id=organization_id,
                category_id=random.choice(categories).id if categories else None,
                created_by=current_user["id"]
            )
            db.add(reward)
            created_data["rewards"].append({
                "id": str(reward.id),
                "name": reward.name,
                "cost": reward.points_cost,
                "stock": reward.stock_quantity
            })

        # Commit all changes
        await db.commit()
        
        return {
            "message": "Sample data seeded successfully",
            "summary": {
                "campaigns_created": len(created_data["campaigns"]),
                "quests_created": len(created_data["quests"]),
                "badges_created": len(created_data["badges"]),
                "rewards_created": len(created_data["rewards"])
            },
            "data": created_data
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to seed data: {str(e)}")


@router.post("/seed-users-and-roles")
async def seed_users_and_roles(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Seed the database with sample users and roles for development and testing.
    """
    try:
        created_data = {
            "roles": [],
            "users": []
        }

        # Create custom roles
        role_mapping = {}
        for template in ROLE_TEMPLATES:
            role = Role(
                id=uuid.uuid4(),
                organization_id=organization_id,
                name=template["name"],
                description=template["description"],
                level=template["level"],
                color=template["color"],
                permissions=template["permissions"],
                is_system=False
            )
            db.add(role)
            role_mapping[template["name"]] = role
            created_data["roles"].append({
                "id": str(role.id),
                "name": role.name,
                "level": role.level,
                "permissions_count": len(role.permissions)
            })

        # Flush to get role IDs
        await db.flush()

        # Create sample users
        default_password = "TempPassword123!"  # Users should change this on first login
        password_hash = get_password_hash(default_password)

        for template in USER_TEMPLATES:
            role = role_mapping.get(template["role_name"])
            if not role:
                continue

            user = User(
                id=uuid.uuid4(),
                organization_id=organization_id,
                email=template["email"],
                password_hash=password_hash,
                first_name=template["first_name"],
                last_name=template["last_name"],
                is_active=True,
                is_superuser=False,
                role_id=role.id
            )
            db.add(user)
            created_data["users"].append({
                "id": str(user.id),
                "email": user.email,
                "name": f"{user.first_name} {user.last_name}",
                "role": role.name,
                "default_password": default_password
            })

        # Commit all changes
        await db.commit()

        return {
            "message": "Users and roles seeded successfully",
            "summary": {
                "roles_created": len(created_data["roles"]),
                "users_created": len(created_data["users"])
            },
            "data": created_data,
            "important_note": f"All users have the default password: {default_password}. They should change it on first login."
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to seed users and roles: {str(e)}")


@router.delete("/clear-users-and-roles")
async def clear_users_and_roles(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all non-superuser users and custom roles from the organization.
    This preserves system roles and superuser accounts.
    """
    try:
        # Count before deletion
        users_result = await db.execute(
            select(User).filter(
                User.organization_id == organization_id,
                User.is_superuser == False
            )
        )
        users_count = len(users_result.scalars().all())

        roles_result = await db.execute(
            select(Role).filter(
                Role.organization_id == organization_id,
                Role.is_system == False
            )
        )
        roles_count = len(roles_result.scalars().all())

        # Delete non-superuser users
        await db.execute(
            delete(User).filter(
                User.organization_id == organization_id,
                User.is_superuser == False
            )
        )

        # Delete custom roles (non-system)
        await db.execute(
            delete(Role).filter(
                Role.organization_id == organization_id,
                Role.is_system == False
            )
        )

        await db.commit()

        return {
            "message": "Users and roles cleared successfully",
            "summary": {
                "users_deleted": users_count,
                "roles_deleted": roles_count
            },
            "note": "System roles and superuser accounts were preserved"
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to clear users and roles: {str(e)}")


@router.post("/seed-campaigns")
async def seed_campaigns_only(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Seed only campaign data.
    """

    try:
        created_campaigns = []

        for template in CAMPAIGN_TEMPLATES:
            campaign = Campaign(
                id=uuid.uuid4(),
                name=template["name"],
                description=template["description"],
                status=template["status"],
                target_audience=template["target_audience"],
                start_date=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)),
                end_date=datetime.now(timezone.utc) + timedelta(days=random.randint(30, 90)),
                organization_id=organization_id,
                created_by=current_user["id"]
            )
            db.add(campaign)
            created_campaigns.append({
                "id": str(campaign.id),
                "name": campaign.name,
                "status": campaign.status.value
            })

        await db.commit()

        return {
            "message": "Campaigns seeded successfully",
            "campaigns_created": len(created_campaigns),
            "campaigns": created_campaigns
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to seed campaigns: {str(e)}")


@router.post("/seed-quests/{campaign_id}")
async def seed_quests_for_campaign(
    campaign_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Seed quests for a specific campaign.
    """
    try:
        # Verify campaign exists and belongs to user's organization
        campaign_result = await db.execute(
            select(Campaign).filter(
                Campaign.id == campaign_id,
                Campaign.organization_id == organization_id
            )
        )
        campaign = campaign_result.scalar_one_or_none()

        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")

        # Get categories
        categories_result = await db.execute(
            select(Category).filter(Category.organization_id == organization_id)
        )
        categories = categories_result.scalars().all()

        created_quests = []

        # Create 3-5 quests for the campaign
        quest_count = random.randint(3, 5)
        selected_templates = random.sample(QUEST_TEMPLATES, min(quest_count, len(QUEST_TEMPLATES)))

        for template in selected_templates:
            quest = Quest(
                id=uuid.uuid4(),
                title=template["title"],
                description=template["description"],
                points_reward=template["points_reward"],
                frequency=template["frequency"],
                validation_type=template["validation_type"],
                validation_criteria=template["validation_criteria"],
                status=random.choice([QuestStatus.ACTIVE, QuestStatus.DRAFT]),
                campaign_id=campaign.id,
                category_id=random.choice(categories).id if categories else None,
                created_by=current_user["id"]
            )
            db.add(quest)
            created_quests.append({
                "id": str(quest.id),
                "title": quest.title,
                "points": quest.points_reward,
                "status": quest.status.value
            })

        await db.commit()

        return {
            "message": f"Quests seeded successfully for campaign '{campaign.name}'",
            "campaign_id": campaign_id,
            "campaign_name": campaign.name,
            "quests_created": len(created_quests),
            "quests": created_quests
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to seed quests: {str(e)}")


@router.delete("/clear-sample-data")
async def clear_sample_data(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all sample data for the current organization.
    WARNING: This will delete all campaigns, quests, badges, and rewards!
    """
    try:
        # Count existing data
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns = campaigns_result.scalars().all()
        campaigns_count = len(campaigns)

        # Get quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests = quests_result.scalars().all()
        quests_count = len(quests)

        badges_result = await db.execute(
            select(Badge).filter(Badge.organization_id == organization_id)
        )
        badges = badges_result.scalars().all()
        badges_count = len(badges)

        rewards_result = await db.execute(
            select(Reward).filter(Reward.organization_id == organization_id)
        )
        rewards = rewards_result.scalars().all()
        rewards_count = len(rewards)

        # Delete in correct order (respecting foreign key constraints)
        # Quests first (they reference campaigns)
        for quest in quests:
            await db.delete(quest)

        # Then campaigns
        for campaign in campaigns:
            await db.delete(campaign)

        # Badges and rewards
        for badge in badges:
            await db.delete(badge)

        for reward in rewards:
            await db.delete(reward)

        await db.commit()

        return {
            "message": "Sample data cleared successfully",
            "deleted": {
                "campaigns": campaigns_count,
                "quests": quests_count,
                "badges": badges_count,
                "rewards": rewards_count
            }
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to clear data: {str(e)}")


@router.get("/data-summary")
async def get_data_summary(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Get a summary of existing data for the current organization.
    """
    try:
        # Count campaigns
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns_count = len(campaigns_result.scalars().all())

        # Count quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests_count = len(quests_result.scalars().all())

        # Count badges
        badges_result = await db.execute(
            select(Badge).filter(Badge.organization_id == organization_id)
        )
        badges_count = len(badges_result.scalars().all())

        # Count rewards
        rewards_result = await db.execute(
            select(Reward).filter(Reward.organization_id == organization_id)
        )
        rewards_count = len(rewards_result.scalars().all())

        # Count categories
        categories_result = await db.execute(
            select(Category).filter(Category.organization_id == organization_id)
        )
        categories_count = len(categories_result.scalars().all())

        return {
            "organization_id": str(organization_id),
            "data_summary": {
                "campaigns": campaigns_count,
                "quests": quests_count,
                "badges": badges_count,
                "rewards": rewards_count,
                "categories": categories_count
            },
            "seeding_available": categories_count > 0
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get data summary: {str(e)}")


@router.post("/seed-badges")
async def seed_badges(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Seed the database with sample badges for development and testing
    """
    try:
        created_badges = []

        for badge_data in BADGE_TEMPLATES:
            # Check if badge with this name already exists
            existing_badge_result = await db.execute(
                select(Badge).filter(
                    Badge.organization_id == organization_id,
                    Badge.name == badge_data["name"]
                )
            )
            existing_badge = existing_badge_result.scalar_one_or_none()

            # Skip if badge already exists
            if existing_badge:
                continue

            # Create the badge
            badge = Badge(
                id=uuid.uuid4(),
                name=badge_data["name"],
                description=badge_data["description"],
                icon=badge_data["icon"],
                tier=badge_data["tier"],
                criteria=badge_data["criteria"],
                points_reward=badge_data["points_reward"],
                status=badge_data["status"],
                times_earned=0,
                organization_id=organization_id,
                created_by=current_user["id"]
            )
            db.add(badge)
            created_badges.append({
                "id": str(badge.id),
                "name": badge.name,
                "tier": badge.tier.value,
                "status": badge.status.value,
                "points": badge.points_reward
            })

        await db.commit()

        return {
            "message": f"Successfully created {len(created_badges)} badges",
            "badges_created": len(created_badges),
            "badges": created_badges
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to seed badges: {str(e)}"
        )


@router.delete("/clear-badges")
async def clear_badges(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all badges for the organization (development only)
    """
    try:
        # Count existing badges
        badges_result = await db.execute(
            select(Badge).filter(Badge.organization_id == organization_id)
        )
        badges = badges_result.scalars().all()
        deleted_count = len(badges)

        # Delete all badges for the organization
        for badge in badges:
            await db.delete(badge)

        await db.commit()

        return {
            "message": f"Successfully deleted {deleted_count} badges",
            "badges_deleted": deleted_count
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear badges: {str(e)}"
        )


@router.delete("/clear-all-data")
async def clear_all_data(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear ALL data for the organization including categories.
    WARNING: This will delete EVERYTHING - campaigns, quests, badges, rewards, and categories!
    This is a destructive operation and cannot be undone.
    """
    try:
        # Count existing data before deletion
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns = campaigns_result.scalars().all()
        campaigns_count = len(campaigns)

        # Get quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests = quests_result.scalars().all()
        quests_count = len(quests)

        badges_result = await db.execute(
            select(Badge).filter(Badge.organization_id == organization_id)
        )
        badges = badges_result.scalars().all()
        badges_count = len(badges)

        rewards_result = await db.execute(
            select(Reward).filter(Reward.organization_id == organization_id)
        )
        rewards = rewards_result.scalars().all()
        rewards_count = len(rewards)

        categories_result = await db.execute(
            select(Category).filter(Category.organization_id == organization_id)
        )
        categories = categories_result.scalars().all()
        categories_count = len(categories)

        # Delete in correct order (respecting foreign key constraints)
        # 1. Quests first (they reference campaigns and categories)
        for quest in quests:
            await db.delete(quest)

        # 2. Then campaigns
        for campaign in campaigns:
            await db.delete(campaign)

        # 3. Badges and rewards (they reference categories)
        for badge in badges:
            await db.delete(badge)

        for reward in rewards:
            await db.delete(reward)

        # 4. Finally categories (last because others reference them)
        for category in categories:
            await db.delete(category)

        await db.commit()

        return {
            "message": "ALL data cleared successfully for the organization",
            "warning": "This operation deleted EVERYTHING including categories",
            "deleted": {
                "campaigns": campaigns_count,
                "quests": quests_count,
                "badges": badges_count,
                "rewards": rewards_count,
                "categories": categories_count,
                "total_items": campaigns_count + quests_count + badges_count + rewards_count + categories_count
            }
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to clear all data: {str(e)}")


@router.delete("/clear-categories")
async def clear_categories(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all categories for the organization.
    WARNING: This will also clear all badges, quests, and rewards that reference these categories!
    """
    try:
        # First, we need to clear all items that reference categories
        # Count everything before deletion
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns = campaigns_result.scalars().all()
        campaigns_count = len(campaigns)

        # Get quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests = quests_result.scalars().all()
        quests_count = len(quests)

        badges_result = await db.execute(
            select(Badge).filter(Badge.organization_id == organization_id)
        )
        badges = badges_result.scalars().all()
        badges_count = len(badges)

        rewards_result = await db.execute(
            select(Reward).filter(Reward.organization_id == organization_id)
        )
        rewards = rewards_result.scalars().all()
        rewards_count = len(rewards)

        categories_result = await db.execute(
            select(Category).filter(Category.organization_id == organization_id)
        )
        categories = categories_result.scalars().all()
        categories_count = len(categories)

        # Delete in correct order
        for quest in quests:
            await db.delete(quest)

        for campaign in campaigns:
            await db.delete(campaign)

        for badge in badges:
            await db.delete(badge)

        for reward in rewards:
            await db.delete(reward)

        for category in categories:
            await db.delete(category)

        await db.commit()

        return {
            "message": "Categories and all related data cleared successfully",
            "warning": "This operation also deleted all campaigns, quests, badges, and rewards",
            "deleted": {
                "categories": categories_count,
                "campaigns": campaigns_count,
                "quests": quests_count,
                "badges": badges_count,
                "rewards": rewards_count,
                "total_items": campaigns_count + quests_count + badges_count + rewards_count + categories_count
            }
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to clear categories: {str(e)}")


@router.delete("/clear-rewards")
async def clear_rewards(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all rewards for the organization.
    """
    try:
        # Count existing rewards
        rewards_result = await db.execute(
            select(Reward).filter(Reward.organization_id == organization_id)
        )
        rewards = rewards_result.scalars().all()
        deleted_count = len(rewards)

        # Delete all rewards for the organization
        for reward in rewards:
            await db.delete(reward)

        await db.commit()

        return {
            "message": f"Successfully deleted {deleted_count} rewards",
            "rewards_deleted": deleted_count
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear rewards: {str(e)}"
        )


@router.delete("/clear-quests")
async def clear_quests(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all quests for the organization.
    """
    try:
        # Count existing quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests = quests_result.scalars().all()
        deleted_count = len(quests)

        # Delete all quests for the organization
        for quest in quests:
            await db.delete(quest)

        await db.commit()

        return {
            "message": f"Successfully deleted {deleted_count} quests",
            "quests_deleted": deleted_count
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear quests: {str(e)}"
        )


@router.delete("/clear-campaigns")
async def clear_campaigns(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id)
) -> Dict[str, Any]:
    """
    Clear all campaigns for the organization.
    WARNING: This will also delete all quests that belong to these campaigns!
    """
    try:
        # Count existing campaigns and their quests
        campaigns_result = await db.execute(
            select(Campaign).filter(Campaign.organization_id == organization_id)
        )
        campaigns = campaigns_result.scalars().all()
        campaigns_count = len(campaigns)

        # Get quests through campaign relationship
        quests_result = await db.execute(
            select(Quest).join(Campaign).filter(Campaign.organization_id == organization_id)
        )
        quests = quests_result.scalars().all()
        quests_count = len(quests)

        # Delete quests first (they reference campaigns)
        for quest in quests:
            await db.delete(quest)

        # Then delete campaigns
        for campaign in campaigns:
            await db.delete(campaign)

        await db.commit()

        return {
            "message": f"Successfully deleted {campaigns_count} campaigns and {quests_count} quests",
            "campaigns_deleted": campaigns_count,
            "quests_deleted": quests_count
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear campaigns: {str(e)}"
        )


@router.delete("/nuclear-clear-all")
async def nuclear_clear_all_data(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    🚨 NUCLEAR OPTION 🚨
    Clear ALL data from ALL organizations - Complete database reset!

    WARNING: This is EXTREMELY DESTRUCTIVE and will:
    - Delete ALL organizations and their data
    - Delete ALL users (except superusers)
    - Delete ALL campaigns, quests, badges, rewards, categories
    - Delete ALL roles (except system roles)
    - Reset the entire database to a clean state

    This operation is IRREVERSIBLE and should ONLY be used for:
    - Development environment resets
    - Testing scenarios
    - Emergency database cleanup

    REQUIRES: Superuser privileges
    """
    # Check if user is superuser
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=403,
            detail="Access denied. This operation requires superuser privileges."
        )

    try:
        # Count all data before deletion for reporting
        total_counts = {}

        # Count organizations
        orgs_result = await db.execute(select(Organization))
        organizations = orgs_result.scalars().all()
        total_counts["organizations"] = len(organizations)

        # Count all users (we'll recreate superusers after clearing everything)
        users_result = await db.execute(select(User))
        all_users = users_result.scalars().all()
        superusers = [u for u in all_users if u.is_superuser]

        # Store superuser info for recreation
        superuser_data = []
        for user in superusers:
            superuser_data.append({
                "email": user.email,
                "password_hash": user.password_hash,
                "first_name": user.first_name,
                "last_name": user.last_name
            })

        total_counts["users_total"] = len(all_users)
        total_counts["users_to_delete"] = len(all_users)  # Delete all, recreate superusers
        total_counts["superusers_to_recreate"] = len(superusers)

        # Count all content across all organizations
        campaigns_result = await db.execute(select(Campaign))
        all_campaigns = campaigns_result.scalars().all()
        total_counts["campaigns"] = len(all_campaigns)

        # Count quests through campaigns
        quests_result = await db.execute(select(Quest))
        all_quests = quests_result.scalars().all()
        total_counts["quests"] = len(all_quests)

        badges_result = await db.execute(select(Badge))
        all_badges = badges_result.scalars().all()
        total_counts["badges"] = len(all_badges)

        rewards_result = await db.execute(select(Reward))
        all_rewards = rewards_result.scalars().all()
        total_counts["rewards"] = len(all_rewards)

        categories_result = await db.execute(select(Category))
        all_categories = categories_result.scalars().all()
        total_counts["categories"] = len(all_categories)

        roles_result = await db.execute(select(Role))
        all_roles = roles_result.scalars().all()
        # Separate roles: system roles (keep), regular org roles (delete), orphaned roles (delete)
        system_roles = [r for r in all_roles if r.is_system and r.organization_id is None]
        regular_roles = [r for r in all_roles if r.organization_id is not None]  # All roles with org
        orphaned_roles = [r for r in all_roles if not r.is_system and r.organization_id is None]  # Orphaned non-system roles

        total_counts["roles_total"] = len(all_roles)
        total_counts["roles_to_delete"] = len(regular_roles) + len(orphaned_roles)
        total_counts["system_roles_kept"] = len(system_roles)

        # Start the nuclear deletion process
        # Order is critical to respect foreign key constraints

        # 1. Delete all quests first (they reference campaigns and categories)
        for quest in all_quests:
            await db.delete(quest)

        # 2. Delete all campaigns (they reference organizations)
        for campaign in all_campaigns:
            await db.delete(campaign)

        # 3. Delete all badges and rewards (they reference categories and organizations)
        for badge in all_badges:
            await db.delete(badge)

        for reward in all_rewards:
            await db.delete(reward)

        # 4. Delete all categories (they reference organizations)
        for category in all_categories:
            await db.delete(category)

        # 5. Delete ALL users (including superusers - we'll recreate them)
        for user in all_users:
            await db.delete(user)

        # 6. Delete all roles that belong to organizations
        for role in regular_roles:
            await db.delete(role)

        # 7. Delete orphaned roles (non-system roles without organization)
        for role in orphaned_roles:
            await db.delete(role)

        # 8. Delete all organizations
        for org in organizations:
            await db.delete(org)

        # 9. Flush to ensure all deletions are processed
        await db.flush()

        # 10. Create a system organization for superusers
        from app.models.organization import Organization as OrgModel
        system_org = OrgModel(
            id=uuid.uuid4(),
            name="System Administration",
            slug="system-admin",
            description="System organization for superuser accounts",
            website="",
            industry="Technology",
            company_size="Enterprise",
            primary_color="#dc2626",
            subscription_plan="enterprise"
        )
        db.add(system_org)

        # 11. Recreate superusers in the system organization
        recreated_superusers = []
        for user_data in superuser_data:
            new_superuser = User(
                id=uuid.uuid4(),
                email=user_data["email"],
                password_hash=user_data["password_hash"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                is_active=True,
                is_superuser=True,
                organization_id=system_org.id,
                role_id=None
            )
            db.add(new_superuser)
            recreated_superusers.append(new_superuser)

        # Commit the nuclear deletion
        await db.commit()

        # Calculate total items deleted
        total_deleted = (
            total_counts["campaigns"] +
            total_counts["quests"] +
            total_counts["badges"] +
            total_counts["rewards"] +
            total_counts["categories"] +
            total_counts["roles_to_delete"] +
            total_counts["users_to_delete"] +
            total_counts["organizations"]
        )

        return {
            "message": "🚨 NUCLEAR CLEAR COMPLETED 🚨",
            "warning": "ALL DATA HAS BEEN DELETED FROM ALL ORGANIZATIONS",
            "operation": "IRREVERSIBLE DATABASE RESET",
            "performed_by": current_user.get("email", "unknown"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "deleted_counts": {
                "organizations": total_counts["organizations"],
                "users": total_counts["users_to_delete"],
                "campaigns": total_counts["campaigns"],
                "quests": total_counts["quests"],
                "badges": total_counts["badges"],
                "rewards": total_counts["rewards"],
                "categories": total_counts["categories"],
                "roles": total_counts["roles_to_delete"],
                "total_items_deleted": total_deleted
            },
            "recreated": {
                "superusers": total_counts["superusers_to_recreate"],
                "system_organization": {
                    "id": str(system_org.id),
                    "name": system_org.name,
                    "purpose": "Houses recreated superuser accounts"
                }
            },
            "preserved": {
                "system_roles": total_counts["system_roles_kept"]
            },
            "database_state": "CLEAN - Ready for fresh setup",
            "superuser_note": "Superusers recreated in new system organization with same credentials"
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Nuclear clear operation failed: {str(e)}"
        )


@router.delete("/clear-all-organizations-data")
async def clear_all_organizations_data(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    🧹 GLOBAL DATA CLEAR 🧹
    Clear ALL content data from ALL organizations while preserving organizations and users.

    This will delete:
    - ALL campaigns, quests, badges, rewards, categories from ALL organizations
    - ALL non-system roles from ALL organizations

    This will preserve:
    - ALL organizations (structure intact)
    - ALL users (accounts intact)
    - System roles

    Use this for:
    - Resetting all content while keeping organizational structure
    - Preparing for fresh data import across all organizations
    - Cleaning up test data while preserving user accounts

    REQUIRES: Superuser privileges
    """
    # Check if user is superuser
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=403,
            detail="Access denied. This operation requires superuser privileges."
        )

    try:
        # Count all data before deletion
        total_counts = {}

        # Count organizations (will be preserved)
        orgs_result = await db.execute(select(Organization))
        organizations = orgs_result.scalars().all()
        total_counts["organizations_preserved"] = len(organizations)

        # Count users (will be preserved)
        users_result = await db.execute(select(User))
        all_users = users_result.scalars().all()
        total_counts["users_preserved"] = len(all_users)

        # Count all content to be deleted
        campaigns_result = await db.execute(select(Campaign))
        all_campaigns = campaigns_result.scalars().all()
        total_counts["campaigns"] = len(all_campaigns)

        quests_result = await db.execute(select(Quest))
        all_quests = quests_result.scalars().all()
        total_counts["quests"] = len(all_quests)

        badges_result = await db.execute(select(Badge))
        all_badges = badges_result.scalars().all()
        total_counts["badges"] = len(all_badges)

        rewards_result = await db.execute(select(Reward))
        all_rewards = rewards_result.scalars().all()
        total_counts["rewards"] = len(all_rewards)

        categories_result = await db.execute(select(Category))
        all_categories = categories_result.scalars().all()
        total_counts["categories"] = len(all_categories)

        roles_result = await db.execute(select(Role))
        all_roles = roles_result.scalars().all()
        # Only delete roles that belong to organizations (not system roles without org)
        regular_roles = [r for r in all_roles if not r.is_system and r.organization_id is not None]
        system_roles = [r for r in all_roles if r.is_system or r.organization_id is None]
        total_counts["roles_to_delete"] = len(regular_roles)
        total_counts["system_roles_preserved"] = len(system_roles)

        # Delete all content data while preserving organizations and users

        # 1. Delete all quests first
        for quest in all_quests:
            await db.delete(quest)

        # 2. Delete all campaigns
        for campaign in all_campaigns:
            await db.delete(campaign)

        # 3. Delete all badges and rewards
        for badge in all_badges:
            await db.delete(badge)

        for reward in all_rewards:
            await db.delete(reward)

        # 4. Delete all categories
        for category in all_categories:
            await db.delete(category)

        # 5. Delete all non-system roles
        for role in regular_roles:
            await db.delete(role)

        await db.commit()

        total_deleted = (
            total_counts["campaigns"] +
            total_counts["quests"] +
            total_counts["badges"] +
            total_counts["rewards"] +
            total_counts["categories"] +
            total_counts["roles_to_delete"]
        )

        return {
            "message": "🧹 GLOBAL DATA CLEAR COMPLETED 🧹",
            "warning": "ALL CONTENT DATA DELETED FROM ALL ORGANIZATIONS",
            "operation": "GLOBAL CONTENT RESET - Organizations and Users Preserved",
            "performed_by": current_user.get("email", "unknown"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "deleted_counts": {
                "campaigns": total_counts["campaigns"],
                "quests": total_counts["quests"],
                "badges": total_counts["badges"],
                "rewards": total_counts["rewards"],
                "categories": total_counts["categories"],
                "roles": total_counts["roles_to_delete"],
                "total_items_deleted": total_deleted
            },
            "preserved": {
                "organizations": total_counts["organizations_preserved"],
                "users": total_counts["users_preserved"],
                "system_roles": total_counts["system_roles_preserved"]
            },
            "next_steps": [
                "Organizations are ready for fresh content",
                "Users can log in normally",
                "Seed default categories for each organization",
                "Import or create new content data"
            ]
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Global data clear operation failed: {str(e)}"
        )
