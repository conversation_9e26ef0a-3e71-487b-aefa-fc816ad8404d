"""
Quest management endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, get_current_user_organization_id
from app.api.deps import require_permissions
from app.schemas.quest import Quest, QuestCreate, QuestUpdate
from app.models.quest import QuestStatus
from app.services.quest_service import QuestService

router = APIRouter()


@router.get("/", response_model=List[Quest])
async def list_quests(
    campaign_id: Optional[str] = Query(None, description="Filter by campaign ID"),
    status_filter: Optional[QuestStatus] = Query(None, description="Filter by quest status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:read"]))
):
    """List quests for the current organization"""

    if campaign_id:
        return await QuestService.get_quests_by_campaign(
            db=db,
            campaign_id=campaign_id,
            current_user_org_id=organization_id,
            skip=skip,
            limit=limit
        )
    else:
        return await QuestService.get_quests_by_organization(
            db=db,
            organization_id=organization_id,
            status_filter=status_filter,
            skip=skip,
            limit=limit
        )


@router.post("/", response_model=Quest, status_code=status.HTTP_201_CREATED)
async def create_quest(
    quest_create: QuestCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:create"]))
):
    """Create a new quest"""
    return await QuestService.create_quest(
        db=db,
        quest_create=quest_create,
        current_user_id=current_user["id"],
        current_user_org_id=organization_id
    )


@router.get("/{quest_id}", response_model=Quest)
async def get_quest(
    quest_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:read"]))
):
    """Get a specific quest by ID"""
    return await QuestService.get_quest_by_id(
        db=db,
        quest_id=quest_id,
        current_user_org_id=organization_id
    )


@router.put("/{quest_id}", response_model=Quest)
async def update_quest(
    quest_id: str,
    quest_update: QuestUpdate,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:update"]))
):
    """Update a quest"""
    return await QuestService.update_quest(
        db=db,
        quest_id=quest_id,
        quest_update=quest_update,
        current_user_org_id=organization_id
    )


@router.delete("/{quest_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_quest(
    quest_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:delete"]))
):
    """Delete a quest"""
    await QuestService.delete_quest(
        db=db,
        quest_id=quest_id,
        current_user_org_id=organization_id
    )


@router.post("/{quest_id}/activate", response_model=Quest)
async def activate_quest(
    quest_id: str,
    db: AsyncSession = Depends(get_db),
    organization_id: str = Depends(get_current_user_organization_id),
    _: dict = Depends(require_permissions(["quests:update"]))
):
    """Activate a quest"""
    return await QuestService.activate_quest(
        db=db,
        quest_id=quest_id,
        current_user_org_id=organization_id
    )
