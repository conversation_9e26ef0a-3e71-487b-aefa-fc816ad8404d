"""
API v1 router registration.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    organizations,
    campaigns,
    quests,
    badges,
    rewards,
    analytics,
    categories,
    seeding,
    roles,
    permissions,
    invitations
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(organizations.router, prefix="/organizations", tags=["organizations"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(quests.router, prefix="/quests", tags=["quests"])
api_router.include_router(badges.router, prefix="/badges", tags=["badges"])
api_router.include_router(rewards.router, prefix="/rewards", tags=["rewards"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(seeding.router, prefix="/seeding", tags=["seeding"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
api_router.include_router(invitations.router, prefix="/invitations", tags=["invitations"])
