"""
API dependencies for authentication, permissions, and common operations.
"""

from typing import List, Callable, Dict, Any
from fastapi import Depends, HTTPException, status

from app.core.security import get_current_active_user, get_current_user_organization_id


def require_permissions(required_permissions: List[str]) -> Callable:
    """
    Dependency factory that creates a permission checker.
    
    Args:
        required_permissions: List of permissions required to access the endpoint
        
    Returns:
        Dependency function that checks if user has required permissions
    """
    def permission_checker(current_user: Dict[str, Any] = Depends(get_current_active_user)):
        user_permissions = current_user.get("permissions", [])
        
        # Check if user has all required permissions
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return permission_checker


def require_role(required_role: str) -> Callable:
    """
    Dependency factory that creates a role checker.
    
    Args:
        required_role: Role required to access the endpoint
        
    Returns:
        Dependency function that checks if user has required role
    """
    def role_checker(current_user: Dict[str, Any] = Depends(get_current_active_user)):
        user_role = current_user.get("role")
        
        if user_role != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required role: {required_role}"
            )
        
        return current_user
    
    return role_checker


def require_admin(current_user: Dict[str, Any] = Depends(get_current_active_user)) -> Dict[str, Any]:
    """Dependency that requires admin role"""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


def get_current_organization(
    organization_id: str = Depends(get_current_user_organization_id)
) -> str:
    """Get the current user's organization ID"""
    return organization_id
