"""
Reward Pydantic schemas.
"""

from typing import Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, field_validator

from app.models.reward import RewardStatus


class RewardBase(BaseModel):
    """Base reward schema"""
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    points_cost: int = Field(ge=0)
    stock_quantity: Optional[int] = Field(None, ge=0)


class RewardCreate(RewardBase):
    """Reward creation schema"""
    category_id: Optional[str] = None


class RewardUpdate(BaseModel):
    """Reward update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    points_cost: Optional[int] = Field(None, ge=0)
    stock_quantity: Optional[int] = Field(None, ge=0)
    status: Optional[RewardStatus] = None
    category_id: Optional[str] = None


class Reward(RewardBase):
    """Reward response schema"""
    id: str
    organization_id: str
    category_id: Optional[str] = None
    status: RewardStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', 'category_id', 'created_by', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if v is None:
            return None
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
