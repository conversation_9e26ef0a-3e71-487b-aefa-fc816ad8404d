"""
User Pydantic schemas.
"""

from typing import Optional, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, EmailStr, field_validator


class UserBase(BaseModel):
    """Base user schema"""
    email: EmailStr
    first_name: str
    last_name: str
    is_active: bool = True


class UserCreate(UserBase):
    """User creation schema"""
    password: str
    organization_id: Optional[str] = None
    role_id: Optional[str] = None


class UserUpdate(BaseModel):
    """User update schema"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    role_id: Optional[str] = None


class UserInDB(UserBase):
    """User schema with password hash (internal use)"""
    id: Union[str, UUID]
    password_hash: str
    organization_id: Union[str, UUID]
    role_id: Optional[Union[str, UUID]] = None
    is_superuser: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', 'role_id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, value):
        """Convert UUID to string"""
        if value is None:
            return None
        return str(value)

    class Config:
        from_attributes = True


class User(UserBase):
    """User response schema (public)"""
    id: Union[str, UUID]
    organization_id: Union[str, UUID]
    role_id: Optional[Union[str, UUID]] = None
    is_superuser: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', 'role_id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, value):
        """Convert UUID to string"""
        if value is None:
            return None
        return str(value)

    class Config:
        from_attributes = True
