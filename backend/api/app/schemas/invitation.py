"""
Invitation Pydantic schemas.
"""

from typing import Optional, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, EmailStr, field_validator


class InvitationBase(BaseModel):
    """Base invitation schema"""
    email: EmailStr
    first_name: str
    last_name: str
    role_id: Optional[str] = None
    invitation_message: Optional[str] = None


class InvitationCreate(InvitationBase):
    """Invitation creation schema"""
    pass


class InvitationAccept(BaseModel):
    """Invitation acceptance schema"""
    token: str
    password: str
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v


class InvitationResponse(InvitationBase):
    """Invitation response schema"""
    id: Union[str, UUID]
    organization_id: Union[str, UUID]
    token: str
    invited_by_id: Union[str, UUID]
    is_accepted: bool
    is_expired: bool
    expires_at: datetime
    accepted_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    @field_validator('id', 'organization_id', 'role_id', 'invited_by_id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, value):
        """Convert UUID to string"""
        if value is None:
            return None
        return str(value)

    class Config:
        from_attributes = True


class InvitationWithDetails(InvitationResponse):
    """Invitation with additional details"""
    organization_name: Optional[str] = None
    role_name: Optional[str] = None
    invited_by_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class InvitationStatus(BaseModel):
    """Invitation status response"""
    valid: bool
    expired: bool
    accepted: bool
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    organization_name: Optional[str] = None
    role_name: Optional[str] = None
    expires_at: Optional[datetime] = None
