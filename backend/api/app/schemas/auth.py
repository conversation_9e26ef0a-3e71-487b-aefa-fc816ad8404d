"""
Authentication Pydantic schemas.
"""

from typing import Optional
from pydantic import BaseModel


class Token(BaseModel):
    """Token response schema"""
    access_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """Token data schema"""
    user_id: Optional[str] = None
    organization_id: Optional[str] = None


class UserLogin(BaseModel):
    """User login schema"""
    email: str
    password: str


class UserRegister(BaseModel):
    """User registration schema"""
    email: str
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
