"""
Badge Pydantic schemas.
"""

from typing import Optional, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, field_validator

from app.models.badge import BadgeTier, BadgeStatus


class CategoryInfo(BaseModel):
    """Category information for badge responses"""
    id: str
    name: str
    description: Optional[str] = None
    color: str = "#6b7280"
    icon: Optional[str] = None

    @field_validator('id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if v is None:
            return None
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True


class BadgeBase(BaseModel):
    """Base badge schema"""
    name: str
    description: Optional[str] = None
    icon: str = "Trophy"
    tier: BadgeTier = BadgeTier.BRONZE
    criteria: str
    points_reward: int = Field(ge=0)
    status: BadgeStatus = BadgeStatus.DRAFT


class BadgeCreate(BadgeBase):
    """Badge creation schema"""
    category_id: Optional[str] = None


class BadgeUpdate(BaseModel):
    """Badge update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    tier: Optional[BadgeTier] = None
    criteria: Optional[str] = None
    points_reward: Optional[int] = Field(None, ge=0)
    status: Optional[BadgeStatus] = None
    category_id: Optional[str] = None


class Badge(BadgeBase):
    """Badge response schema"""
    id: str
    organization_id: str
    category_id: Optional[str] = None
    category: Optional[CategoryInfo] = None
    times_earned: int = 0
    created_by: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', 'category_id', 'created_by', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if v is None:
            return None
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
