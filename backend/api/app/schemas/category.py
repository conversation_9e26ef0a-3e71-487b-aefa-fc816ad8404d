"""
Category Pydantic schemas.
"""

from typing import Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, field_validator


class CategoryBase(BaseModel):
    """Base category schema"""
    name: str
    description: Optional[str] = None
    color: str = "#6b7280"
    icon: Optional[str] = None


class CategoryCreate(CategoryBase):
    """Category creation schema"""
    organization_id: str


class CategoryUpdate(BaseModel):
    """Category update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None
    icon: Optional[str] = None


class Category(CategoryBase):
    """Category response schema"""
    id: str
    organization_id: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
