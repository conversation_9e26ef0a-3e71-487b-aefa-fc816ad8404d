"""
Campaign Pydantic schemas.
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, field_validator

from app.models.campaign import CampaignStatus


class CampaignBase(BaseModel):
    """Base campaign schema"""
    name: str
    description: Optional[str] = None
    banner_url: Optional[str] = None
    start_date: Optional[Union[str, datetime]] = None
    end_date: Optional[Union[str, datetime]] = None
    target_audience: Optional[Dict[str, Any]] = None

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def parse_date(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            # Handle date strings in YYYY-MM-DD format
            try:
                # Convert date string to datetime at start of day (00:00:00) UTC
                parsed_date = datetime.strptime(v, '%Y-%m-%d')
                # Return as datetime object for PostgreSQL timestamptz
                result = parsed_date.replace(hour=0, minute=0, second=0, microsecond=0)
                print(f"DEBUG: Converted '{v}' to datetime: {result} (type: {type(result)})")
                return result
            except ValueError:
                # Try full datetime format
                try:
                    result = datetime.fromisoformat(v.replace('Z', '+00:00'))
                    print(f"DEBUG: Converted ISO '{v}' to datetime: {result} (type: {type(result)})")
                    return result
                except ValueError:
                    print(f"DEBUG: Could not parse date '{v}', returning as-is")
                    return v
        print(f"DEBUG: Date field already correct type: {v} (type: {type(v)})")
        return v


class CampaignCreate(CampaignBase):
    """Campaign creation schema"""
    organization_id: str


class CampaignUpdate(BaseModel):
    """Campaign update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    banner_url: Optional[str] = None
    start_date: Optional[Union[str, datetime]] = None
    end_date: Optional[Union[str, datetime]] = None
    status: Optional[CampaignStatus] = None
    target_audience: Optional[Dict[str, Any]] = None

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def parse_date(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            # Handle date strings in YYYY-MM-DD format
            try:
                # Convert date string to datetime at start of day (00:00:00) UTC
                parsed_date = datetime.strptime(v, '%Y-%m-%d')
                # Return as datetime object for PostgreSQL timestamptz
                return parsed_date.replace(hour=0, minute=0, second=0, microsecond=0)
            except ValueError:
                # Try full datetime format
                try:
                    return datetime.fromisoformat(v.replace('Z', '+00:00'))
                except ValueError:
                    return v
        return v


class Campaign(CampaignBase):
    """Campaign response schema"""
    id: str
    organization_id: str
    status: CampaignStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', 'created_by', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
