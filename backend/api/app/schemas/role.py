"""
Role Pydantic schemas.
"""

from typing import Optional, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, field_validator


class RoleBase(BaseModel):
    """Base role schema"""
    name: str
    description: Optional[str] = None
    level: int = 1
    color: str = "#6b7280"
    permissions: List[str] = []
    is_system: bool = False


class RoleCreate(RoleBase):
    """Role creation schema"""
    organization_id: str


class RoleUpdate(BaseModel):
    """Role update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    level: Optional[int] = None
    color: Optional[str] = None
    permissions: Optional[List[str]] = None
    is_system: Optional[bool] = None


class RoleClone(BaseModel):
    """Role clone schema"""
    name: str
    description: Optional[str] = None


class RoleWithUsers(BaseModel):
    """Role with user count schema"""
    id: str
    name: str
    description: Optional[str] = None
    level: int
    color: str
    permissions: List[str]
    is_system: bool
    organization_id: str
    user_count: int
    created_at: datetime
    updated_at: datetime


class RolePermissionUpdate(BaseModel):
    """Role permission update schema"""
    permissions: List[str]


class RoleValidation(BaseModel):
    """Role validation schema"""
    role_id: str
    role_name: str
    total_permissions: int
    valid_permissions: int
    invalid_permissions: int
    conflicts: List[dict]
    validation_details: dict


class Role(RoleBase):
    """Role response schema"""
    id: str
    organization_id: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'organization_id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
