"""
Quest Pydantic schemas.
"""

from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, field_validator

from app.models.quest import QuestStatus, QuestValidationType, QuestFrequency


class QuestBase(BaseModel):
    """Base quest schema"""
    title: str
    description: Optional[str] = None
    points_reward: int = Field(ge=0)
    frequency: QuestFrequency = QuestFrequency.ONE_TIME
    validation_type: QuestValidationType = QuestValidationType.AUTOMATIC
    validation_criteria: Optional[Dict[str, Any]] = None


class QuestCreate(QuestBase):
    """Quest creation schema"""
    campaign_id: str
    category_id: Optional[str] = None


class QuestUpdate(BaseModel):
    """Quest update schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    points_reward: Optional[int] = Field(None, ge=0)
    frequency: Optional[QuestFrequency] = None
    validation_type: Optional[QuestValidationType] = None
    validation_criteria: Optional[Dict[str, Any]] = None
    status: Optional[QuestStatus] = None
    category_id: Optional[str] = None


class Quest(QuestBase):
    """Quest response schema"""
    id: str
    campaign_id: str
    category_id: Optional[str] = None
    status: QuestStatus
    created_by: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', 'campaign_id', 'category_id', 'created_by', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if v is None:
            return None
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
