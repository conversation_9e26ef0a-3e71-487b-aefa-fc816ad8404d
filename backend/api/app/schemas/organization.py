"""
Organization Pydantic schemas.
"""

from typing import Optional, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, field_validator


class OrganizationBase(BaseModel):
    """Base organization schema"""
    name: str
    description: Optional[str] = None
    website: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    primary_color: str = "#3b82f6"
    subscription_plan: str = "free"


class OrganizationCreate(OrganizationBase):
    """Organization creation schema"""
    slug: str


class OrganizationUpdate(BaseModel):
    """Organization update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    primary_color: Optional[str] = None
    subscription_plan: Optional[str] = None


class Organization(OrganizationBase):
    """Organization response schema"""
    id: str
    slug: str
    created_at: datetime
    updated_at: datetime

    @field_validator('id', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v):
        """Convert UUID objects to strings"""
        if isinstance(v, UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
