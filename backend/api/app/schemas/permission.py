"""
Permission Pydantic schemas.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, field_validator

from app.core.permissions import PermissionResource, PermissionAction, PermissionScope


class PermissionBase(BaseModel):
    """Base permission schema"""
    id: str
    name: str
    description: str
    resource: PermissionResource
    action: PermissionAction
    scope: PermissionScope
    category: str


class Permission(PermissionBase):
    """Permission schema"""
    pass


class PermissionCheck(BaseModel):
    """Permission check request schema"""
    permissions: List[str]


class PermissionCheckResult(BaseModel):
    """Permission check result schema"""
    results: Dict[str, bool]


class PermissionTemplate(BaseModel):
    """Permission template schema"""
    id: str
    name: str
    description: str
    permissions: List[str]
    level: int
    color: str
    is_system: bool = False
    category: Optional[str] = None


class PermissionTemplateCreate(BaseModel):
    """Permission template creation schema"""
    name: str
    description: str
    permissions: List[str]
    level: int = 1
    color: str = "#6b7280"
    category: Optional[str] = None


class PermissionMatrix(BaseModel):
    """Permission matrix schema"""
    resources: Dict[str, List[str]]


class PermissionHierarchy(BaseModel):
    """Permission hierarchy schema"""
    hierarchy: Dict[str, List[str]]


class UserPermissions(BaseModel):
    """User permissions schema"""
    user_id: str
    permissions: List[str]
    effective_permissions: List[str]
    role_id: Optional[str] = None
    role_name: Optional[str] = None


class PermissionValidation(BaseModel):
    """Permission validation schema"""
    permission_id: str
    valid: bool
    details: Optional[Dict[str, Any]] = None


class PermissionCategory(BaseModel):
    """Permission category schema"""
    name: str
    permissions: List[str]
    description: Optional[str] = None


class PermissionScope(BaseModel):
    """Permission scope schema"""
    scope: str
    description: str
    level: int


class PermissionAudit(BaseModel):
    """Permission audit log schema"""
    id: str
    user_id: str
    permission_id: str
    action: str  # granted, revoked, checked
    resource_id: Optional[str] = None
    result: bool
    timestamp: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class PermissionUsage(BaseModel):
    """Permission usage statistics schema"""
    permission_id: str
    usage_count: int
    last_used: Optional[datetime] = None
    users_with_permission: int
    roles_with_permission: int


class PermissionConflict(BaseModel):
    """Permission conflict detection schema"""
    permission_id: str
    conflicting_permissions: List[str]
    severity: str  # low, medium, high
    description: str
    recommendation: Optional[str] = None


class PermissionDependency(BaseModel):
    """Permission dependency schema"""
    permission_id: str
    depends_on: List[str]
    implied_by: List[str]
    conflicts_with: List[str]


class BulkPermissionOperation(BaseModel):
    """Bulk permission operation schema"""
    operation: str  # grant, revoke, check
    user_ids: List[str]
    permission_ids: List[str]
    role_id: Optional[str] = None


class BulkPermissionResult(BaseModel):
    """Bulk permission operation result schema"""
    operation: str
    success_count: int
    failure_count: int
    results: List[Dict[str, Any]]
    errors: List[str]
