"""
FastAPI Dependencies

Common dependencies used across multiple routers.
"""

from fastapi import Depends, HTTPException, status
from typing import List, Callable
from .auth import get_current_user

def require_permissions(required_permissions: List[str]) -> Callable:
    """
    Dependency factory that creates a permission checker.
    
    Args:
        required_permissions: List of permissions required to access the endpoint
        
    Returns:
        Dependency function that checks if user has required permissions
    """
    def permission_checker(current_user = Depends(get_current_user)):
        user_permissions = current_user.get("permissions", [])
        
        # Check if user has all required permissions
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return None
    
    return permission_checker

def require_role(required_role: str) -> Callable:
    """
    Dependency factory that creates a role checker.
    
    Args:
        required_role: Role required to access the endpoint
        
    Returns:
        Dependency function that checks if user has required role
    """
    def role_checker(current_user = Depends(get_current_user)):
        user_role = current_user.get("role")
        
        if user_role != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required role: {required_role}"
            )
        
        return None
    
    return role_checker

def require_admin(current_user = Depends(get_current_user)):
    """Dependency that requires admin role"""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return None
