"""
CRUD operations for Reward model.
"""

from typing import List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.reward import Reward, RewardStatus
from app.schemas.reward import RewardCreate, RewardUpdate


class CRUDReward(CRUDBase[Reward, RewardCreate, RewardUpdate]):
    """CRUD operations for Reward"""

    async def get_by_organization(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reward]:
        """Get rewards by organization"""
        result = await db.execute(
            select(Reward)
            .where(Reward.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_active_rewards(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reward]:
        """Get active rewards by organization"""
        result = await db.execute(
            select(Reward)
            .where(Reward.organization_id == organization_id)
            .where(Reward.status == RewardStatus.ACTIVE)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_points_range(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        min_points: int,
        max_points: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reward]:
        """Get rewards by points cost range"""
        result = await db.execute(
            select(Reward)
            .where(Reward.organization_id == organization_id)
            .where(Reward.points_cost >= min_points)
            .where(Reward.points_cost <= max_points)
            .where(Reward.status == RewardStatus.ACTIVE)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


reward_crud = CRUDReward(Reward)
