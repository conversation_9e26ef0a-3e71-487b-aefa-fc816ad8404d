"""
CRUD operations for Badge model.
"""

from typing import List, Optional
from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.badge import Badge, BadgeTier, BadgeStatus
from app.schemas.badge import BadgeC<PERSON>, BadgeUpdate


class CRUDBadge(CRUDBase[Badge, BadgeCreate, BadgeUpdate]):
    """CRUD operations for Badge"""

    async def get(self, db: AsyncSession, id: str) -> Optional[Badge]:
        """Get a single badge by ID with category information"""
        result = await db.execute(
            select(Badge)
            .options(selectinload(Badge.category))
            .where(Badge.id == id)
        )
        return result.scalar_one_or_none()

    async def get_by_organization(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Badge]:
        """Get badges by organization"""
        result = await db.execute(
            select(Badge)
            .options(selectinload(Badge.category))
            .where(Badge.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_tier(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        tier: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Badge]:
        """Get badges by tier within organization"""
        result = await db.execute(
            select(Badge)
            .options(selectinload(Badge.category))
            .where(Badge.organization_id == organization_id)
            .where(Badge.tier == tier)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_organization_with_filters(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        tier_filter: Optional[BadgeTier] = None,
        status_filter: Optional[BadgeStatus] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Badge]:
        """Get badges by organization with optional filters"""
        query = select(Badge).options(selectinload(Badge.category)).where(Badge.organization_id == organization_id)

        if tier_filter:
            query = query.where(Badge.tier == tier_filter)

        if status_filter:
            query = query.where(Badge.status == status_filter)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Badge.name.ilike(search_term),
                    Badge.description.ilike(search_term)
                )
            )

        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()


badge_crud = CRUDBadge(Badge)
