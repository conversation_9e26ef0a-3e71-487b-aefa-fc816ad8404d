"""
Role CRUD operations.
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.crud.base import CRUDBase
from app.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate


class CRUDRole(CRUDBase[Role, RoleCreate, RoleUpdate]):
    """CRUD operations for Role"""
    
    async def get_by_organization(
        self,
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Role]:
        """Get roles by organization"""
        stmt = (
            select(self.model)
            .where(self.model.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.level.desc(), self.model.name)
        )
        result = await db.execute(stmt)
        return result.scalars().all()

    async def get_by_name_and_organization(
        self,
        db: AsyncSession,
        name: str,
        organization_id: str
    ) -> Optional[Role]:
        """Get role by name and organization"""
        stmt = (
            select(self.model)
            .where(
                self.model.name == name,
                self.model.organization_id == organization_id
            )
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def get_system_roles(
        self,
        db: AsyncSession,
        organization_id: str
    ) -> List[Role]:
        """Get system roles for organization"""
        stmt = (
            select(self.model)
            .where(
                self.model.organization_id == organization_id,
                self.model.is_system == True
            )
            .order_by(self.model.level.desc())
        )
        result = await db.execute(stmt)
        return result.scalars().all()

    async def get_custom_roles(
        self,
        db: AsyncSession,
        organization_id: str
    ) -> List[Role]:
        """Get custom (non-system) roles for organization"""
        stmt = (
            select(self.model)
            .where(
                self.model.organization_id == organization_id,
                self.model.is_system == False
            )
            .order_by(self.model.level.desc(), self.model.name)
        )
        result = await db.execute(stmt)
        return result.scalars().all()
    
    async def get_by_name_and_organization(
        self,
        db: AsyncSession,
        name: str,
        organization_id: str
    ) -> Optional[Role]:
        """Get role by name and organization"""
        stmt = select(self.model).where(
            self.model.name == name,
            self.model.organization_id == organization_id
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()


role_crud = CRUDRole(Role)
