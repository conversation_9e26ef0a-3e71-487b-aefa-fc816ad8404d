"""
CRUD operations for Quest model.
"""

from typing import List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.quest import Quest
from app.schemas.quest import Quest<PERSON><PERSON>, QuestUpdate


class CRUDQuest(CRUDBase[Quest, QuestCreate, QuestUpdate]):
    """CRUD operations for Quest"""

    async def get_by_campaign(
        self,
        db: AsyncSession,
        *,
        campaign_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Quest]:
        """Get quests by campaign"""
        result = await db.execute(
            select(Quest)
            .where(Quest.campaign_id == campaign_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_organization(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Quest]:
        """Get quests by organization (through campaign relationship)"""
        result = await db.execute(
            select(Quest)
            .join(Quest.campaign)
            .where(Quest.campaign.has(organization_id=organization_id))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


quest_crud = CRUDQuest(Quest)
