"""
CRUD operations for Organization model.
"""

from typing import Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate, OrganizationUpdate


class CRUDOrganization(CRUDBase[Organization, OrganizationCreate, OrganizationUpdate]):
    """CRUD operations for Organization"""
    
    async def get_by_slug(self, db: AsyncSession, *, slug: str) -> Optional[Organization]:
        """Get organization by slug"""
        result = await db.execute(select(Organization).where(Organization.slug == slug))
        return result.scalar_one_or_none()
    
    async def get_by_name(self, db: AsyncSession, *, name: str) -> Optional[Organization]:
        """Get organization by name"""
        result = await db.execute(select(Organization).where(Organization.name == name))
        return result.scalar_one_or_none()


organization_crud = CRUDOrganization(Organization)
