"""
Category CRUD operations.
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.crud.base import CRUDBase
from app.models.category import Category
from app.schemas.category import CategoryCreate, CategoryUpdate


class CRUDCategory(CRUDBase[Category, CategoryCreate, CategoryUpdate]):
    """CRUD operations for Category"""
    
    async def get_by_organization(
        self,
        db: AsyncSession,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Category]:
        """Get categories by organization"""
        stmt = (
            select(self.model)
            .where(self.model.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await db.execute(stmt)
        return result.scalars().all()
    
    async def get_by_name_and_organization(
        self,
        db: AsyncSession,
        name: str,
        organization_id: str
    ) -> Optional[Category]:
        """Get category by name and organization"""
        stmt = select(self.model).where(
            self.model.name == name,
            self.model.organization_id == organization_id
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()


category_crud = CRUDCategory(Category)
