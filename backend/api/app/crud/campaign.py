"""
CRUD operations for Campaign model.
"""

from typing import List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.campaign import Campaign
from app.schemas.campaign import CampaignCreate, CampaignUpdate


class CRUDCampaign(CRUDBase[Campaign, CampaignCreate, CampaignUpdate]):
    """CRUD operations for Campaign"""
    
    async def get_by_organization(
        self, 
        db: AsyncSession, 
        *, 
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Campaign]:
        """Get campaigns by organization"""
        result = await db.execute(
            select(Campaign)
            .where(Campaign.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


campaign_crud = CRUDCampaign(Campaign)
