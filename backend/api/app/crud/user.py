"""
CRUD operations for User model.
"""

from typing import Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """CRUD operations for User"""
    
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """Get user by email"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    
    async def create(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        """Create user with hashed password"""
        create_data = obj_in.dict()
        password = create_data.pop("password")
        create_data["password_hash"] = get_password_hash(password)
        
        db_obj = User(**create_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def authenticate(self, db: AsyncSession, *, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        user = await self.get_by_email(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user
    
    async def is_active(self, user: User) -> bool:
        """Check if user is active"""
        return user.is_active
    
    async def is_superuser(self, user: User) -> bool:
        """Check if user is superuser"""
        return user.is_superuser
    
    async def get_by_organization(
        self,
        db: AsyncSession,
        *,
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> list[User]:
        """Get users by organization"""
        result = await db.execute(
            select(User)
            .where(User.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_role(
        self,
        db: AsyncSession,
        *,
        role_id: str
    ) -> list[User]:
        """Get users by role"""
        result = await db.execute(
            select(User).where(User.role_id == role_id)
        )
        return result.scalars().all()

    async def assign_role(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        role_id: str,
        organization_id: str
    ) -> User:
        """Assign role to user"""
        user = await self.get(db, id=user_id)
        if not user:
            raise ValueError("User not found")

        if str(user.organization_id) != organization_id:
            raise ValueError("User not in current organization")

        user.role_id = role_id
        await db.commit()
        await db.refresh(user)
        return user


user_crud = CRUDUser(User)
