"""
CRUD operations for invitations.
"""

from typing import List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.invitation import Invitation
from app.models.organization import Organization
from app.models.role import Role
from app.models.user import User
from app.schemas.invitation import InvitationCreate, InvitationResponse


class CRUDInvitation(CRUDBase[Invitation, InvitationCreate, InvitationResponse]):
    """CRUD operations for Invitation"""
    
    async def get_by_token(self, db: AsyncSession, *, token: str) -> Optional[Invitation]:
        """Get invitation by token"""
        result = await db.execute(
            select(Invitation)
            .options(
                selectinload(Invitation.organization),
                selectinload(Invitation.role),
                selectinload(Invitation.invited_by)
            )
            .where(Invitation.token == token)
        )
        return result.scalar_one_or_none()
    
    async def get_by_email_and_organization(
        self, 
        db: AsyncSession, 
        *, 
        email: str, 
        organization_id: str
    ) -> Optional[Invitation]:
        """Get pending invitation by email and organization"""
        result = await db.execute(
            select(Invitation)
            .where(
                and_(
                    Invitation.email == email,
                    Invitation.organization_id == organization_id,
                    Invitation.is_accepted == False,
                    Invitation.is_expired == False
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_pending_invitations(
        self, 
        db: AsyncSession, 
        *, 
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Invitation]:
        """Get pending invitations for an organization"""
        result = await db.execute(
            select(Invitation)
            .options(
                selectinload(Invitation.organization),
                selectinload(Invitation.role),
                selectinload(Invitation.invited_by)
            )
            .where(
                and_(
                    Invitation.organization_id == organization_id,
                    Invitation.is_accepted == False,
                    Invitation.is_expired == False
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(Invitation.created_at.desc())
        )
        return result.scalars().all()
    
    async def get_organization_invitations(
        self, 
        db: AsyncSession, 
        *, 
        organization_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Invitation]:
        """Get all invitations for an organization"""
        result = await db.execute(
            select(Invitation)
            .options(
                selectinload(Invitation.organization),
                selectinload(Invitation.role),
                selectinload(Invitation.invited_by)
            )
            .where(Invitation.organization_id == organization_id)
            .offset(skip)
            .limit(limit)
            .order_by(Invitation.created_at.desc())
        )
        return result.scalars().all()
    
    async def create_invitation(
        self,
        db: AsyncSession,
        *,
        invitation_data: InvitationCreate,
        organization_id: str,
        invited_by_id: str
    ) -> Invitation:
        """Create a new invitation"""
        invitation = Invitation(
            email=invitation_data.email,
            first_name=invitation_data.first_name,
            last_name=invitation_data.last_name,
            organization_id=organization_id,
            role_id=invitation_data.role_id,
            invited_by_id=invited_by_id,
            invitation_message=invitation_data.invitation_message
        )
        
        db.add(invitation)
        await db.commit()
        await db.refresh(invitation)
        
        # Load relationships
        await db.refresh(invitation, ["organization", "role", "invited_by"])
        
        return invitation
    
    async def accept_invitation(
        self,
        db: AsyncSession,
        *,
        invitation: Invitation
    ) -> Invitation:
        """Accept an invitation"""
        invitation.accept()
        await db.commit()
        await db.refresh(invitation)
        return invitation
    
    async def expire_invitation(
        self,
        db: AsyncSession,
        *,
        invitation: Invitation
    ) -> Invitation:
        """Expire an invitation"""
        invitation.expire()
        await db.commit()
        await db.refresh(invitation)
        return invitation
    
    async def expire_old_invitations(self, db: AsyncSession) -> int:
        """Expire all old invitations"""
        result = await db.execute(
            select(Invitation)
            .where(
                and_(
                    Invitation.expires_at < datetime.utcnow(),
                    Invitation.is_expired == False,
                    Invitation.is_accepted == False
                )
            )
        )
        invitations = result.scalars().all()
        
        count = 0
        for invitation in invitations:
            invitation.expire()
            count += 1
        
        if count > 0:
            await db.commit()
        
        return count
    
    async def delete_invitation(
        self,
        db: AsyncSession,
        *,
        invitation_id: str
    ) -> bool:
        """Delete an invitation"""
        result = await db.execute(
            select(Invitation).where(Invitation.id == invitation_id)
        )
        invitation = result.scalar_one_or_none()
        
        if invitation:
            await db.delete(invitation)
            await db.commit()
            return True
        
        return False


# Create instance
invitation_crud = CRUDInvitation(Invitation)
