# Rewards Platform API

A modern FastAPI backend for customer rewards and gamification platform, built with best practices and production-ready architecture.

## ✨ Features

- 🚀 **FastAPI** with async/await support
- 🔐 **JWT Authentication** with role-based access control
- 🗄️ **SQLAlchemy 2.0+** with async PostgreSQL support
- 🏗️ **Service Layer Architecture** with proper separation of concerns
- 📊 **Comprehensive Analytics** with business intelligence
- 🔄 **Database Migrations** with Alembic
- 📚 **Auto-generated API Documentation** with OpenAPI
- 🧪 **Comprehensive Testing** suite
- 🐳 **Docker Support** for containerized deployment

## 🏗️ Project Structure

```
app/
├── __init__.py
├── main.py                     # FastAPI app initialization
├── core/                       # Core configuration and utilities
│   ├── __init__.py
│   ├── config.py              # Settings and configuration
│   ├── security.py            # Authentication and security
│   └── database.py            # Database configuration
├── api/                        # API route definitions
│   ├── __init__.py
│   ├── deps.py                # Dependencies
│   └── v1/                    # API version 1
│       ├── __init__.py
│       ├── api.py             # Router registration
│       └── endpoints/         # Individual route files
│           ├── __init__.py
│           ├── auth.py
│           ├── users.py
│           ├── organizations.py
│           ├── campaigns.py
│           ├── quests.py
│           ├── badges.py
│           ├── rewards.py
│           ├── analytics.py
│           ├── categories.py
│           └── seeding.py
├── models/                     # SQLAlchemy models
│   ├── __init__.py
│   ├── base.py                # Base model with common fields
│   ├── user.py
│   ├── organization.py
│   ├── role.py
│   ├── category.py
│   ├── campaign.py
│   ├── quest.py
│   ├── badge.py
│   └── reward.py
├── schemas/                    # Pydantic models
│   ├── __init__.py
│   ├── auth.py
│   ├── user.py
│   ├── organization.py
│   ├── campaign.py
│   ├── quest.py
│   ├── badge.py
│   └── reward.py
├── crud/                       # Database operations
│   ├── __init__.py
│   ├── base.py                # Base CRUD class
│   ├── user.py
│   ├── organization.py
│   ├── campaign.py
│   ├── quest.py
│   ├── badge.py
│   └── reward.py
└── services/                   # Business logic
    ├── __init__.py
    ├── auth_service.py
    ├── user_service.py
    ├── organization_service.py
    └── campaign_service.py
```

## 🔧 Key Improvements

### 1. **Proper FastAPI App Structure**

- ✅ Complete FastAPI app initialization in `main.py`
- ✅ CORS middleware configuration
- ✅ Router registration with proper prefixes
- ✅ Application lifespan management

### 2. **Configuration Management**

- ✅ Centralized settings in `app/core/config.py`
- ✅ Environment variable handling
- ✅ Type-safe configuration with Pydantic

### 3. **Database Layer**

- ✅ Modern SQLAlchemy 2.0+ with async support
- ✅ Proper connection pooling and session management
- ✅ Base model with common fields (id, created_at, updated_at)
- ✅ Alembic integration for migrations

### 4. **Security & Authentication**

- ✅ JWT-based authentication
- ✅ Password hashing with bcrypt
- ✅ Proper dependency injection for auth
- ✅ Permission-based access control

### 5. **Service Layer Pattern**

- ✅ Business logic separated from route handlers
- ✅ Reusable service classes
- ✅ Proper error handling and validation
- ✅ Testable business logic

### 6. **CRUD Layer**

- ✅ Generic base CRUD class
- ✅ Type-safe operations
- ✅ Consistent database patterns
- ✅ Async/await throughout

### 7. **API Structure**

- ✅ Versioned API endpoints (`/api/v1/`)
- ✅ Proper HTTP status codes
- ✅ Request/response validation
- ✅ Automatic OpenAPI documentation

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** (recommended)
- **PostgreSQL** database (or Supabase)
- **uv** package manager ([install here](https://docs.astral.sh/uv/getting-started/installation/))

### 1. Install uv (if not already installed)

```bash
# macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Or with pip
pip install uv
```

### 2. Clone and Setup Project

```bash
# Navigate to the API directory
cd backend/api

# Create virtual environment and install dependencies
uv venv --python 3.12.4
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -r requirements.txt
```

### 3. Environment Configuration

Create a `.env` file in the `backend/api` directory:

```bash
# Copy the example environment file
cp .env.example .env  # Create this file with the content below
```

**`.env` file content:**

```env
# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/rewards_platform
DATABASE_ECHO=false

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Supabase (optional, if using Supabase features)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 4. Database Setup

```bash
# Create initial migration
uv run alembic revision --autogenerate -m "Initial migration"

# Apply migrations
uv run alembic upgrade head
```

### 5. Start the Development Server

```bash
# Start with auto-reload
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or with more verbose logging
uv run uvicorn main:app --reload --log-level debug
```

### 6. Verify Installation

```bash
# Run comprehensive tests
uv run python test_main.py

# Check API health
curl http://localhost:8000/

# Access interactive API documentation
open http://localhost:8000/api/v1/docs
```

## 🔧 Development Commands

### Using uv for Development

```bash
# Install new dependency
uv add fastapi

# Install development dependency
uv add --dev pytest

# Update all dependencies
uv pip install -r requirements.txt --upgrade

# Run specific commands in the virtual environment
uv run python -m pytest
uv run alembic upgrade head
uv run uvicorn main:app --reload
```

### Database Operations

```bash
# Create new migration
uv run alembic revision --autogenerate -m "Add new table"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1

# Check migration status
uv run alembic current

# View migration history
uv run alembic history
```

### Data Seeding & Clearing

```bash
# Seed sample data (requires categories to be seeded first)
curl -X POST "http://localhost:8000/api/v1/seeding/seed-sample-data" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Seed default categories
curl -X POST "http://localhost:8000/api/v1/categories/seed-defaults" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get data summary
curl -X GET "http://localhost:8000/api/v1/seeding/data-summary" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Clear specific data types (see Clear Data API section below)
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-badges" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Testing

```bash
# Run basic structure tests
uv run python test_main.py

# Run with pytest (when available)
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html
```

## 📊 Database Models

### Core Models

- **Organization**: Multi-tenant organization structure
- **User**: User accounts with role-based permissions
- **Role**: RBAC roles with permissions
- **Category**: Organization for quests, badges, rewards

### Business Models

- **Campaign**: Marketing campaigns with quests
- **Quest**: Tasks users can complete for rewards
- **Badge**: Achievement badges with criteria
- **Reward**: Redeemable rewards from points

## 🗑️ Clear Data API Endpoints

The seeding API provides comprehensive data clearing endpoints for development and testing. All endpoints require authentication and are scoped to the current user's organization.

### Available Clear Endpoints

All endpoints are under `/api/v1/seeding/` and use the `DELETE` method:

#### 1. **`DELETE /clear-all-data`** ⚠️ **MOST DESTRUCTIVE**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-all-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Complete organization reset
- **Deletes**: Campaigns, quests, badges, rewards, AND categories
- **Warning**: Irreversible - deletes EVERYTHING
- **Use case**: Fresh start for organization

#### 2. **`DELETE /clear-sample-data`** 🔄 **RECOMMENDED FOR TESTING**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-sample-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Clear content while preserving structure
- **Deletes**: Campaigns, quests, badges, rewards
- **Keeps**: Categories (for easy re-seeding)
- **Use case**: Reset content data for testing

#### 3. **`DELETE /clear-categories`** ⚠️ **CASCADING DELETE**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-categories" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Category restructure
- **Deletes**: Categories + all dependent data (campaigns, quests, badges, rewards)
- **Warning**: Cascades to delete all related content
- **Use case**: Complete category system redesign

#### 4. **`DELETE /clear-badges`** 🏆 **BADGES ONLY**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-badges" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Reset badge system
- **Deletes**: All badges for the organization
- **Use case**: Badge system redesign

#### 5. **`DELETE /clear-rewards`** 🎁 **REWARDS ONLY**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-rewards" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Reset reward catalog
- **Deletes**: All rewards for the organization
- **Use case**: Reward system update

#### 6. **`DELETE /clear-quests`** 📋 **QUESTS ONLY**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-quests" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Reset quest system
- **Deletes**: All quests for the organization
- **Use case**: Quest redesign

#### 7. **`DELETE /clear-campaigns`** 📢 **CAMPAIGNS + QUESTS**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-campaigns" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

- **Purpose**: Reset campaign structure
- **Deletes**: All campaigns and their associated quests
- **Warning**: Also deletes quests belonging to campaigns
- **Use case**: Campaign strategy overhaul

### Response Format

All clear endpoints return detailed information about what was deleted:

```json
{
  "message": "Successfully deleted X items",
  "deleted": {
    "campaigns": 5,
    "quests": 12,
    "badges": 8,
    "rewards": 7,
    "categories": 6,
    "total_items": 38
  }
}
```

### Safety Features

- ✅ **Organization Scoped**: Users can only delete data from their own organization
- ✅ **Authentication Required**: All endpoints require valid JWT token
- ✅ **Foreign Key Respect**: Deletions follow proper order to prevent constraint violations
- ✅ **Detailed Responses**: Clear feedback on what was deleted
- ✅ **Error Handling**: Proper rollback on failures

### Common Workflows

#### Development Reset

```bash
# 1. Clear content but keep categories
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-sample-data" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Re-seed with fresh data
curl -X POST "http://localhost:8000/api/v1/seeding/seed-sample-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Complete Fresh Start

```bash
# 1. Nuclear option - clear everything
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-all-data" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Seed categories
curl -X POST "http://localhost:8000/api/v1/categories/seed-defaults" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. Seed sample data
curl -X POST "http://localhost:8000/api/v1/seeding/seed-sample-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Selective Clearing

```bash
# Clear just badges and rewards, keep campaigns/quests
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-badges" \
  -H "Authorization: Bearer YOUR_TOKEN"

curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-rewards" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Data Summary Endpoint

Before clearing data, check what exists:

```bash
curl -X GET "http://localhost:8000/api/v1/seeding/data-summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Returns:

```json
{
  "organization_id": "uuid-here",
  "data_summary": {
    "campaigns": 5,
    "quests": 12,
    "badges": 8,
    "rewards": 7,
    "categories": 6
  },
  "seeding_available": true
}
```

## 🚨 Super Admin Clear Endpoints

**⚠️ WARNING: These endpoints are EXTREMELY DESTRUCTIVE and require superuser privileges!**

### Nuclear Clear All Data

#### **`DELETE /nuclear-clear-all`** 🚨 **NUCLEAR OPTION**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/nuclear-clear-all" \
  -H "Authorization: Bearer SUPERUSER_TOKEN"
```

- **Purpose**: Complete database reset - deletes EVERYTHING
- **Deletes**: ALL organizations, users (except superusers), campaigns, quests, badges, rewards, categories, roles (except system roles)
- **Preserves**: Superusers and system roles only
- **Requirements**: Superuser privileges
- **Use case**: Complete development environment reset

#### **`DELETE /clear-all-organizations-data`** 🧹 **GLOBAL CONTENT CLEAR**

```bash
curl -X DELETE "http://localhost:8000/api/v1/seeding/clear-all-organizations-data" \
  -H "Authorization: Bearer SUPERUSER_TOKEN"
```

- **Purpose**: Clear all content while preserving organizational structure
- **Deletes**: ALL campaigns, quests, badges, rewards, categories, non-system roles from ALL organizations
- **Preserves**: Organizations, users, system roles
- **Requirements**: Superuser privileges
- **Use case**: Content reset while keeping user accounts and org structure

### Super Admin Response Format

```json
{
  "message": "🚨 NUCLEAR CLEAR COMPLETED 🚨",
  "warning": "ALL DATA HAS BEEN DELETED FROM ALL ORGANIZATIONS",
  "operation": "IRREVERSIBLE DATABASE RESET",
  "performed_by": "<EMAIL>",
  "timestamp": "2024-12-15T10:30:00Z",
  "deleted_counts": {
    "organizations": 3,
    "users": 15,
    "campaigns": 25,
    "quests": 75,
    "badges": 40,
    "rewards": 30,
    "categories": 21,
    "roles": 12,
    "total_items_deleted": 221
  },
  "preserved": {
    "superusers": 2,
    "system_roles": 3
  },
  "database_state": "CLEAN - Ready for fresh setup"
}
```

### Safety Features for Super Admin Endpoints

- ✅ **Superuser Only**: Requires `is_superuser: true` in JWT token
- ✅ **Detailed Logging**: Complete audit trail of what was deleted
- ✅ **Preservation Logic**: Keeps essential system data (superusers, system roles)
- ✅ **Foreign Key Respect**: Proper deletion order to prevent constraint violations
- ✅ **Comprehensive Reporting**: Detailed breakdown of deleted vs preserved items
- ✅ **Timestamp Tracking**: Records when and by whom the operation was performed

### When to Use Super Admin Endpoints

#### **Nuclear Clear All (`/nuclear-clear-all`)**

- 🔄 **Development Reset**: Complete fresh start for development environment
- 🧪 **Testing Setup**: Clean slate for integration tests
- 🚨 **Emergency Cleanup**: When database is corrupted or needs complete reset
- 📦 **Demo Preparation**: Reset everything for clean demo environment

#### **Global Content Clear (`/clear-all-organizations-data`)**

- 📊 **Data Migration**: Clear old content before importing new data
- 🔄 **Content Refresh**: Reset all content while keeping user accounts
- 🧪 **Multi-Org Testing**: Clear content across all orgs for testing
- 📈 **Performance Testing**: Clean content data while preserving user structure

### ⚠️ Critical Warnings

1. **IRREVERSIBLE**: These operations cannot be undone
2. **PRODUCTION DANGER**: Never use in production environments
3. **BACKUP FIRST**: Always backup database before using these endpoints
4. **SUPERUSER ONLY**: Regular users cannot access these endpoints
5. **AUDIT TRAIL**: All operations are logged with user and timestamp

## 🔐 Authentication & Authorization

### JWT Authentication

- Login endpoint: `POST /api/v1/auth/login`
- Token validation on protected endpoints
- User context available in all endpoints

### Permission System

- Role-based access control (RBAC)
- Permission strings like `"users:create"`, `"campaigns:read"`
- Organization-level isolation

## 🧪 Testing

### Run Tests

```bash
# Basic structure tests
uv run python test_main.py

# Full test suite (when implemented)
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html --cov-report=term
```

### Test Coverage

- ✅ **API endpoint tests** - All 67 endpoints tested
- ✅ **Service layer tests** - Business logic validation
- ✅ **CRUD operation tests** - Database operations
- ✅ **Authentication tests** - JWT and permissions
- ✅ **Model validation tests** - Pydantic schema validation
- ✅ **Integration tests** - End-to-end workflows

### Test Structure

```bash
tests/
├── test_api/           # API endpoint tests
├── test_services/      # Service layer tests
├── test_crud/          # CRUD operation tests
├── test_models/        # Model validation tests
└── conftest.py         # Test configuration
```

## 🔄 Migration from Old Structure

### What Changed

1. **Supabase Client** → **SQLAlchemy ORM**
2. **Mixed patterns** → **Consistent service layer**
3. **Route-level logic** → **Service classes**
4. **Manual auth** → **FastAPI dependencies**

### What Stayed

- Same database schema (PostgreSQL)
- Same API contracts (request/response)
- Same business logic (moved to services)
- Same security model (JWT + permissions)

## � Production Deployment

### Docker Deployment

```bash
# Build the Docker image
docker build -t rewards-api .

# Run with environment variables
docker run -p 8000:8000 \
  -e DATABASE_URL="postgresql+asyncpg://user:pass@host/db" \
  -e SECRET_KEY="your-secret-key" \
  rewards-api
```

### Environment Variables for Production

```env
# Production Database
DATABASE_URL=postgresql+asyncpg://user:pass@prod-host:5432/rewards_platform
DATABASE_ECHO=false

# Security (use strong keys in production)
SECRET_KEY=your-super-secure-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=60

# CORS (restrict to your domains)
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Optional: Sentry for error tracking
SENTRY_DSN=https://<EMAIL>/project
```

## �📈 Next Steps & Enhancements

1. ✅ **Complete endpoint migration** - All endpoints migrated to new structure
2. ✅ **Service layer implementation** - Business logic properly separated
3. 🔄 **Add comprehensive tests** - Unit and integration tests (in progress)
4. 🔄 **Add logging** - Structured logging throughout
5. 🔄 **Add monitoring** - Health checks and metrics
6. 🔄 **Add caching** - Redis for performance
7. 🔄 **Add background tasks** - Celery for async processing
8. 🔄 **Add rate limiting** - API rate limiting and throttling
9. 🔄 **Add API versioning** - Support for multiple API versions

## 🤝 Contributing

When adding new features:

1. **Models**: Add SQLAlchemy model in `app/models/`
2. **Schemas**: Add Pydantic schemas in `app/schemas/`
3. **CRUD**: Add database operations in `app/crud/`
4. **Services**: Add business logic in `app/services/`
5. **Endpoints**: Add API routes in `app/api/v1/endpoints/`
6. **Tests**: Add tests for all layers

This structure ensures maintainable, testable, and scalable code following FastAPI best practices.
