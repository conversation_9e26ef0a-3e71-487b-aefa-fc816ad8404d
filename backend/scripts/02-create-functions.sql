-- Utility functions and triggers for the rewards platform

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_users_updated_at BEFORE UPDATE ON customer_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_quests_updated_at BEFORE UPDATE ON quests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_badges_updated_at BEFORE UPDATE ON badges FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rewards_updated_at BEFORE UPDATE ON rewards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reward_redemptions_updated_at BEFORE UPDATE ON reward_redemptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhooks_updated_at BEFORE UPDATE ON webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update customer user points
CREATE OR REPLACE FUNCTION update_customer_points()
RETURNS TRIGGER AS $$
BEGIN
    -- Update total points for the customer user
    UPDATE customer_users 
    SET 
        total_points = total_points + NEW.amount,
        lifetime_points = CASE 
            WHEN NEW.amount > 0 THEN lifetime_points + NEW.amount 
            ELSE lifetime_points 
        END,
        updated_at = NOW()
    WHERE id = NEW.customer_user_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update points when transaction is created
CREATE TRIGGER update_points_on_transaction 
    AFTER INSERT ON points_transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_customer_points();

-- Function to create points transaction
CREATE OR REPLACE FUNCTION create_points_transaction(
    p_organization_id UUID,
    p_customer_user_id UUID,
    p_type VARCHAR(20),
    p_amount INTEGER,
    p_source_type VARCHAR(20) DEFAULT NULL,
    p_source_id UUID DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_created_by UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    current_balance INTEGER;
    transaction_id UUID;
BEGIN
    -- Get current balance
    SELECT total_points INTO current_balance 
    FROM customer_users 
    WHERE id = p_customer_user_id;
    
    -- Create transaction record
    INSERT INTO points_transactions (
        organization_id,
        customer_user_id,
        type,
        amount,
        balance_after,
        source_type,
        source_id,
        description,
        created_by
    ) VALUES (
        p_organization_id,
        p_customer_user_id,
        p_type,
        p_amount,
        current_balance + p_amount,
        p_source_type,
        p_source_id,
        p_description,
        p_created_by
    ) RETURNING id INTO transaction_id;
    
    RETURN transaction_id;
END;
$$ language 'plpgsql';

-- Function to check badge criteria and award badges
CREATE OR REPLACE FUNCTION check_and_award_badges(
    p_organization_id UUID,
    p_customer_user_id UUID
)
RETURNS TABLE(badge_id UUID, badge_name VARCHAR) AS $$
DECLARE
    badge_record RECORD;
    criteria JSONB;
    awarded_badge_id UUID;
BEGIN
    -- Loop through all active badges for the organization
    FOR badge_record IN 
        SELECT b.id, b.name, b.criteria, b.points_reward
        FROM badges b
        WHERE b.organization_id = p_organization_id 
        AND b.status = 'active'
        AND NOT EXISTS (
            SELECT 1 FROM badge_awards ba 
            WHERE ba.badge_id = b.id 
            AND ba.customer_user_id = p_customer_user_id
        )
    LOOP
        criteria := badge_record.criteria;
        
        -- Check if user meets badge criteria (simplified example)
        -- In practice, this would be more complex based on criteria type
        IF criteria->>'type' = 'points_threshold' THEN
            IF (SELECT total_points FROM customer_users WHERE id = p_customer_user_id) >= 
               (criteria->>'threshold')::INTEGER THEN
                
                -- Award the badge
                INSERT INTO badge_awards (
                    organization_id,
                    badge_id,
                    customer_user_id,
                    reason
                ) VALUES (
                    p_organization_id,
                    badge_record.id,
                    p_customer_user_id,
                    'Automatic award - criteria met'
                );
                
                -- Award points if specified
                IF badge_record.points_reward > 0 THEN
                    PERFORM create_points_transaction(
                        p_organization_id,
                        p_customer_user_id,
                        'earned',
                        badge_record.points_reward,
                        'badge',
                        badge_record.id,
                        'Points awarded for badge: ' || badge_record.name
                    );
                END IF;
                
                -- Return awarded badge info
                badge_id := badge_record.id;
                badge_name := badge_record.name;
                RETURN NEXT;
            END IF;
        END IF;
    END LOOP;
    
    RETURN;
END;
$$ language 'plpgsql';

-- Function to process quest completion
CREATE OR REPLACE FUNCTION process_quest_completion(
    p_organization_id UUID,
    p_quest_id UUID,
    p_customer_user_id UUID,
    p_completion_data JSONB DEFAULT '{}',
    p_auto_approve BOOLEAN DEFAULT false
)
RETURNS UUID AS $$
DECLARE
    quest_record RECORD;
    completion_id UUID;
    points_to_award INTEGER;
BEGIN
    -- Get quest details
    SELECT q.*, c.status as campaign_status
    INTO quest_record
    FROM quests q
    JOIN campaigns c ON q.campaign_id = c.id
    WHERE q.id = p_quest_id 
    AND q.organization_id = p_organization_id;
    
    -- Validate quest and campaign are active
    IF quest_record.status != 'active' OR quest_record.campaign_status != 'active' THEN
        RAISE EXCEPTION 'Quest or campaign is not active';
    END IF;
    
    -- Check if user already completed this quest (for non-repeatable quests)
    IF quest_record.frequency = 'once' AND EXISTS (
        SELECT 1 FROM quest_completions 
        WHERE quest_id = p_quest_id 
        AND customer_user_id = p_customer_user_id 
        AND status = 'approved'
    ) THEN
        RAISE EXCEPTION 'Quest already completed by user';
    END IF;
    
    -- Determine points to award
    points_to_award := CASE 
        WHEN p_auto_approve OR quest_record.validation_type = 'automatic' 
        THEN quest_record.points 
        ELSE 0 
    END;
    
    -- Create completion record
    INSERT INTO quest_completions (
        organization_id,
        quest_id,
        customer_user_id,
        status,
        points_awarded,
        completion_data,
        approved_at
    ) VALUES (
        p_organization_id,
        p_quest_id,
        p_customer_user_id,
        CASE 
            WHEN p_auto_approve OR quest_record.validation_type = 'automatic' 
            THEN 'approved' 
            ELSE 'pending' 
        END,
        points_to_award,
        p_completion_data,
        CASE 
            WHEN p_auto_approve OR quest_record.validation_type = 'automatic' 
            THEN NOW() 
            ELSE NULL 
        END
    ) RETURNING id INTO completion_id;
    
    -- Award points if auto-approved
    IF points_to_award > 0 THEN
        PERFORM create_points_transaction(
            p_organization_id,
            p_customer_user_id,
            'earned',
            points_to_award,
            'quest',
            p_quest_id,
            'Points earned for quest: ' || quest_record.title
        );
        
        -- Check for badge awards
        PERFORM check_and_award_badges(p_organization_id, p_customer_user_id);
    END IF;
    
    RETURN completion_id;
END;
$$ language 'plpgsql';

-- Function to process reward redemption
CREATE OR REPLACE FUNCTION redeem_reward(
    p_organization_id UUID,
    p_reward_id UUID,
    p_customer_user_id UUID
)
RETURNS UUID AS $$
DECLARE
    reward_record RECORD;
    user_points INTEGER;
    redemption_id UUID;
    expiry_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get reward details
    SELECT * INTO reward_record
    FROM rewards
    WHERE id = p_reward_id 
    AND organization_id = p_organization_id
    AND status = 'active';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Reward not found or not available';
    END IF;
    
    -- Check stock availability
    IF reward_record.available_quantity IS NOT NULL AND reward_record.available_quantity <= 0 THEN
        RAISE EXCEPTION 'Reward is out of stock';
    END IF;
    
    -- Get user points
    SELECT total_points INTO user_points
    FROM customer_users
    WHERE id = p_customer_user_id;
    
    -- Check if user has enough points
    IF user_points < reward_record.points_cost THEN
        RAISE EXCEPTION 'Insufficient points for redemption';
    END IF;
    
    -- Calculate expiry date
    expiry_date := CASE 
        WHEN reward_record.expiry_days IS NOT NULL 
        THEN NOW() + (reward_record.expiry_days || ' days')::INTERVAL
        ELSE NULL 
    END;
    
    -- Create redemption record
    INSERT INTO reward_redemptions (
        organization_id,
        reward_id,
        customer_user_id,
        points_spent,
        expires_at
    ) VALUES (
        p_organization_id,
        p_reward_id,
        p_customer_user_id,
        reward_record.points_cost,
        expiry_date
    ) RETURNING id INTO redemption_id;
    
    -- Deduct points
    PERFORM create_points_transaction(
        p_organization_id,
        p_customer_user_id,
        'spent',
        -reward_record.points_cost,
        'reward',
        p_reward_id,
        'Points spent on reward: ' || reward_record.name
    );
    
    -- Update stock if applicable
    IF reward_record.available_quantity IS NOT NULL THEN
        UPDATE rewards 
        SET available_quantity = available_quantity - 1,
            status = CASE 
                WHEN available_quantity - 1 <= 0 THEN 'out_of_stock'
                ELSE status 
            END
        WHERE id = p_reward_id;
    END IF;
    
    RETURN redemption_id;
END;
$$ language 'plpgsql';
