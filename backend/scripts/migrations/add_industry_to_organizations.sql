-- Migration: Add industry column to organizations table
-- Date: 2024-01-XX
-- Description: Add industry field to store organization industry information

-- Add industry column to organizations table
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS industry VARCHAR(100);

-- Add comment to document the column
COMMENT ON COLUMN organizations.industry IS 'Industry category for the organization (e.g., Technology, Healthcare, Finance)';

-- Create index for better query performance on industry filtering
CREATE INDEX IF NOT EXISTS idx_organizations_industry ON organizations(industry);

-- Update existing organizations with a default industry if needed (optional)
-- UPDATE organizations SET industry = 'Other' WHERE industry IS NULL;
