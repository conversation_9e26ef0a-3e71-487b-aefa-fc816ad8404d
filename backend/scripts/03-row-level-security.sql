-- Row Level Security (RLS) policies for multi-tenant security

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE quest_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE quest_completions ENABLE ROW LEVEL SECURITY;
ALTER TABLE badge_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE badge_awards ENABLE ROW LEVEL SECURITY;
ALTER TABLE reward_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE reward_redemptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's organization
CREATE OR REPLACE FUNCTION get_current_user_org_id()
RETURNS UUID AS $$
BEGIN
    RETURN (current_setting('app.current_organization_id', true))::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if current user has permission
CREATE OR REPLACE FUNCTION current_user_has_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_permissions JSONB;
BEGIN
    SELECT r.permissions INTO user_permissions
    FROM users u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = (current_setting('app.current_user_id', true))::UUID;
    
    RETURN user_permissions ? permission_name;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations policies
CREATE POLICY "Users can view their own organization" ON organizations
    FOR SELECT USING (id = get_current_user_org_id());

CREATE POLICY "Owners can update their organization" ON organizations
    FOR UPDATE USING (
        id = get_current_user_org_id() AND 
        current_user_has_permission('settings.company')
    );

-- Roles policies
CREATE POLICY "Users can view roles in their organization" ON roles
    FOR SELECT USING (organization_id = get_current_user_org_id());

CREATE POLICY "Admins can manage roles" ON roles
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('team.edit')
    );

-- Users policies
CREATE POLICY "Users can view team members in their organization" ON users
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('team.view')
    );

CREATE POLICY "Admins can manage team members" ON users
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('team.edit')
    );

-- Customer users policies
CREATE POLICY "Users can view customer users in their organization" ON customer_users
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('users.view')
    );

CREATE POLICY "Users can manage customer users with permission" ON customer_users
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('users.edit')
    );

-- Campaigns policies
CREATE POLICY "Users can view campaigns in their organization" ON campaigns
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('campaigns.view')
    );

CREATE POLICY "Users can manage campaigns with permission" ON campaigns
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('campaigns.create')
    );

CREATE POLICY "Users can update campaigns with permission" ON campaigns
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('campaigns.edit')
    );

CREATE POLICY "Users can delete campaigns with permission" ON campaigns
    FOR DELETE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('campaigns.delete')
    );

-- Quest categories policies
CREATE POLICY "Users can view quest categories in their organization" ON quest_categories
    FOR SELECT USING (organization_id = get_current_user_org_id());

CREATE POLICY "Users can manage quest categories with permission" ON quest_categories
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.edit')
    );

-- Quests policies
CREATE POLICY "Users can view quests in their organization" ON quests
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.view')
    );

CREATE POLICY "Users can manage quests with permission" ON quests
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.create')
    );

CREATE POLICY "Users can update quests with permission" ON quests
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.edit')
    );

CREATE POLICY "Users can delete quests with permission" ON quests
    FOR DELETE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.delete')
    );

-- Quest completions policies
CREATE POLICY "Users can view quest completions in their organization" ON quest_completions
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.view')
    );

CREATE POLICY "Users can approve quest completions with permission" ON quest_completions
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('quests.approve')
    );

-- Badge categories policies
CREATE POLICY "Users can view badge categories in their organization" ON badge_categories
    FOR SELECT USING (organization_id = get_current_user_org_id());

CREATE POLICY "Users can manage badge categories with permission" ON badge_categories
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.edit')
    );

-- Badges policies
CREATE POLICY "Users can view badges in their organization" ON badges
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.view')
    );

CREATE POLICY "Users can manage badges with permission" ON badges
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.create')
    );

CREATE POLICY "Users can update badges with permission" ON badges
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.edit')
    );

CREATE POLICY "Users can delete badges with permission" ON badges
    FOR DELETE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.delete')
    );

-- Badge awards policies
CREATE POLICY "Users can view badge awards in their organization" ON badge_awards
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.view')
    );

CREATE POLICY "Users can award badges with permission" ON badge_awards
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('badges.award')
    );

-- Reward categories policies
CREATE POLICY "Users can view reward categories in their organization" ON reward_categories
    FOR SELECT USING (organization_id = get_current_user_org_id());

CREATE POLICY "Users can manage reward categories with permission" ON reward_categories
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.edit')
    );

-- Rewards policies
CREATE POLICY "Users can view rewards in their organization" ON rewards
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.view')
    );

CREATE POLICY "Users can manage rewards with permission" ON rewards
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.create')
    );

CREATE POLICY "Users can update rewards with permission" ON rewards
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.edit')
    );

CREATE POLICY "Users can delete rewards with permission" ON rewards
    FOR DELETE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.delete')
    );

-- Reward redemptions policies
CREATE POLICY "Users can view reward redemptions in their organization" ON reward_redemptions
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.view')
    );

CREATE POLICY "Users can fulfill redemptions with permission" ON reward_redemptions
    FOR UPDATE USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('rewards.fulfill')
    );

-- Points transactions policies
CREATE POLICY "Users can view points transactions in their organization" ON points_transactions
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('users.view')
    );

CREATE POLICY "Users can create points transactions with permission" ON points_transactions
    FOR INSERT WITH CHECK (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('users.edit')
    );

-- API keys policies
CREATE POLICY "Users can view API keys in their organization" ON api_keys
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('settings.integrations')
    );

CREATE POLICY "Users can manage API keys with permission" ON api_keys
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('settings.integrations')
    );

-- Webhooks policies
CREATE POLICY "Users can view webhooks in their organization" ON webhooks
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('settings.integrations')
    );

CREATE POLICY "Users can manage webhooks with permission" ON webhooks
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('settings.integrations')
    );

-- Webhook deliveries policies
CREATE POLICY "Users can view webhook deliveries for their organization" ON webhook_deliveries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM webhooks w 
            WHERE w.id = webhook_deliveries.webhook_id 
            AND w.organization_id = get_current_user_org_id()
            AND current_user_has_permission('settings.integrations')
        )
    );

-- Audit logs policies
CREATE POLICY "Users can view audit logs in their organization" ON audit_logs
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('system.audit')
    );

-- Notifications policies
CREATE POLICY "Users can view notifications in their organization" ON notifications
    FOR SELECT USING (
        organization_id = get_current_user_org_id() AND
        (user_id = (current_setting('app.current_user_id', true))::UUID OR
         current_user_has_permission('users.view'))
    );

CREATE POLICY "Users can manage notifications with permission" ON notifications
    FOR ALL USING (
        organization_id = get_current_user_org_id() AND
        current_user_has_permission('users.edit')
    );
