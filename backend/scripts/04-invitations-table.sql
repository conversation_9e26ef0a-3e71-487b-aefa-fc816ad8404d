-- Create invitations table for user invitation system
-- This script should be run after the main database setup

-- Create invitations table
CREATE TABLE IF NOT EXISTS invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR NOT NULL,
    first_name <PERSON><PERSON><PERSON>R NOT NULL,
    last_name VA<PERSON>HAR NOT NULL,
    
    -- Organization and role references
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE SET NULL,
    
    -- Invitation details
    token VARCHAR UNIQUE NOT NULL,
    invited_by_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Status and timing
    is_accepted BOOLEAN DEFAULT FALSE NOT NULL,
    is_expired BOOLEAN DEFAULT FALSE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    invitation_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invitations_email ON invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_organization_id ON invitations(organization_id);
CREATE INDEX IF NOT EXISTS idx_invitations_invited_by_id ON invitations(invited_by_id);
CREATE INDEX IF NOT EXISTS idx_invitations_status ON invitations(is_accepted, is_expired);
CREATE INDEX IF NOT EXISTS idx_invitations_expires_at ON invitations(expires_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_invitations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_invitations_updated_at
    BEFORE UPDATE ON invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_invitations_updated_at();

-- Add RLS (Row Level Security) policies
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see invitations from their organization
CREATE POLICY invitations_organization_isolation ON invitations
    FOR ALL
    USING (organization_id = (current_setting('app.current_user_organization_id', true))::UUID);

-- Policy: Allow service role to bypass RLS for system operations
CREATE POLICY invitations_service_role_bypass ON invitations
    FOR ALL
    TO service_role
    USING (true);

-- Grant permissions
GRANT ALL ON invitations TO authenticated;
GRANT ALL ON invitations TO service_role;

-- Add comments for documentation
COMMENT ON TABLE invitations IS 'User invitations for organization access';
COMMENT ON COLUMN invitations.token IS 'Unique token for invitation acceptance';
COMMENT ON COLUMN invitations.expires_at IS 'When the invitation expires';
COMMENT ON COLUMN invitations.is_accepted IS 'Whether the invitation has been accepted';
COMMENT ON COLUMN invitations.is_expired IS 'Whether the invitation has been manually expired';
COMMENT ON COLUMN invitations.invitation_message IS 'Optional message from the inviter';
