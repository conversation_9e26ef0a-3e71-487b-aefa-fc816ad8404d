-- Views and analytics queries for reporting and dashboards

-- Campaign performance view
CREATE OR REPLACE VIEW campaign_performance AS
SELECT 
    c.id,
    c.name,
    c.status,
    c.start_date,
    c.end_date,
    COUNT(DISTINCT q.id) as total_quests,
    COUNT(DISTINCT qc.customer_user_id) as unique_participants,
    COUNT(qc.id) as total_completions,
    SUM(CASE WHEN qc.status = 'approved' THEN qc.points_awarded ELSE 0 END) as total_points_awarded,
    AVG(CASE WHEN qc.status = 'approved' THEN qc.points_awarded ELSE NULL END) as avg_points_per_completion,
    COUNT(CASE WHEN qc.status = 'pending' THEN 1 END) as pending_approvals
FROM campaigns c
LEFT JOIN quests q ON c.id = q.campaign_id
LEFT JOIN quest_completions qc ON q.id = qc.quest_id
GROUP BY c.id, c.name, c.status, c.start_date, c.end_date;

-- User engagement metrics view
CREATE OR REPLACE VIEW user_engagement_metrics AS
SELECT 
    cu.id,
    cu.external_id,
    cu.name,
    cu.email,
    cu.total_points,
    cu.lifetime_points,
    cu.status,
    cu.first_seen,
    cu.last_active,
    COUNT(DISTINCT qc.id) as total_quest_completions,
    COUNT(DISTINCT ba.id) as total_badges_earned,
    COUNT(DISTINCT rr.id) as total_rewards_redeemed,
    SUM(CASE WHEN qc.status = 'approved' THEN 1 ELSE 0 END) as approved_completions,
    SUM(CASE WHEN qc.status = 'pending' THEN 1 ELSE 0 END) as pending_completions,
    MAX(qc.completed_at) as last_quest_completion,
    MAX(ba.awarded_at) as last_badge_earned,
    MAX(rr.redeemed_at) as last_reward_redeemed,
    EXTRACT(DAYS FROM (NOW() - cu.last_active)) as days_since_last_active
FROM customer_users cu
LEFT JOIN quest_completions qc ON cu.id = qc.customer_user_id
LEFT JOIN badge_awards ba ON cu.id = ba.customer_user_id
LEFT JOIN reward_redemptions rr ON cu.id = rr.customer_user_id
GROUP BY cu.id, cu.external_id, cu.name, cu.email, cu.total_points, cu.lifetime_points, cu.status, cu.first_seen, cu.last_active;

-- Quest performance view
CREATE OR REPLACE VIEW quest_performance AS
SELECT 
    q.id,
    q.title,
    q.description,
    q.points,
    q.frequency,
    q.validation_type,
    q.status,
    qc_cat.name as category_name,
    c.name as campaign_name,
    COUNT(qc.id) as total_attempts,
    COUNT(CASE WHEN qc.status = 'approved' THEN 1 END) as approved_completions,
    COUNT(CASE WHEN qc.status = 'pending' THEN 1 END) as pending_completions,
    COUNT(CASE WHEN qc.status = 'rejected' THEN 1 END) as rejected_completions,
    COUNT(DISTINCT qc.customer_user_id) as unique_participants,
    ROUND(
        COUNT(CASE WHEN qc.status = 'approved' THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(qc.id), 0) * 100, 2
    ) as approval_rate,
    SUM(CASE WHEN qc.status = 'approved' THEN qc.points_awarded ELSE 0 END) as total_points_awarded,
    AVG(CASE WHEN qc.status = 'approved' THEN qc.points_awarded ELSE NULL END) as avg_points_awarded
FROM quests q
LEFT JOIN quest_completions qc ON q.id = qc.quest_id
LEFT JOIN quest_categories qc_cat ON q.category_id = qc_cat.id
LEFT JOIN campaigns c ON q.campaign_id = c.id
GROUP BY q.id, q.title, q.description, q.points, q.frequency, q.validation_type, q.status, qc_cat.name, c.name;

-- Badge distribution view
CREATE OR REPLACE VIEW badge_distribution AS
SELECT 
    b.id,
    b.name,
    b.tier,
    b.rarity,
    bc.name as category_name,
    COUNT(ba.id) as total_awards,
    COUNT(DISTINCT ba.customer_user_id) as unique_recipients,
    b.points_reward,
    SUM(b.points_reward) as total_points_awarded,
    MIN(ba.awarded_at) as first_awarded,
    MAX(ba.awarded_at) as last_awarded
FROM badges b
LEFT JOIN badge_awards ba ON b.id = ba.badge_id
LEFT JOIN badge_categories bc ON b.category_id = bc.id
GROUP BY b.id, b.name, b.tier, b.rarity, bc.name, b.points_reward;

-- Reward popularity view
CREATE OR REPLACE VIEW reward_popularity AS
SELECT 
    r.id,
    r.name,
    r.type,
    r.points_cost,
    r.stock_quantity,
    r.available_quantity,
    rc.name as category_name,
    COUNT(rr.id) as total_redemptions,
    COUNT(DISTINCT rr.customer_user_id) as unique_redeemers,
    COUNT(CASE WHEN rr.status = 'fulfilled' THEN 1 END) as fulfilled_redemptions,
    COUNT(CASE WHEN rr.status = 'pending' THEN 1 END) as pending_redemptions,
    SUM(rr.points_spent) as total_points_spent,
    AVG(rr.points_spent) as avg_points_spent,
    ROUND(
        COUNT(CASE WHEN rr.status = 'fulfilled' THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(rr.id), 0) * 100, 2
    ) as fulfillment_rate
FROM rewards r
LEFT JOIN reward_redemptions rr ON r.id = rr.reward_id
LEFT JOIN reward_categories rc ON r.category_id = rc.id
GROUP BY r.id, r.name, r.type, r.points_cost, r.stock_quantity, r.available_quantity, rc.name;

-- Daily activity summary view
CREATE OR REPLACE VIEW daily_activity_summary AS
SELECT 
    DATE(created_at) as activity_date,
    'quest_completion' as activity_type,
    COUNT(*) as activity_count,
    COUNT(DISTINCT customer_user_id) as unique_users,
    SUM(points_awarded) as total_points
FROM quest_completions
WHERE status = 'approved'
GROUP BY DATE(created_at)

UNION ALL

SELECT 
    DATE(awarded_at) as activity_date,
    'badge_award' as activity_type,
    COUNT(*) as activity_count,
    COUNT(DISTINCT customer_user_id) as unique_users,
    0 as total_points
FROM badge_awards
GROUP BY DATE(awarded_at)

UNION ALL

SELECT 
    DATE(redeemed_at) as activity_date,
    'reward_redemption' as activity_type,
    COUNT(*) as activity_count,
    COUNT(DISTINCT customer_user_id) as unique_users,
    -SUM(points_spent) as total_points
FROM reward_redemptions
GROUP BY DATE(redeemed_at)

ORDER BY activity_date DESC, activity_type;

-- Organization dashboard summary view
CREATE OR REPLACE VIEW organization_dashboard_summary AS
SELECT 
    o.id as organization_id,
    o.name as organization_name,
    
    -- User metrics
    COUNT(DISTINCT cu.id) as total_customer_users,
    COUNT(DISTINCT CASE WHEN cu.status = 'active' THEN cu.id END) as active_customer_users,
    COUNT(DISTINCT CASE WHEN cu.last_active >= NOW() - INTERVAL '7 days' THEN cu.id END) as weekly_active_users,
    COUNT(DISTINCT CASE WHEN cu.last_active >= NOW() - INTERVAL '30 days' THEN cu.id END) as monthly_active_users,
    
    -- Campaign metrics
    COUNT(DISTINCT c.id) as total_campaigns,
    COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_campaigns,
    
    -- Quest metrics
    COUNT(DISTINCT q.id) as total_quests,
    COUNT(DISTINCT CASE WHEN q.status = 'active' THEN q.id END) as active_quests,
    COUNT(DISTINCT qc.id) as total_quest_completions,
    COUNT(DISTINCT CASE WHEN qc.status = 'pending' THEN qc.id END) as pending_quest_approvals,
    
    -- Badge metrics
    COUNT(DISTINCT b.id) as total_badges,
    COUNT(DISTINCT ba.id) as total_badge_awards,
    
    -- Reward metrics
    COUNT(DISTINCT r.id) as total_rewards,
    COUNT(DISTINCT rr.id) as total_reward_redemptions,
    COUNT(DISTINCT CASE WHEN rr.status = 'pending' THEN rr.id END) as pending_reward_fulfillments,
    
    -- Points metrics
    COALESCE(SUM(cu.total_points), 0) as total_points_in_circulation,
    COALESCE(SUM(cu.lifetime_points), 0) as total_lifetime_points_earned,
    COALESCE(SUM(CASE WHEN pt.type = 'earned' THEN pt.amount ELSE 0 END), 0) as total_points_earned,
    COALESCE(SUM(CASE WHEN pt.type = 'spent' THEN ABS(pt.amount) ELSE 0 END), 0) as total_points_spent
    
FROM organizations o
LEFT JOIN customer_users cu ON o.id = cu.organization_id
LEFT JOIN campaigns c ON o.id = c.organization_id
LEFT JOIN quests q ON o.id = q.organization_id
LEFT JOIN quest_completions qc ON o.id = qc.organization_id
LEFT JOIN badges b ON o.id = b.organization_id
LEFT JOIN badge_awards ba ON o.id = ba.organization_id
LEFT JOIN rewards r ON o.id = r.organization_id
LEFT JOIN reward_redemptions rr ON o.id = rr.organization_id
LEFT JOIN points_transactions pt ON o.id = pt.organization_id
GROUP BY o.id, o.name;

-- Leaderboard view
CREATE OR REPLACE VIEW user_leaderboard AS
SELECT 
    cu.id,
    cu.external_id,
    cu.name,
    cu.email,
    cu.total_points,
    cu.lifetime_points,
    COUNT(DISTINCT qc.id) as quest_completions,
    COUNT(DISTINCT ba.id) as badges_earned,
    COUNT(DISTINCT rr.id) as rewards_redeemed,
    RANK() OVER (PARTITION BY cu.organization_id ORDER BY cu.total_points DESC) as points_rank,
    RANK() OVER (PARTITION BY cu.organization_id ORDER BY COUNT(DISTINCT qc.id) DESC) as completions_rank,
    RANK() OVER (PARTITION BY cu.organization_id ORDER BY COUNT(DISTINCT ba.id) DESC) as badges_rank
FROM customer_users cu
LEFT JOIN quest_completions qc ON cu.id = qc.customer_user_id AND qc.status = 'approved'
LEFT JOIN badge_awards ba ON cu.id = ba.customer_user_id
LEFT JOIN reward_redemptions rr ON cu.id = rr.customer_user_id
WHERE cu.status = 'active'
GROUP BY cu.id, cu.external_id, cu.name, cu.email, cu.total_points, cu.lifetime_points, cu.organization_id;

-- API usage analytics view
CREATE OR REPLACE VIEW api_usage_analytics AS
SELECT 
    ak.id,
    ak.name,
    ak.key_prefix,
    ak.environment,
    ak.status,
    ak.last_used,
    ak.created_at,
    EXTRACT(DAYS FROM (NOW() - ak.last_used)) as days_since_last_use,
    CASE 
        WHEN ak.last_used IS NULL THEN 'Never Used'
        WHEN ak.last_used >= NOW() - INTERVAL '1 day' THEN 'Active'
        WHEN ak.last_used >= NOW() - INTERVAL '7 days' THEN 'Recent'
        WHEN ak.last_used >= NOW() - INTERVAL '30 days' THEN 'Inactive'
        ELSE 'Stale'
    END as usage_status
FROM api_keys ak;

-- Webhook delivery success rate view
CREATE OR REPLACE VIEW webhook_delivery_analytics AS
SELECT 
    w.id,
    w.name,
    w.url,
    w.status,
    w.failure_count,
    COUNT(wd.id) as total_deliveries,
    COUNT(CASE WHEN wd.response_status BETWEEN 200 AND 299 THEN 1 END) as successful_deliveries,
    COUNT(CASE WHEN wd.response_status NOT BETWEEN 200 AND 299 OR wd.response_status IS NULL THEN 1 END) as failed_deliveries,
    ROUND(
        COUNT(CASE WHEN wd.response_status BETWEEN 200 AND 299 THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(wd.id), 0) * 100, 2
    ) as success_rate,
    MAX(wd.delivered_at) as last_successful_delivery,
    AVG(wd.delivery_attempts) as avg_delivery_attempts
FROM webhooks w
LEFT JOIN webhook_deliveries wd ON w.id = wd.webhook_id
GROUP BY w.id, w.name, w.url, w.status, w.failure_count;
