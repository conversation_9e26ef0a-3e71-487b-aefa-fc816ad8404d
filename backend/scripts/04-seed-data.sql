-- Seed data for development and testing

-- Insert sample organization
INSERT INTO organizations (id, name, slug, description, website, primary_color, subscription_plan)
VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'Acme Corporation',
    'acme-corp',
    'Leading provider of innovative solutions',
    'https://acme-corp.com',
    '#3b82f6',
    'enterprise'
);

-- Insert system roles for the organization
INSERT INTO roles (id, organization_id, name, description, level, permissions, is_system, color) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    'Owner',
    'Full access to all features and settings',
    100,
    '["dashboard.view", "dashboard.analytics", "campaigns.view", "campaigns.create", "campaigns.edit", "campaigns.delete", "campaigns.publish", "quests.view", "quests.create", "quests.edit", "quests.delete", "quests.approve", "badges.view", "badges.create", "badges.edit", "badges.delete", "badges.award", "rewards.view", "rewards.create", "rewards.edit", "rewards.delete", "rewards.fulfill", "users.view", "users.edit", "users.delete", "users.impersonate", "team.view", "team.invite", "team.edit", "team.remove", "settings.view", "settings.company", "settings.branding", "settings.integrations", "settings.billing", "settings.security", "analytics.view", "analytics.export", "analytics.advanced", "system.audit", "system.backup", "system.maintenance"]',
    true,
    '#dc2626'
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440000',
    'Administrator',
    'Full access to platform features',
    90,
    '["dashboard.view", "dashboard.analytics", "campaigns.view", "campaigns.create", "campaigns.edit", "campaigns.delete", "campaigns.publish", "quests.view", "quests.create", "quests.edit", "quests.delete", "quests.approve", "badges.view", "badges.create", "badges.edit", "badges.delete", "badges.award", "rewards.view", "rewards.create", "rewards.edit", "rewards.delete", "rewards.fulfill", "users.view", "users.edit", "users.delete", "team.view", "team.invite", "team.edit", "team.remove", "settings.view", "settings.company", "settings.branding", "settings.integrations", "settings.security", "analytics.view", "analytics.export", "analytics.advanced", "system.audit"]',
    true,
    '#7c3aed'
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440000',
    'Manager',
    'Can manage campaigns, quests, badges, and rewards',
    70,
    '["dashboard.view", "dashboard.analytics", "campaigns.view", "campaigns.create", "campaigns.edit", "campaigns.publish", "quests.view", "quests.create", "quests.edit", "quests.approve", "badges.view", "badges.create", "badges.edit", "badges.award", "rewards.view", "rewards.create", "rewards.edit", "rewards.fulfill", "users.view", "users.edit", "team.view", "analytics.view", "analytics.export", "settings.view"]',
    true,
    '#059669'
);

-- Insert sample users
INSERT INTO users (id, organization_id, role_id, email, name, status, joined_at) VALUES
(
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440001',
    '<EMAIL>',
    'John Owner',
    'active',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440011',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440002',
    '<EMAIL>',
    'Jane Admin',
    'active',
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440012',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440003',
    '<EMAIL>',
    'Bob Manager',
    'active',
    NOW()
);

-- Insert quest categories
INSERT INTO quest_categories (id, organization_id, name, description, icon, color) VALUES
(
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-446655440000',
    'Engagement',
    'User engagement activities',
    'heart',
    '#ef4444'
),
(
    '550e8400-e29b-41d4-a716-446655440021',
    '550e8400-e29b-41d4-a716-446655440000',
    'Learning',
    'Educational and training quests',
    'book-open',
    '#3b82f6'
),
(
    '550e8400-e29b-41d4-a716-446655440022',
    '550e8400-e29b-41d4-a716-446655440000',
    'Social',
    'Community and social activities',
    'users',
    '#10b981'
);

-- Insert badge categories
INSERT INTO badge_categories (id, organization_id, name, description) VALUES
(
    '550e8400-e29b-41d4-a716-446655440030',
    '550e8400-e29b-41d4-a716-446655440000',
    'Achievement',
    'General achievement badges'
),
(
    '550e8400-e29b-41d4-a716-446655440031',
    '550e8400-e29b-41d4-a716-446655440000',
    'Milestone',
    'Milestone and progress badges'
),
(
    '550e8400-e29b-41d4-a716-446655440032',
    '550e8400-e29b-41d4-a716-446655440000',
    'Special',
    'Special event and limited badges'
);

-- Insert reward categories
INSERT INTO reward_categories (id, organization_id, name, description) VALUES
(
    '550e8400-e29b-41d4-a716-446655440040',
    '550e8400-e29b-41d4-a716-446655440000',
    'Digital',
    'Digital rewards and content'
),
(
    '550e8400-e29b-41d4-a716-446655440041',
    '550e8400-e29b-41d4-a716-446655440000',
    'Physical',
    'Physical products and merchandise'
),
(
    '550e8400-e29b-41d4-a716-446655440042',
    '550e8400-e29b-41d4-a716-446655440000',
    'Experience',
    'Experiences and services'
);

-- Insert sample campaign
INSERT INTO campaigns (id, organization_id, name, description, status, start_date, end_date, created_by) VALUES
(
    '550e8400-e29b-41d4-a716-446655440050',
    '550e8400-e29b-41d4-a716-446655440000',
    'Welcome Campaign',
    'Onboarding campaign for new users',
    'active',
    NOW() - INTERVAL '7 days',
    NOW() + INTERVAL '30 days',
    '550e8400-e29b-41d4-a716-446655440010'
);

-- Insert sample quests
INSERT INTO quests (id, organization_id, campaign_id, category_id, title, description, points, frequency, validation_type, created_by) VALUES
(
    '550e8400-e29b-41d4-a716-446655440060',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440050',
    '550e8400-e29b-41d4-a716-446655440020',
    'Complete Profile',
    'Fill out your complete user profile',
    100,
    'once',
    'automatic',
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440061',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440050',
    '550e8400-e29b-41d4-a716-446655440020',
    'Daily Login',
    'Log in to the platform daily',
    50,
    'daily',
    'automatic',
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440062',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440050',
    '550e8400-e29b-41d4-a716-446655440022',
    'Refer a Friend',
    'Invite a friend to join the platform',
    500,
    'once',
    'manual',
    '550e8400-e29b-41d4-a716-446655440010'
);

-- Insert sample badges
INSERT INTO badges (id, organization_id, category_id, name, description, tier, criteria, points_reward, created_by) VALUES
(
    '550e8400-e29b-41d4-a716-446655440070',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440030',
    'First Steps',
    'Awarded for completing your first quest',
    'bronze',
    '{"type": "quest_count", "threshold": 1}',
    50,
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440071',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440031',
    'Point Collector',
    'Awarded for earning 1000 points',
    'silver',
    '{"type": "points_threshold", "threshold": 1000}',
    100,
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440072',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440031',
    'High Achiever',
    'Awarded for earning 10000 points',
    'gold',
    '{"type": "points_threshold", "threshold": 10000}',
    500,
    '550e8400-e29b-41d4-a716-446655440010'
);

-- Insert sample rewards
INSERT INTO rewards (id, organization_id, category_id, name, description, type, points_cost, stock_quantity, available_quantity, created_by) VALUES
(
    '550e8400-e29b-41d4-a716-446655440080',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440040',
    '10% Discount Code',
    'Get 10% off your next purchase',
    'discount',
    500,
    NULL,
    NULL,
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440081',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440041',
    'Company T-Shirt',
    'Branded company t-shirt',
    'physical',
    2000,
    100,
    100,
    '550e8400-e29b-41d4-a716-446655440010'
),
(
    '550e8400-e29b-41d4-a716-446655440082',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440042',
    'Coffee Chat with CEO',
    '30-minute coffee chat with the CEO',
    'experience',
    5000,
    5,
    5,
    '550e8400-e29b-41d4-a716-446655440010'
);

-- Insert sample customer users
INSERT INTO customer_users (id, organization_id, external_id, email, name, total_points, lifetime_points) VALUES
(
    '550e8400-e29b-41d4-a716-446655440090',
    '550e8400-e29b-41d4-a716-446655440000',
    'user_001',
    '<EMAIL>',
    'Alice Johnson',
    1250,
    1250
),
(
    '550e8400-e29b-41d4-a716-446655440091',
    '550e8400-e29b-41d4-a716-446655440000',
    'user_002',
    '<EMAIL>',
    'Bob Smith',
    750,
    750
),
(
    '550e8400-e29b-41d4-a716-446655440092',
    '550e8400-e29b-41d4-a716-446655440000',
    'user_003',
    '<EMAIL>',
    'Charlie Brown',
    2100,
    2100
);

-- Insert sample quest completions
INSERT INTO quest_completions (organization_id, quest_id, customer_user_id, status, points_awarded, approved_at) VALUES
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440060',
    '550e8400-e29b-41d4-a716-446655440090',
    'approved',
    100,
    NOW() - INTERVAL '5 days'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440061',
    '550e8400-e29b-41d4-a716-446655440090',
    'approved',
    50,
    NOW() - INTERVAL '1 day'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440060',
    '550e8400-e29b-41d4-a716-446655440091',
    'approved',
    100,
    NOW() - INTERVAL '3 days'
);

-- Insert sample badge awards
INSERT INTO badge_awards (organization_id, badge_id, customer_user_id, reason) VALUES
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440070',
    '550e8400-e29b-41d4-a716-446655440090',
    'Automatic award - first quest completed'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440071',
    '550e8400-e29b-41d4-a716-446655440090',
    'Automatic award - 1000 points threshold reached'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440070',
    '550e8400-e29b-41d4-a716-446655440091',
    'Automatic award - first quest completed'
);

-- Insert sample points transactions
INSERT INTO points_transactions (organization_id, customer_user_id, type, amount, balance_after, source_type, source_id, description) VALUES
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440090',
    'earned',
    100,
    100,
    'quest',
    '550e8400-e29b-41d4-a716-446655440060',
    'Points earned for quest: Complete Profile'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440090',
    'earned',
    50,
    150,
    'badge',
    '550e8400-e29b-41d4-a716-446655440070',
    'Points awarded for badge: First Steps'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440090',
    'earned',
    50,
    200,
    'quest',
    '550e8400-e29b-41d4-a716-446655440061',
    'Points earned for quest: Daily Login'
);
