version: '3.8'

services:
  # Frontend - Next.js Admin App
  frontend:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    volumes:
      - ./frontend/admin:/app
      - /app/node_modules
    depends_on:
      - backend

  # Backend - FastAPI
  backend:
    build:
      context: ./backend/api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/rewards_platform
      - SECRET_KEY=your-super-secret-key-here
      - ALLOWED_ORIGINS=http://localhost:3000
    volumes:
      - ./backend/api:/app
    depends_on:
      - db

  # Database - PostgreSQL
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=rewards_platform
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
